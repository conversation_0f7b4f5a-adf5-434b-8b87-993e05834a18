﻿@using HlktechPower.Entity

<head>
    <!-- 第三方css -->
    <link rel="stylesheet" href="~/modules/layui/css/layui.css">
    <!-- 基础css -->
    <link rel="stylesheet" href="~/css/public.css">
    <link rel="stylesheet" href="~/css/mySidebar.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="~/css/pageCss/companyIng.css">
    <!-- 所有页面组成部分 -->
    <script src="~/components/mySidebar.js" defer></script>
</head>

<body>
    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="/">@T("首页")</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="#">@T("技术应用")</div>
        </div>
        <div class="myContent">
            <!-- 侧边栏 -->
            <div class="mySidebar"></div>
            <div class="myContent-right">
                <div class="search">
                    <div class="h3">@T("技术应用")</div>
                    <div class="layui-input-group">
                        <input type="text" placeholder="" class="layui-input" value="@Model.key" id="searchKey">
                        <div class="layui-input-split layui-input-suffix" id="sousuo">
                            <i class="iconfont icon-search"></i>&nbsp;<span class="wen">@T("搜索")</span>
                        </div>
                    </div>
                </div>
                <div class="companyIng">
                    @foreach (Article item in Model.list)
                    {
                        <div class="company-item">
                            <div class="item-image">
                                <img src="@DHSetting.Current.CurDomainUrl/@item.Pic" alt="News Image">
                            </div>
                            <div class="item-content">
                                <div class="item-title">@item.Name</div>
                                <div class="item-description">@item.Summary</div>
                                <div class="item-date">@T("发布时间"): @item.CreateTime</div>
                                <a href="#" class="item-button">@T("查看详情")</a>
                            </div>
                        </div>
                    }
                    <!-- 分页 -->
                    <div id="demo-laypage-all"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="bug"></div>
    <script>
        const data = [
            { text: '@T("NewsDynamics")', link: '#' },  // 标题
            { text: '@T("企业动态")', link: '@Url.Action("Index")' },
            { text: '@T("产品动态")', link: '@Url.Action("ProductDynamics")' },
            { text: '@T("技术应用")', link: '@Url.Action("TechnologyApp")' },
        ];
        // 初始化分页器
        layui.use(['laypage'], function () {
            var laypage = layui.laypage;
            // 完整显示
            laypage.render({
                elem: 'demo-laypage-all', // 元素 id
                count: '@Model.total', // 数据总数
                curr: '@Model.page',
                limit: '@Model.limit',
                prev: '@T("上一页")',
                next: '@T("下一页")',
                countText: ['@T("共") ', ' @T("条")'],
                skipText: ['@T("到第")', '@T("页")', '@T("确认")'],
                limitTemplet: function (item) {
                    return item + ' @T("条/页")';
                },
                layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'], // 功能布局
                jump: function (obj, first) {
                    if (!first) {
                        // 获取搜索关键字
                        var key = document.getElementById('searchKey').value;
                        // 跳转到对应页
                        window.location.href = '@Url.Action("TechnologyApp")?page=' + obj.curr + '&limit=' + obj.limit + '&key=' + key;
                    }
                }
            });
        });
    </script>
</body>
<script asp-location="Footer">
    $("#sousuo").click(function () {
        var key = $("#searchKey").val();
        window.location.href = '@Url.Action("TechnologyApp")?key=' + key;
    });
</script>