﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechPower.Entity;

/// <summary>帮助翻译表</summary>
[Serializable]
[DataObject]
[Description("帮助翻译表")]
[BindIndex("IU_DH_HelpsLan_HId_LId", true, "HId,LId")]
[BindTable("DH_HelpsLan", Description = "帮助翻译表", ConnName = "Pek", DbType = DatabaseType.None)]
public partial class HelpsLan : IHelpsLan, IEntity<IHelpsLan>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _HId;
    /// <summary>帮助文章Id</summary>
    [DisplayName("帮助文章Id")]
    [Description("帮助文章Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("HId", "帮助文章Id", "")]
    public Int32 HId { get => _HId; set { if (OnPropertyChanging("HId", value)) { _HId = value; OnPropertyChanged("HId"); } } }

    private Int32 _LId;
    /// <summary>所属语言Id</summary>
    [DisplayName("所属语言Id")]
    [Description("所属语言Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LId", "所属语言Id", "")]
    public Int32 LId { get => _LId; set { if (OnPropertyChanging("LId", value)) { _LId = value; OnPropertyChanged("LId"); } } }

    private String? _Name;
    /// <summary>帮助文章标题</summary>
    [DisplayName("帮助文章标题")]
    [Description("帮助文章标题")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("Name", "帮助文章标题", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private String? _Content;
    /// <summary>内容</summary>
    [DisplayName("内容")]
    [Description("内容")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("Content", "内容", "text")]
    public String? Content { get => _Content; set { if (OnPropertyChanging("Content", value)) { _Content = value; OnPropertyChanged("Content"); } } }

    private String? _Pic;
    /// <summary>文章主图</summary>
    [DisplayName("文章主图")]
    [Description("文章主图")]
    [DataObjectField(false, false, true, 255)]
    [BindColumn("Pic", "文章主图", "")]
    public String? Pic { get => _Pic; set { if (OnPropertyChanging("Pic", value)) { _Pic = value; OnPropertyChanged("Pic"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IHelpsLan model)
    {
        Id = model.Id;
        HId = model.HId;
        LId = model.LId;
        Name = model.Name;
        Content = model.Content;
        Pic = model.Pic;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "HId" => _HId,
            "LId" => _LId,
            "Name" => _Name,
            "Content" => _Content,
            "Pic" => _Pic,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "HId": _HId = value.ToInt(); break;
                case "LId": _LId = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "Content": _Content = Convert.ToString(value); break;
                case "Pic": _Pic = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static HelpsLan? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据帮助文章Id、所属语言Id查找</summary>
    /// <param name="hId">帮助文章Id</param>
    /// <param name="lId">所属语言Id</param>
    /// <returns>实体对象</returns>
    public static HelpsLan? FindByHIdAndLId(Int32 hId, Int32 lId)
    {
        if (hId < 0) return null;
        if (lId < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.HId == hId && e.LId == lId);

        return Find(_.HId == hId & _.LId == lId);
    }

    /// <summary>根据帮助文章Id查找</summary>
    /// <param name="hId">帮助文章Id</param>
    /// <returns>实体列表</returns>
    public static IList<HelpsLan> FindAllByHId(Int32 hId)
    {
        if (hId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.HId == hId);

        return FindAll(_.HId == hId);
    }
    #endregion

    #region 字段名
    /// <summary>取得帮助翻译表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>帮助文章Id</summary>
        public static readonly Field HId = FindByName("HId");

        /// <summary>所属语言Id</summary>
        public static readonly Field LId = FindByName("LId");

        /// <summary>帮助文章标题</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>内容</summary>
        public static readonly Field Content = FindByName("Content");

        /// <summary>文章主图</summary>
        public static readonly Field Pic = FindByName("Pic");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得帮助翻译表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>帮助文章Id</summary>
        public const String HId = "HId";

        /// <summary>所属语言Id</summary>
        public const String LId = "LId";

        /// <summary>帮助文章标题</summary>
        public const String Name = "Name";

        /// <summary>内容</summary>
        public const String Content = "Content";

        /// <summary>文章主图</summary>
        public const String Pic = "Pic";
    }
    #endregion
}
