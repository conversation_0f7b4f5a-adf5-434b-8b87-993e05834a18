﻿@using HlktechPower.Entity
@{
    SampleApply sampleApply = Model;
}
<style>
    .w80 {
        width: 80px;
    }
</style>
<div class="page">
    <table border="0" cellpadding="0" cellspacing="0" class="ds-default-table">
        <thead>
            <tr>
                <th colspan="20">@T("样品申请")</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <th class="w80">@T("项目名称")</th>
                <td colspan="20">@sampleApply.ProjectName</td>
            </tr>
            <tr>
                <th class="w80">@T("联系人")</th>
                <td colspan="20">@sampleApply.ContactPerson</td>
            </tr>
            <tr>
                <th class="w80">@T("联系电话")</th>
                <td colspan="20">@sampleApply.Phone</td>
            </tr>
            <tr>
                <th class="w80">@T("邮箱")</th>
                <td colspan="20">@sampleApply.Mail</td>
            </tr>
            <tr>
                <th class="w80">@T("公司名称")</th>
                <td colspan="20">@sampleApply.CompanyName</td>
            </tr>
            <tr>
                <th class="w80">@T("预计")@T("月")/@T("年")@T("用量") (@T("个"))</th>
                <td colspan="20">@sampleApply.Dosage</td>
            </tr>
            <tr>
                <th class="w80">@T("所属地区")</th>
                <td colspan="20">@sampleApply.Country?.Name @sampleApply.Province?.Name @sampleApply.City?.Name</td>
            </tr>
            <tr>
                <th class="w80">@T("详细地址")</th>
                <td colspan="20">@sampleApply.Address</td>
            </tr>
        </tbody>
    </table>
</div>
<link rel="stylesheet" href="~/static/plugins/js/jquery.lightbox/css/lightbox.min.css">
<script src="~/static/plugins/js/jquery.lightbox/js/lightbox.min.js"></script>
<script>

</script>