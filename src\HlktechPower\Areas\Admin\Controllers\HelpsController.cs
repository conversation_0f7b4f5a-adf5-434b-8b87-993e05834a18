﻿
using DH.Core.Domain.Localization;
using DH.Entity;
using HlktechPower.Entity;
using Microsoft.AspNetCore.Mvc;

using NewLife.Data;
using NewLife.Log;

using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Helpers;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Webs;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

namespace HlktechPower.Areas.Admin.Controllers;

/// <summary>帮助管理</summary>
[DisplayName("帮助管理")]
[Description("用于帮助的管理")]
[AdminArea]
[DHMenu(48, ParentMenuName = "Sites", CurrentMenuUrl = "~/{area}/Helps", CurrentMenuName = "HelpsList", CurrentIcon = "&#xe72a;", LastUpdate = "20250603")]
public class HelpsController : PekCubeAdminControllerX {
    /// <summary>
    /// 帮助列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("帮助列表")]
    public IActionResult Index(string name, int search_ac_id = -1, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true
        };
        var list = Helps.Searchs(name, search_ac_id, pages).Select(x => new Helps 
        { 
            Id = x.Id, 
            HName = x.HelpsCategory?.Name, 
            Name = HelpsLan.FindByHIdAndLId(x.Id,WorkingLanguage.Id)?.RealName??x.Name, 
            Show = x.Show, 
            CreateTime = x.CreateTime, 
            Sort = x.Sort 
        });

        var List = new List<HelpsCategory>();
        var live1 = HelpsCategory.FindAllByLevel(0); //1级数据
        viewModel.Claslist = live1;

        viewModel.list = list;
        viewModel.page = page;
        viewModel.name = name;

        viewModel.search_ac_id = search_ac_id;
        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name }, { "search_ac_id", search_ac_id.ToString() } });
        return View(viewModel);
    }

    /// <summary>
    /// 添加帮助管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("添加帮助管理")]
    public IActionResult AddHelps()
    {
        dynamic viewModel = new ExpandoObject();
        ViewBag.FileList = UploadInfo.FindAllByItemIdAndFileType(0, 1);
        ViewBag.FileList2 = UploadInfo.FindAllByItemIdAndFileType(0, 11);
        var List = new List<HelpsCategory>();
        var live1 = HelpsCategory.FindAllByLevel(0); //1级数据
        GetCategoryList(live1, List);

        var Sort = Helps.FindMax("Sort");
        ViewBag.Sort = Sort + 1;
        viewModel.Plist = List;
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View(viewModel);
    }

    /// <summary>
    /// 获取分类集合
    /// </summary>
    /// <param name="levelList"></param>
    /// <param name="list"></param>
    private static void GetCategoryList(IList<HelpsCategory> levelList, IList<HelpsCategory> list)
    {
        if (levelList.Count > 0)
        {
            foreach (var item in levelList)
            {
                list.Add(item);

                var level = HelpsCategory.FindAllByParentId(item.Id);
                GetCategoryList(level, list);
            }
        }
    }

    /// <summary>
    /// 新增提交
    /// </summary>
    /// <param name="Helps_title">帮助标题</param>
    /// <param name="default_user_portrait">帮助主图</param>
    /// <param name="gc_class_id">关联分类帮助Id</param>
    /// <param name="Helps_url">链接</param>
    /// <param name="Helps_show">帮助是否显示</param>
    /// <param name="Helps_sort">帮助排序</param>
    /// <param name="Helps_content">帮助内容</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("新增提交")]
    [HttpPost]
    public IActionResult CreatHelps(IFormFile default_user_portrait, string Helps_title, int gc_class_id, string Helps_url, int Helps_show, int Helps_sort, string Helps_content)
    {
        if (Helps_title.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("标题名称不能为空") });
        }
        var ex = Helps.FindByName(Helps_title.SafeString().Trim());
        if (ex != null)
        {
            return Prompt(new PromptModel { Message = GetResource("名称已存在") });
        }
        if (gc_class_id == 0)
        {
            return Prompt(new PromptModel { Message = GetResource("请选择所属帮助分类") });
        }

        using (var tran1 = Helps.Meta.CreateTrans())
        {

            var Model = new Helps
            {
                Name = Helps_title.SafeString().Trim(),
                HId = gc_class_id,
                Url = Helps_url.SafeString().Trim(),
                Show = Helps_show == 1,
                Sort = Helps_sort,
                Content = Helps_content
            };
            Model.Insert();

            if (default_user_portrait != null)
            {
                var bytes = default_user_portrait.OpenReadStream().ReadBytes(default_user_portrait.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }

                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(default_user_portrait.FileName)}";
                var filepath = $"Helps/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

                saveFileName.EnsureDirectory();
                default_user_portrait.SaveAs(saveFileName);
                Model.Pic = filepath;

                Model.Update();
            }

            var file_id = GetRequest("file_id[]");
            if (file_id.IsNotNullAndWhiteSpace())
            {
                var list = UploadInfo.FindByIds(file_id.Trim(','));
                //修改没有标识的图片
                foreach (var item in list)
                {
                    item.ItemId = Model.Id;
                }
                list.Save();
            }

            var localizationSettings = LocalizationSettings.Current;

            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var filea = Request.Form.Files;
                var list = filea.Count();
                foreach (var item in Languagelist)
                {
                    var aaaa = new HelpsLan();
                    aaaa.Name = GetRequest($"[{item.Id}].Helps_title").SafeString().Trim();
                    aaaa.Content = GetRequest($"Helps_content_{item.Id}").SafeString().Trim();
                    aaaa.HId = Model.Id;
                    aaaa.LId = item.Id;
                    aaaa.Insert();

                    var Name = $"[{item.Id}].default_user_portrait";
                    var file = filea.Where(x => x.Name == Name.SafeString().Trim()).FirstOrDefault();
                    if (file != null)
                    {
                        var bytes = file.OpenReadStream().ReadBytes(file.Length);
                        if (!bytes.IsImageFile())
                        {
                            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                        }
                        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
                        var filepath = $"HelpsLan/{filename}";
                        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

                        filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

                        saveFileName.EnsureDirectory();
                        file.SaveAs(saveFileName);
                        aaaa.Pic = filepath.Replace("\\", "/");
                        aaaa.Update();
                    }
                }
            }
            tran1.Commit();
        }

        Helps.Meta.Cache.Clear("");//清除缓存
        HelpsLan.Meta.Cache.Clear("");//清除缓存
        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 帮助管理修改页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("帮助管理修改页面")]
    public IActionResult EditHelps(Int32 Id)
    {
        var Model = Helps.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));

        }
        ViewBag.FileList = UploadInfo.FindAllByItemId(Id);

        dynamic viewModel = new ExpandoObject();
        var List = new List<HelpsCategory>();
        var live1 = HelpsCategory.FindAllByLevel(0);//一级数据
        GetCategoryList(live1, List);
        ViewBag.Name = "";
        ViewBag.AID = 0;
        var pmodel = HelpsCategory.FindById(Model.HId);
        if (pmodel != null)
        {
            ViewBag.Name = pmodel.Name;
            ViewBag.AID = pmodel.Id;
        }
        viewModel.Plist = List;
        viewModel.Model = Model;

        ViewBag.Images = UrlHelper.Combine(DHSetting.Current.CurDomainUrl??"", Model.Pic?.IsNullOrWhiteSpace() == false ? Model.Pic : "");
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View(viewModel);
    }

    /// <summary>
    /// 帮助管理修改接口
    /// </summary>
    /// <param name="Id">帮助编号</param>
    /// <param name="Helps_title">帮助标题</param>
    /// <param name="default_user_portrait">帮助主图</param>
    /// <param name="gc_class_id">关联分类帮助Id</param>
    /// <param name="Helps_url">链接</param>
    /// <param name="Helps_show">帮助是否显示</param>
    /// <param name="Helps_sort">帮助排序</param>
    /// <param name="Helps_content">帮助内容</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("帮助管理修改接口")]
    public IActionResult EditHelps(Int32 Id, IFormFile default_user_portrait, string Helps_title, int gc_class_id, string Helps_url, int Helps_show, int Helps_sort, string Helps_content)
    {
        if (Helps_title.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("标题名称不能为空") });
        }
        var ex = Helps.FindByName(Helps_title.SafeString().Trim());
        if (ex != null && ex.Id != Id)
        {
            return Prompt(new PromptModel { Message = GetResource("名称已存在") });
        }
        if (gc_class_id == 0)
        {
            return Prompt(new PromptModel { Message = GetResource("请选择所属帮助分类") });
        }
        var Model = Helps.FindById(Id);
        if (Model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除") });
        }

        using (var tran1 = Helps.Meta.CreateTrans())
        {
            Model.Name = Helps_title.SafeString().Trim();
            Model.HId = gc_class_id;
            Model.Url = Helps_url.SafeString().Trim();
            Model.Show = Helps_show == 1;
            Model.Sort = Helps_sort;
            Model.Content = Helps_content;

            if (default_user_portrait != null)
            {
                var bytes = default_user_portrait.OpenReadStream().ReadBytes(default_user_portrait.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }

                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(default_user_portrait.FileName)}";
                var filepath = $"Helps/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

                saveFileName.EnsureDirectory();
                default_user_portrait.SaveAs(saveFileName);
                Model.Pic = filepath;
            }
            Model.Update();

            var file_id = GetRequest("file_id[]");
            if (file_id.IsNotNullAndWhiteSpace())
            {
                var list = UploadInfo.FindByIds(file_id.Trim(','));
                //修改没有标识的图片
                foreach (var item in list)
                {
                    item.ItemId = Model.Id;
                }
                list.Save();
            }
            var localizationSettings = LocalizationSettings.Current;

            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var filea = Request.Form.Files;
                var list = filea.Count();

                var lanlist = HelpsLan.FindAllByHId(Model.Id);
                foreach (var item in Languagelist)
                {
                    var aaaa = lanlist.Find(x => x.LId == item.Id);
                    if (aaaa == null)
                    {
                        aaaa = new HelpsLan();
                    }
                    var Name = $"[{item.Id}].default_user_portrait";
                    aaaa.Name = GetRequest($"[{item.Id}].Helps_title").SafeString().Trim();
                    aaaa.Content = GetRequest($"Helps_content_{item.Id}").SafeString().Trim();
                    aaaa.HId = Model.Id;
                    aaaa.LId = item.Id;
                    aaaa.Save();

                    var file = filea.Where(x => x.Name == Name.SafeString().Trim()).FirstOrDefault();
                    if (file != null)
                    {
                        var bytes = file.OpenReadStream().ReadBytes(file.Length);
                        if (!bytes.IsImageFile())
                        {
                            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                        }
                        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
                        var filepath = $"HelpsLan/{filename}";
                        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

                        filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

                        var f = saveFileName.AsFile();
                        if (f.Exists)
                        {
                            f.Delete();
                        }

                        saveFileName.EnsureDirectory();
                        file.SaveAs(saveFileName);
                        aaaa.Pic = filepath.Replace("\\", "/");
                        aaaa.Update();
                    }
                }
            }
            tran1.Commit();
        }
        Helps.Meta.Cache.Clear("");//清除缓存
        HelpsLan.Meta.Cache.Clear("");//清除缓存
        return Prompt(new PromptModel { Message = GetResource("修改成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 批量删除数据
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("帮助管理删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();
        Helps.DelByIds(Ids.Trim(','));

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 图片上传
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片上传")]
    [HttpPost]
    public IActionResult UploadImg(Int32 Id, IFormFile fileupload)
    {
        var bytes = fileupload.OpenReadStream().ReadBytes(fileupload.Length);
        if (!bytes.IsImageFile())
        {
            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
        }
        var fileModel = new UploadInfo
        {
            FileSize = fileupload.Length,
            FileType = 2,
            ItemId = Id,
            IsImg = true,
            OriginFileName = fileupload.FileName
        };

        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(fileupload.FileName)}";
        var filepath = $"Helps/{filename}";
        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

        filepath = $"/{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

        saveFileName.EnsureDirectory();
        fileupload.SaveAs(saveFileName);

        fileModel.FileName = filename;
        fileModel.FileUrl = filepath;
        fileModel.Insert();

        return Json(new { file_id = fileModel.Id, file_name = filename, file_path = Pek.Helpers.DHWeb.GetSiteUrl() + filepath });
    }

    /// <summary>
    /// 图片删除
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片删除")]
    public IActionResult DeleteImg(Int32 Id)
    {
        var model = UploadInfo.FindById(Id);

        if (model != null)
        {
            try
            {
                model.FileUrl?.GetFullPath().AsFile().Delete();
            }
            catch (Exception ex)
            {
                XTrace.WriteException(ex);
            }

            model.Delete();
        }

        return Ok("true");
    }

    /// <summary>
    /// 根据名称查询
    /// </summary>
    /// <param name="title"></param>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("根据名称查询")]
    public IActionResult FinByName(string title, int Id)
    {
        var Model = Helps.FindByName(title);
        if (Id != 0)
        {
            if (Model != null && Model.Id != Id)
            {
                return Json(false);
            }
        }
        else
        {
            if (Model != null)
            {
                return Json(false);
            }
        }

        return Json(true);
    }

    /// <summary>
    /// 图片上传
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("文件上传")]
    [HttpPost]
    public IActionResult UploadFile(Int32 Id, IFormFile fileupload2)
    {
        if (fileupload2.Length > 2097152)
        {
            return Prompt(new PromptModel { Message = GetResource("最大支持2M的文件！"), IsOk = false });
        }

        var fileModel = new UploadInfo();
        fileModel.FileSize = fileupload2.Length;
        fileModel.FileType = 11;
        fileModel.ItemId = Id;

        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(fileupload2.FileName)}";
        var filepath = $"Article/{filename}";
        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

        XTrace.WriteLine($"获取保存的路径:{saveFileName}");

        filepath = $"/{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

        saveFileName.EnsureDirectory();
        fileupload2.SaveAs(saveFileName);

        fileModel.FileName = filename;
        fileModel.FileUrl = filepath;
        fileModel.Insert();

        return Json(new { file_id = fileModel.Id, file_name = filename, file_path = Pek.Helpers.DHWeb.GetSiteUrl() + filepath });
    }
}
