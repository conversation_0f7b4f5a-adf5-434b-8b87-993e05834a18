/* 字体定义 */
@font-face {
  font-family: "Source Han Sans CN";
  src: url("fonts/Source_Han_Sans_SC_Regular.otf") format("opentype");
  font-style: normal;
  font-display: swap;
  font-weight: 400; 
}
@font-face {
  font-family: "Source Han Sans CN";
  src: url("fonts/Source_Han_Sans_SC_Bold.otf") format("opentype");
  font-style: normal;
  font-display: swap;
  font-weight: 700;
}
@font-face {
  font-family: "Source Han Sans CN";
  src: url("fonts/Source_Han_Sans_SC_Normal_Normal.otf") format("opentype");
  font-style: normal;
  font-display: swap;
  font-weight: 400;
}
@font-face {
  font-family: "Source Han Sans CN";
  src: url("fonts/Source_Han_Sans_SC_Light_Light.otf") format("opentype");
  font-style: normal;
  font-display: swap;
  font-weight: 300;
}

@font-face {
  font-family: "Source Han Sans CN";
  src: url("fonts/Source_Han_Sans_SC_ExtraLight_ExtraLight.otf") format("opentype");
  font-style: normal;
  font-display: swap;
  font-weight: 100;
}

@font-face {
  font-family: "Source Han Sans CN";
  src: url("fonts/Source_Han_Sans_SC_Medium_Medium.otf") format("opentype");
  font-style: normal;
  font-display: swap;
  font-weight: 500;
}
@font-face {
  font-family: "Source Han Sans CN";
  src: url("fonts/Source_Han_Sans_SC_Heavy_Heavy.otf") format("opentype");
  font-style: normal;
  font-display: swap;
  font-weight: 900;
}



/* 图标字体通用样式 */
/* .iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
} */

/* 正文字体设置 */
body {
  font-family: "Source Han Sans CN", "Microsoft Yahei", Arial, sans-serif;
  font-size: 14px;
}

/* 标题字体 */
h1,
h2,
h3,
h4,
h5,
h6 {
  /* Hiragino Sans GB",  */
  font-family: "Source Han Sans CN", "Microsoft Yahei", Arial, sans-serif;
}

/* 移动端 */
/* @media (max-width: 767px) {
  html {
    font-size: 10px;
  }
} */

/* 平板 */
/* @media (min-width: 768px) {
  html {
    font-size: 12px;
  }
} */

/* PC */
/* @media (min-width: 1200px) {
  html {
    font-size: 16px;
  }
} */

/* 大屏 */
/* @media (min-width: 1920px) {
  html {
    font-size: 16px;
  }
} */