﻿using DH.Entity;
using HlktechPower.Entity;
using Microsoft.AspNetCore.Mvc;
using Pek.Models;
using Pek.NCube;
using System.Dynamic;
using System.Xml.Linq;

namespace HlktechPower.Controllers
{
    /// <summary>
    /// 联系我们
    /// </summary>
    public class ContactUsController : PekBaseControllerX
    {
        /// <summary>
        /// 联系信息
        /// </summary>
        /// <returns></returns>
        public IActionResult Index()
        {
            var model = SingleArticle.FindByCode("ContactInformation");
            if (model == null)
                return Content(GetResource("文章内容不存在"));

            var modelLan = SingleArticleLan.FindBySIdAndLId(model.Id, WorkingLanguage.Id);

            if (!string.IsNullOrEmpty(modelLan?.Content))
                model.Content = modelLan.Content;

            return View(model);
        }

        /// <summary>
        /// 建议反馈
        /// </summary>
        /// <returns></returns>
        public IActionResult SuggestionAndFeedback()
        {
            dynamic viewModel = new ExpandoObject();
            //产品分类
            viewModel.productlist = ProductClass.FindAllByParentId(0).Where(e => e.Enable).OrderBy(e => e.Sort).Select(e => new ProductClass
            {
                Id = e.Id,
                Name = ProductClassLan.FindByProductClass(e, WorkingLanguage.Id).Name,
                Image = ProductClassLan.FindByProductClass(e, WorkingLanguage.Id).Image,
            });
            //省份
            viewModel.provincelist = Regions.FindAllByCIdAndLevel("CN", 0).Select(e => new Regions
            {
                Id = e.Id,
                AreaCode = e.AreaCode,
                Name = RegionsLan.FindByRIdAndLId(e.Id, WorkingLanguage.Id)?.Name ?? e.Name,
            });
            return View(viewModel);
        }

        /// <summary>
        /// 添加建议反馈
        /// </summary>
        /// <param name="ProblemType">问题类型</param>
        /// <param name="PId">产品分类编号</param>
        /// <param name="Content">问题与建议</param>
        /// <param name="Name">姓名</param>
        /// <param name="Mail">邮箱</param>
        /// <param name="Phone">手机</param>
        /// <param name="ProvinceCode">省份</param>
        /// <param name="CityCode">城市</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddSuggestionAndFeedback([FromForm]Int32 ProblemType, [FromForm]Int64 productType, [FromForm]String Content, [FromForm] String Name, [FromForm] String Mail, [FromForm] String Phone, [FromForm] String ProvinceCode, [FromForm] String CityCode)
        {
            DResult res = new();
            if (ProblemType == 1 && productType <= 0)
            {
                res.msg = GetResource("产品类型不能为空");
                return Json(res);
            }
            if (Content.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("问题与建议不能为空");
                return Json(res);
            }
            if (Name.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("姓名不能为空");
                return Json(res);
            }
            if (Mail.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("邮箱不能为空");
                return Json(res);
            }
            if (Phone.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("手机不能为空");
                return Json(res);
            }
            if (ProvinceCode.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("省份不能为空");
                return Json(res);
            }
            if (CityCode.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("城市不能为空");
                return Json(res);
            }
            var model = new Problem()
            {
                ProblemType = ProblemType,
                PId = productType,
                Show = true,
                Content = Content,
                Name = Name,
                Mail = Mail,
                Phone = Phone,
                Address = ProvinceCode+","+ CityCode,
            };
            model.Insert();
            res.success = true;
            return Json(res);
        }
    }
}
