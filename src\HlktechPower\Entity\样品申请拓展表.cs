﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechPower.Entity;

/// <summary>样品申请拓展表</summary>
[Serializable]
[DataObject]
[Description("样品申请拓展表")]
[BindIndex("IX_DH_SampleApplyEx_SId", false, "SId")]
[BindIndex("IX_DH_SampleApplyEx_PId1", false, "PId1")]
[BindIndex("IX_DH_SampleApplyEx_PId2", false, "PId2")]
[BindIndex("IX_DH_SampleApplyEx_PId3", false, "PId3")]
[BindTable("DH_SampleApplyEx", Description = "样品申请拓展表", ConnName = "Pek", DbType = DatabaseType.None)]
public partial class SampleApplyEx : ISampleApplyEx, IEntity<ISampleApplyEx>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int64 _SId;
    /// <summary>样品申请编号</summary>
    [DisplayName("样品申请编号")]
    [Description("样品申请编号")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("SId", "样品申请编号", "")]
    public Int64 SId { get => _SId; set { if (OnPropertyChanging("SId", value)) { _SId = value; OnPropertyChanged("SId"); } } }

    private Int64 _PId1;
    /// <summary>产品分类编号1</summary>
    [DisplayName("产品分类编号1")]
    [Description("产品分类编号1")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("PId1", "产品分类编号1", "")]
    public Int64 PId1 { get => _PId1; set { if (OnPropertyChanging("PId1", value)) { _PId1 = value; OnPropertyChanged("PId1"); } } }

    private Int64 _PId2;
    /// <summary>产品分类编号2</summary>
    [DisplayName("产品分类编号2")]
    [Description("产品分类编号2")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("PId2", "产品分类编号2", "")]
    public Int64 PId2 { get => _PId2; set { if (OnPropertyChanging("PId2", value)) { _PId2 = value; OnPropertyChanged("PId2"); } } }

    private Int64 _PId3;
    /// <summary>产品分类编号3</summary>
    [DisplayName("产品分类编号3")]
    [Description("产品分类编号3")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("PId3", "产品分类编号3", "")]
    public Int64 PId3 { get => _PId3; set { if (OnPropertyChanging("PId3", value)) { _PId3 = value; OnPropertyChanged("PId3"); } } }

    private Int32 _Number;
    /// <summary>数量</summary>
    [DisplayName("数量")]
    [Description("数量")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Number", "数量", "")]
    public Int32 Number { get => _Number; set { if (OnPropertyChanging("Number", value)) { _Number = value; OnPropertyChanged("Number"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISampleApplyEx model)
    {
        Id = model.Id;
        SId = model.SId;
        PId1 = model.PId1;
        PId2 = model.PId2;
        PId3 = model.PId3;
        Number = model.Number;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "SId" => _SId,
            "PId1" => _PId1,
            "PId2" => _PId2,
            "PId3" => _PId3,
            "Number" => _Number,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "SId": _SId = value.ToLong(); break;
                case "PId1": _PId1 = value.ToLong(); break;
                case "PId2": _PId2 = value.ToLong(); break;
                case "PId3": _PId3 = value.ToLong(); break;
                case "Number": _Number = value.ToInt(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static SampleApplyEx? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据样品申请编号查找</summary>
    /// <param name="sId">样品申请编号</param>
    /// <returns>实体列表</returns>
    public static IList<SampleApplyEx> FindAllBySId(Int64 sId)
    {
        if (sId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.SId == sId);

        return FindAll(_.SId == sId);
    }

    /// <summary>根据产品分类编号1查找</summary>
    /// <param name="pId1">产品分类编号1</param>
    /// <returns>实体列表</returns>
    public static IList<SampleApplyEx> FindAllByPId1(Int64 pId1)
    {
        if (pId1 < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.PId1 == pId1);

        return FindAll(_.PId1 == pId1);
    }

    /// <summary>根据产品分类编号2查找</summary>
    /// <param name="pId2">产品分类编号2</param>
    /// <returns>实体列表</returns>
    public static IList<SampleApplyEx> FindAllByPId2(Int64 pId2)
    {
        if (pId2 < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.PId2 == pId2);

        return FindAll(_.PId2 == pId2);
    }

    /// <summary>根据产品分类编号3查找</summary>
    /// <param name="pId3">产品分类编号3</param>
    /// <returns>实体列表</returns>
    public static IList<SampleApplyEx> FindAllByPId3(Int64 pId3)
    {
        if (pId3 < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.PId3 == pId3);

        return FindAll(_.PId3 == pId3);
    }
    #endregion

    #region 字段名
    /// <summary>取得样品申请拓展表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>样品申请编号</summary>
        public static readonly Field SId = FindByName("SId");

        /// <summary>产品分类编号1</summary>
        public static readonly Field PId1 = FindByName("PId1");

        /// <summary>产品分类编号2</summary>
        public static readonly Field PId2 = FindByName("PId2");

        /// <summary>产品分类编号3</summary>
        public static readonly Field PId3 = FindByName("PId3");

        /// <summary>数量</summary>
        public static readonly Field Number = FindByName("Number");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得样品申请拓展表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>样品申请编号</summary>
        public const String SId = "SId";

        /// <summary>产品分类编号1</summary>
        public const String PId1 = "PId1";

        /// <summary>产品分类编号2</summary>
        public const String PId2 = "PId2";

        /// <summary>产品分类编号3</summary>
        public const String PId3 = "PId3";

        /// <summary>数量</summary>
        public const String Number = "Number";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
