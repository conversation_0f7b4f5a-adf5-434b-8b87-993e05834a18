﻿@using HlktechPower.Entity

<head>
    <link rel="stylesheet" href="~/css/mySidebar.css">
    <!-- 综合-script -->
    <script src="~/components/mySidebar.js" defer></script>
</head>

<body style="background-color:white">
    <div class="main">
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="/">@T("首页")</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="@Url.Action("#")">@T("应用支持")</div>
        </div>
        <div class="myContent">
            <!-- 侧边栏 -->
            <div class="mySidebar"></div>
            <div class="myContent-right">
            </div>
        </div>
    </div>
    <div class="bug"></div>
</body>
<script>
    const data = [
        { text: '@T("应用支持")', link: '#' },  // 标题
        { text: '@T("焦点专题")', link: '@Url.Action("Index")' },
        { text: '@T("资料下载")', link: '@Url.Action("DataDownload")' },
        { text: '@T("应用视频")', link: '@Url.Action("AppVideo")' },
        { text: '@T("常见问题")', link: '@Url.Action("CommonProblem")' },
        { text: '@T("样品申请")', link: '@Url.Action("SampleApply")' },
        { text: '@T("成品检测报告")', link: '@Url.Action("ProductInspectionReport")' }
    ];
</script>
