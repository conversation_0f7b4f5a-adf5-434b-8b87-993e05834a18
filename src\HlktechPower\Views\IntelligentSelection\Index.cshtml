﻿@using HlktechPower.Entity
<head>
    <!-- 本页私有css -->
    <link rel="stylesheet" href="~/css/pageCss/intelligent.css">
</head>

<body style="background-color:white">
    <div class="intelligent">
        <div class="intelligent-left">
            <div class="layui-tab layui-tab-brief">
                <ul class="layui-tab-title">
                    <li class="layui-this">
                        <img src="~/images/power/zhinengxuanxing_gaoliang.png" alt="">
                        <span>@T("智能选型")</span>
                    </li>
                    <li>
                        <img src="~/images/power/rengong_moren.png" alt="">
                        <span>@T("人工选型")</span>
                    </li>
                    <li>
                        <img src="~/images/power/rengong_moren.png" alt="">
                        <span>@T("其他模块")</span>
                    </li>
                </ul>
                <div class="layui-tab-content">
                    <!-- 智能选型 -->
                    <div class="layui-tab-item layui-show mind">
                        <p>@T("如无法找到合适产品或需替代，可选择人工选型")</p>
                        <div class="type"> <img src="../../images/power/leixing.png" alt=""><span>@T("产品类型")</span></div>

                        <div class="layui-tab layui-tab-brief" lay-filter="demoTabFilter">


                            <ul class="layui-tab-title" id="demoTabsHeader">
                                @{
                                    var i = 0;
                                }
                                @foreach (ProductClass item in Model.listProductClass)
                                {
                                    if (i == 0)
                                    {
                                        <li class="layui-this">@item.Name</li>
                                    }
                                    else
                                    {
                                        <li>@item.Name</li>
                                    }
                                    i++;
                                }
                            </ul>
                            <div class="layui-tab-content" id="demoTabsBody">
                                <!-- AC/DC电源模块 -->
                                <div class="layui-tab-item layui-show acdc">
                                    <form class="layui-form">
                                        <div class="demo-acdc-container">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">@T("输出功率"):</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="output"  placeholder="@T("请输入")"
                                                           lay-reqtext="" autocomplete="off" class="layui-input">
                                                    <div class="layui-input-split layui-input-suffix">
                                                        W
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="layui-form-item inp">
                                                <label class="layui-form-label">@T("输入电压"):</label>
                                            </div>
                                            <div class="layui-col-md5">
                                                <div class="layui-form-item inp">
                                                    <div class="layui-input-block">
                                                        <input type="text" name="min" placeholder="@T("请输入")" class="layui-input">
                                                        <div class="layui-input-split layui-input-suffix">
                                                            VDC
                                                        </div>

                                                    </div>

                                                </div>

                                            </div>
                                            <div class="layui-form-mid layui-text-em">
                                                <i class="layui-icon layui-icon-reduce-circle"></i>
                                            </div>
                                            <div class="layui-col-md5">
                                                <div class="layui-form-item inp">
                                                    <div class="layui-input-block">
                                                        <input type="text" name="max" placeholder="@T("请输入")" class="layui-input">
                                                        <div class="layui-input-split layui-input-suffix">
                                                            VDC
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>


                                            <div class="layui-col-md12">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label Vout">@T("输出电压"):&nbsp;&nbsp; Vout1: </label>
                                                    <div class="layui-input-inline">
                                                        <input type="text" name="Vout1" value="4" lay-verify="required" placeholder="@T("请输入")" class="layui-input">
                                                        <div class="layui-input-split layui-input-suffix">
                                                            VDC <i class="layui-icon layui-icon-add-circle-fine"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>


                                            <div class="layui-col-md12 tip">@T("如需多路输出产品请点击+号图标")</div>
                                            <div class="layui-form-item inp">
                                                <label class="layui-form-label">@T("封装类型"):</label>
                                            </div>
                                            <ul class="pics">
                                                <li>
                                                    <img src="../../images/power/3w5.png" alt="">
                                                    <span data-value="a-1">板载式</span>
                                                </li>
                                                <li>
                                                    <img src="../../images/power/3w5.png" alt="">
                                                    <span data-value="a-2">机壳式</span>
                                                </li>
                                                <li>
                                                    <img src="../../images/power/3w5.png" alt="">
                                                    <span data-value="a-3">导轨式</span>
                                                </li>
                                                <li>
                                                    <img src="../../images/power/3w5.png" alt="">
                                                    <span data-value="a-4">开板式</span>
                                                </li>
                                            </ul>
                                            <div class="layui-form-item inp">
                                                <label class="layui-form-label">@T("产品特点"):</label>
                                            </div>

                                            <ul class="pics">
                                                <li>
                                                    <span data-value="aa">小体积</span>
                                                </li>
                                                <li>
                                                    <span data-value="ac">经济型</span>
                                                </li>
                                                <li>
                                                    <span data-value="ae">高可靠</span>
                                                </li>
                                                <li>
                                                    <span data-value="ab">电力</span>
                                                </li>
                                            </ul>

                                            <div class="layui-form-item">
                                                <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="acdcSubmit">@T("点击筛选")</button>
                                                <button type="reset" class="layui-btn layui-btn-fluid"
                                                        lay-filter="acdcReset">
                                                    @T("重置")
                                                </button>
                                            </div>

                                        </div>
                                    </form>
                                </div>
                                <!-- DC/DC电源模块 -->
                                <div class="layui-tab-item dcdc">
                                    <form class="layui-form">
                                        <div class="demo-dcdc-container">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">输出功率:</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="output" value="1" placeholder="请填写(1~75)"
                                                           lay-reqtext="请填写用户名" autocomplete="off" class="layui-input">
                                                    <div class="layui-input-split layui-input-suffix">
                                                        W
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="layui-form-item inp">
                                                <label class="layui-form-label">输入电压:</label>
                                            </div>
                                            <div class="layui-col-md5">
                                                <div class="layui-form-item inp">
                                                    <div class="layui-input-block">
                                                        <input type="text" name="min" value="2" placeholder="最小" class="layui-input">
                                                        <div class="layui-input-split layui-input-suffix">
                                                            VDC
                                                        </div>

                                                    </div>

                                                </div>

                                            </div>
                                            <div class="layui-form-mid layui-text-em">
                                                <i class="layui-icon layui-icon-reduce-circle"></i>
                                            </div>
                                            <div class="layui-col-md5">
                                                <div class="layui-form-item inp">
                                                    <div class="layui-input-block">
                                                        <input type="text" name="max" value="3" placeholder="最大" class="layui-input">
                                                        <div class="layui-input-split layui-input-suffix">
                                                            VDC
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>


                                            <div class="layui-col-md12">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label Vout">输出电压:&nbsp;&nbsp; Vout1: </label>
                                                    <div class="layui-input-inline ">
                                                        <input type="text" name="Vout1" value="4" lay-verify="required" placeholder="单路输出" class="layui-input">
                                                        <div class="layui-input-split layui-input-suffix">
                                                            VDC <i class="layui-icon layui-icon-add-circle-fine"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-col-md12 tip">如需多路输出产品请点击+号图标</div>


                                            <div class="layui-form-item inp">
                                                <label class="layui-form-label">隔离电压:</label>
                                            </div>

                                            <ul class="pics">
                                                <li>
                                                    <span data-value="a">非隔离 </span>
                                                </li>
                                                <li>
                                                    <span data-value="c">1500VDC</span>
                                                </li>
                                                <li>
                                                    <span data-value="e">3000VDC</span>
                                                </li>
                                                <li>
                                                    <span data-value="b">6000VDC</span>
                                                </li>
                                            </ul>


                                            <div class="layui-form-item inp">

                                                <label class="layui-form-label">封装类型:</label>
                                            </div>
                                            <ul class="pics">
                                                <li>
                                                    <span data-value="DIP">DIP</span>
                                                </li>
                                                <li>
                                                    <span data-value="SIP">SIP</span>
                                                </li>
                                            </ul>

                                            <div class="layui-form-item inp">
                                                <label class="layui-form-label">产品特点:</label>
                                            </div>

                                            <ul class="pics">
                                                <li>
                                                    <span data-value="a">工业通用</span>
                                                </li>
                                                <li>
                                                    <span data-value="c">光伏适用</span>
                                                </li>
                                                <li>
                                                    <span data-value="e">经济开板</span>
                                                </li>
                                                <li>
                                                    <span data-value="b">汽车专用</span>
                                                </li>
                                                <li>
                                                    <span data-value="f">高隔离(电力/医疗)</span>
                                                </li>
                                                <li>
                                                    <span data-value="d">铁路适用</span>
                                                </li>
                                            </ul>

                                            <div class="layui-form-item">
                                                <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="dcdcSubmit">点击筛选</button>
                                                <button type="reset" class="layui-btn layui-btn-fluid"
                                                        lay-filter="dcdcReset">
                                                    重置
                                                </button>
                                            </div>

                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                    </div>
                    <!-- 人工选型 -->
                    <div class="layui-tab-item manual">
                        <p>请填写下方内容，我可将第一时间与您电话沟通</p>
                        <form class="layui-form">
                            <div class="demo-manual-container">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">输出功率:</label>
                                    <div class="layui-input-block first">
                                        <input type="text" lay-verify="required" name="output" value="1" placeholder="请填写(1~350)"
                                               lay-reqtext="请填写输出功率" autocomplete="off" class="layui-input my-input">
                                        <div class="layui-input-split layui-input-suffix">
                                            W
                                        </div>
                                    </div>

                                </div>
                                <div class="layui-form-item inp">
                                    <label class="layui-form-label">输入电压:</label>
                                </div>
                                <div class="layui-col-md5">
                                    <div class="layui-form-item inp">
                                        <div class="layui-input-block">
                                            <input type="text" lay-verify="required" name="min" value="2" placeholder="最小" class="layui-input my-input">
                                            <div class="layui-input-split layui-input-suffix">
                                                VDC
                                            </div>

                                        </div>

                                    </div>

                                </div>
                                <div class="layui-form-mid layui-text-em">
                                    <i class="layui-icon layui-icon-reduce-circle"></i>
                                </div>
                                <div class="layui-col-md5">
                                    <div class="layui-form-item inp">
                                        <div class="layui-input-block">
                                            <input type="text" lay-verify="required" name="max" value="3" placeholder="最大" class="layui-input my-input">
                                            <div class="layui-input-split layui-input-suffix">
                                                VDC
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <div class="layui-col-md12">
                                    <div class="layui-form-item ">
                                        <label class="layui-form-label Vout">输出电压:&nbsp;&nbsp; Vout1: </label>
                                        <div class="layui-input-inline ">
                                            <input type="text" name="Vout1" value="4" lay-verify="required" placeholder="单路输出" class="layui-input my-input">
                                            <div class="layui-input-split layui-input-suffix">
                                                VDC <i class="layui-icon layui-icon-add-circle-fine"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md12 tip">如需多路输出产品请点击+号图标</div>
                                <div class="layui-col-md11">
                                    <div class="use">
                                        使用过品牌&型号:
                                    </div>
                                    <div class="layui-form-item">
                                        <div class="layui-input-block">
                                            <input type="text" name="brandModel" lay-verify="required" placeholder="请输入" autocomplete="off"
                                                   class="layui-input my-Input" value="5">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item inp">
                                    <label class="layui-form-label">产品需求:</label>
                                </div>
                                <div class="layui-col-md11">
                                    <div class="layui-form-item">
                                        <div class="layui-input-block">
                                            <input type="text" name="demand" lay-verify="required" placeholder="请输入" autocomplete="off"
                                                   class="layui-input" value="6">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item inp">
                                    <label class="layui-form-label">您的称呼:</label>
                                </div>
                                <div class="layui-col-md11">
                                    <div class="layui-form-item">
                                        <div class="layui-input-block">
                                            <input type="text" name="call" lay-verify="required" placeholder="请输入" autocomplete="off"
                                                   class="layui-input" value="7">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item inp">
                                    <label class="layui-form-label">您的电话:</label>
                                </div>
                                <div class="layui-col-md11">
                                    <div class="layui-form-item">
                                        <div class="layui-input-block">
                                            <input type="text" name="phone" lay-verify="required" placeholder="请输入" autocomplete="off"
                                                   class="layui-input" value="8">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item inp bottom">
                                    <label class="myLabel">您的公司全称:</label>
                                </div>
                                <div class="layui-col-md11">
                                    <div class="layui-form-item">
                                        <div class="layui-input-block">
                                            <input type="text" name="fullName" lay-verify="required" placeholder="请输入" autocomplete="off"
                                                   class="layui-input" value="9">
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-form-item inp bottom">
                                    <label class="myLabel">所在区域:</label>
                                </div>
                                <div class="layui-input-inline last">
                                    <div class="layui-form-item">
                                        <div class="layui-col-md5">
                                            <select name="quiz1" lay-verify="required">
                                                <option value="" selected>请选择省</option>
                                                <option value="浙江">浙江省</option>
                                                <option value="你的工号">江西省</option>
                                                <option value="你最喜欢的老师">福建省</option>
                                            </select>
                                        </div>
                                        <div class="layui-form-mid layui-text-em"></div>
                                        <div class="layui-col-md5">
                                            <select name="quiz2" lay-verify="required">
                                                <option value="">请选择市</option>
                                                <option value="杭州">杭州</option>
                                                <option value="宁波" disabled>宁波</option>
                                                <option value="温州">温州</option>
                                                <option value="温州">台州</option>
                                                <option value="温州">绍兴</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="manualSubmit">提交选型信息</button>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                     <!-- 其他模块 -->
                    <div class="layui-tab-item others">
                      其他模块
                    </div>

                </div>



            </div>
        </div>
        <!-- 表格 -->
        <div class="intelligent-right">
            <div class="tableBox">
                <table class="layui-table">
                    <thead>
                        <tr>
                            <th>产品型号</th>
                            <th>产品图片</th>
                            <th>功率 <br> (W)</th>
                            <th>输入电压<br>(VAC)</th>
                            <th>输入电压<br>(VDC)</th>
                            <th>输出电压<br>(VDC)</th>
                            <th>输出电流<br>(mA)</th>
                            <th>输出路数<br>(mA)</th>
                            <th>隔离电压<br>(mA)</th>
                            <th>封装形式</th>
                            <th>封装尺寸<br>(mm)</th>
                            <th class="long">资料下载<br>(3D/PCB封装库/<br>原理图库)</th>
                            <th>认证/标准</th>
                            <th>技术<br>手册</th>
                            <th>样品<br>申请</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div onclick="toRouter(this)" data-link="../product/productSeries.html">
                                    2W系列
                                </div>
                            </td>
                            <td>
                                <img class="product-img" src="../../images/power/3w5.png" alt="">
                            </td>
                            <td>2W</td>
                            <td>90-265</td>
                            <td>120-350</td>
                            <td>3.3V</td>
                            <td>600mA</td>
                            <td>-</td>
                            <td>3000VAC</td>
                            <td>SIP</td>
                            <td>30.7*16.5*10</td>
                            <td><img src="../../images/power/xiazai.png" alt=""></td>
                            <td class="renzhengBox">
                                <img src="../../images/power/renzheng.png" alt="">
                            </td>
                            <td><img src="../../images/power/pdf.png" alt=""></a></td>
                            <td><img src="../../images/power/shenqing.png" alt=""></td>
                        </tr>

                    </tbody>
                </table>

            </div>

        </div>

    </div>
    <div class="paging">
        <span class="warn">上方产品参数仅供参考，详细技术参数请以技术规格书为准</span>
        <div id="demo-laypage-all"></div>
    </div>
    <div class="bug"></div>
</body>
<script>
    layui.use(['element'], function (element) {
      // Tab 切换事件，监听 lay-filter="demoTabFilter" 的 Tab
      element.on('tab(demoTabFilter)', function (data) {
        // console.log(this); // 当前 Tab 标题的 DOM 对象
        // console.log(data.index); // 得到当前 Tab 的所在下标
        // console.log(data.elem); // 得到 Tab 的容器 DOM 对象
      });
    });

    document.addEventListener('DOMContentLoaded', function () {
      layui.use(['form', 'laydate', 'util'], function () {
        var form = layui.form;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var util = layui.util;

        //点击添加多路输出
        document.addEventListener('click', function(e) {
          if (e.target.classList.contains('layui-icon-add-circle-fine')) {
            // 判断是哪个表单的添加按钮
            const isACDC = e.target.closest('.acdc') !== null;
            const isDCDC = e.target.closest('.dcdc') !== null;
            const isManual = e.target.closest('.manual') !== null;

            if (isACDC) {
              addNewOutputField('acdc');
            } else if (isDCDC) {
              addNewOutputField('dcdc');

            } else if (isManual) {
              addNewOutputField('manual');
            }
          }
        });

        // 添加新的输出字段
        function addNewOutputField(type) {
          const outputContainer = document.querySelector(`.demo-${type}-container`);
          const outputCount = document.querySelectorAll(`.${type} .layui-form-item .Vout`).length;

          // 限制最多4路输出
          if (outputCount >= 4) {
            layer.msg('最多支持4路输出');
            return;
          }

          const newOutputNumber = outputCount + 1;
          let placeholderText = '';

          // 根据输出路数设置不同的placeholder
          switch(newOutputNumber) {
            case 2:
              placeholderText = '两路输出';
              break;
            case 3:
              placeholderText = '三路输出';
              break;
            case 4:
              placeholderText = '四路输出';
              break;
            default:
              placeholderText = '单路输出';
          }

          const newOutputField = document.createElement('div');
          newOutputField.className = 'layui-col-md12';
          newOutputField.innerHTML = `
            <div class="layui-form-item">
              <label class="layui-form-label Vout">Vout${newOutputNumber}: </label>
              <div class="layui-input-inline ">
                <input type="text" name="Vout${newOutputNumber}" value="" lay-verify="required" placeholder="${placeholderText}" class="layui-input my-input">
                <div class="layui-input-split layui-input-suffix">
                  VDC <i class="layui-icon layui-icon-reduce-circle del"></i>
                </div>
              </div>
            </div>
          `;

          // 在提示文字之前插入新的输出字段
          const tipElement = outputContainer.querySelector('.tip');
          outputContainer.insertBefore(newOutputField, tipElement);

          // 重新渲染表单
          form.render();
        }

        // 删除输出字段
        document.addEventListener('click', function(e) {
          if (e.target.classList.contains('layui-icon-reduce-circle')) {
            const outputItem = e.target.closest('.layui-col-md12');
            if (outputItem) {
              // 检查是否包含"输出电压:"文本，如果包含则说明是标题行，不允许删除
              const labelText = outputItem.querySelector('.Vout').textContent;
              if (labelText.includes('输出电压:')) {
                return;
              }
              outputItem.remove();
              // 重新编号剩余的Vout字段
              const type = outputItem.closest('.acdc') ? 'acdc' : 'dcdc';
              renumberOutputFields(type);
            }
          }
        });

        // 重新编号输出字段
        function renumberOutputFields(type) {
          const outputFields = document.querySelectorAll(`.${type} .layui-form-item .Vout`);
          // 跳过第一个字段（标题行）
          for (let i = 1; i < outputFields.length; i++) {
            const field = outputFields[i];
            const newNumber = i;
            field.textContent = `Vout${newNumber}: `;
            const input = field.nextElementSibling.querySelector('input');
            input.name = `Vout${newNumber}`;
          }
        }

        //ACDC 获取封装类型 单选
        let actValue = ''
        document.addEventListener('click', function(e) {
          // 检查点击的是否是封装类型列表项
          if (e.target.closest('.pics li')) {
            const clickedItem = e.target.closest('.pics li');
            console.log('clickedItem', clickedItem);
            // 移除所有兄弟元素的act类
            $(clickedItem).siblings().removeClass('act');
            // 为当前元素添加act类
            $(clickedItem).addClass('act');
            // 获取选中的值
            actValue = $(clickedItem).find('span').data('value');
            console.log('选中的封装类型:', actValue);
          }
        });

        //ACDC 产品特点 多选
        let selectedValues = [];
        // DCDC 隔离电压 封装类型 产品特点 多选
        let arr1 = []
        let arr2 = []
        let arr3 = []

        // 通用的多选处理函数
        function handleMultipleSelection(container, selector, targetArray) {
          $(container).on('click', selector, function(e) {
            e.stopPropagation();
            const clickedItem = $(this);
            clickedItem.toggleClass('act');

            // 更新数组
            targetArray.length = 0; // 清空数组
            $(container).find(selector + '.act').each(function() {
              targetArray.push($(this).find('span').data('value'));
            });

            console.log('选中的值:', targetArray);
          });
        }

        // 初始化DCDC的多选功能
        handleMultipleSelection('.dcdc', '.pics:nth-of-type(1) li', arr1); // 隔离电压
        handleMultipleSelection('.dcdc', '.pics:nth-of-type(2) li', arr2); // 封装类型
        handleMultipleSelection('.dcdc', '.pics:nth-of-type(3) li', arr3); // 产品特点

        // 初始化ACDC的多选功能
        handleMultipleSelection('.acdc', '.pics:nth-of-type(2) li', selectedValues); // 产品特点

        // 提交事件
        form.on('submit(acdcSubmit)', function (data) {
          var field = data.field; // 获取表单字段值
          // acdc
          field.actValue = actValue
          field.selectedValues = selectedValues

          // 显示填写结果
          console.log('field :>> ', field);
          return false; // 阻止默认 form 跳转
        });

        // 重置ACDC表单
        $('.layui-btn[lay-filter="acdcReset"]').on('click', function(e) {
          e.preventDefault();

          // 使用layui表单重置
          $('.acdc form')[0].reset();

          // 重置封装类型单选
          $('.acdc .pics:first-of-type li').removeClass('act');
          actValue = '';

          // 重置产品特点多选
          $('.acdc .pics:nth-of-type(2) li').removeClass('act');
          selectedValues = [];

          // 重置多路输出字段
          const outputFields = document.querySelectorAll('.acdc .layui-form-item .Vout');
          // 保留第一个输出字段，删除其他的
          for (let i = outputFields.length - 1; i > 0; i--) {
            outputFields[i].closest('.layui-col-md12').remove();
          }

          // 重新渲染表单
          form.render();

          return false;
        });

        // 提交事件
        form.on('submit(dcdcSubmit)', function (data) {
          var field = data.field; // 获取表单字段值
          // dcdc
          field.arr1 = arr1
          field.arr2 = arr2
          field.arr3 = arr3

          // 显示填写结果
          console.log('field :>> ', field);
          return false;
        });

        // 重置DCDC表单
        $('.layui-btn[lay-filter="dcdcReset"]').on('click', function(e) {
          e.preventDefault();

          // 使用layui表单重置
          $('.dcdc form')[0].reset();

          // 重置隔离电压多选
          $('.dcdc .pics:nth-of-type(1) li').removeClass('act');
          arr1 = [];

          // 重置封装类型多选
          $('.dcdc .pics:nth-of-type(2) li').removeClass('act');
          arr2 = [];

          // 重置产品特点多选
          $('.dcdc .pics:nth-of-type(3) li').removeClass('act');
          arr3 = [];

          // 重置多路输出字段
          const outputFields = document.querySelectorAll('.dcdc .layui-form-item .Vout');
          // 保留第一个输出字段，删除其他的
          for (let i = outputFields.length - 1; i > 0; i--) {
            outputFields[i].closest('.layui-col-md12').remove();
          }

          // 重新渲染表单
          form.render();

          return false;
        });



         // 提交事件
        form.on('submit(manualSubmit)', function (data) {
          var field = data.field; // 获取表单字段值
          // manual

          // 显示填写结果
          console.log('field :>> ', field);

          // 重置表单
          resetManualForm();

          return false;
        });


         // 重置manual表单
         function resetManualForm() {
          // 重置表单
          $('.manual form')[0].reset();

          // 重置多路输出字段
          const outputFields = document.querySelectorAll('.manual .layui-form-item .Vout');
          // 保留第一个输出字段，删除其他的
          for (let i = outputFields.length - 1; i > 0; i--) {
            outputFields[i].closest('.layui-col-md12').remove();
          }

          // 重置下拉选择框
          $('.manual select').val('');

          // 重新渲染表单
          form.render();
        }
      });
    });
     // 在表格加载完成后初始化分页器
      layui.use(['laypage'], function () {
        var laypage = layui.laypage;
            // 完整显示
            laypage.render({
                elem: 'demo-laypage-all', // 元素 id
                count: '@Model.total', // 数据总数
                curr: '@Model.page',
                limit: '@Model.limit',
                prev: '@T("上一页")',
                next: '@T("下一页")',
                countText: ['@T("共") ', ' @T("条")'],
                skipText: ['@T("到第")', '@T("页")', '@T("确认")'],
                limitTemplet: function (item) {
                    return item + ' @T("条/页")';
                },
                layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'], // 功能布局
                jump: function (obj, first) {
                    if (!first) {
                        // 获取搜索关键字
                        var key = document.getElementById('searchKey').value;
                        // 跳转到对应页
                        window.location.href = '@Url.Action("Index")?page=' + obj.curr + '&limit=' + obj.limit + '&key=' + key;
                    }
                }
            });
      });
</script>