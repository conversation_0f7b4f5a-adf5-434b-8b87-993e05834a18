﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace HlktechPower.Entity;

public partial class Helps : DHEntityBase<Helps>
{
    #region 对象操作
    static Helps()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(HId));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;

        // 单对象缓存
        var sc = Meta.SingleCache;
        // sc.Expire = 60;
        sc.FindSlaveKeyMethod = k => Find(_.Name == k);
        sc.GetSlaveKeyMethod = e => e.Name;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

        // 检查唯一索引
        // CheckExist(method == DataMethod.Insert, nameof(Name));

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化Helps[帮助表]数据……");

    //    var entity = new Helps();
    //    entity.HId = 0;
    //    entity.Url = "abc";
    //    entity.Show = true;
    //    entity.Sort = 0;
    //    entity.Name = "abc";
    //    entity.Content = "abc";
    //    entity.Pic = "abc";
    //    entity.Hits = 0;
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化Helps[帮助表]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>
    /// 帮助分类
    /// </summary>
    [XmlIgnore, ScriptIgnore, IgnoreDataMember]
    public HelpsCategory? HelpsCategory => Extends.Get(nameof(HelpsCategory), k => HelpsCategory.FindById(HId));

    /// <summary>
    /// 帮助分类名称
    /// </summary>
    [XmlIgnore, ScriptIgnore]
    public String? HName = "";
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="hId">帮助分类Id</param>
    /// <param name="name">帮助文章标题</param>
    /// <param name="hits">浏览次数</param>
    /// <param name="show">帮助文章是否显示，0为否，1为是，默认为1</param>
    /// <param name="start">更新时间开始</param>
    /// <param name="end">更新时间结束</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<Helps> Search(Int32 hId, String? name, Int32 hits, Boolean? show, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (hId >= 0) exp &= _.HId == hId;
        if (!name.IsNullOrEmpty()) exp &= _.Name == name;
        if (hits >= 0) exp &= _.Hits == hits;
        if (show != null) exp &= _.Show == show;
        exp &= _.UpdateTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= SearchWhereByKeys(key);

        return FindAll(exp, page);
    }

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="name"></param>
    /// <param name="HId">帮助分类Id</param>
    /// <param name="page"></param>
    /// <param name="selects">选择字段</param>
    /// <returns></returns>
    public static IEnumerable<Helps> Searchs(String name, Int32 HId, PageParameter page, String? selects = null)
    {
        var exp = new WhereExpression();

        if (HId > 0) exp &= _.HId == HId;
        if (!name.IsNullOrEmpty()) exp &= _.Name.Contains(name) | _.Url.Contains(name);
        return FindAll(exp, page, selects);
    }

    /// <summary>
    /// 根据帮助分类Id和语言Id获取帮助列表
    /// </summary>
    /// <param name="HId"></param>
    /// <param name="LId"></param>
    /// <returns></returns>
    public static List<Helps> FindAllByCategoryIdAndLan(Int32 HId,Int32 LId)
    {
        var list = FindAllByHId(HId).Select(e=>new Helps
        {
            Id = e.Id,
            Url = e.Url,
            Show = e.Show,
            Name = HelpsLan.FindByHelps(e,LId).Name,
        }).ToList();
        return list;
    }

    // Select Count(Id) as Id,Category From DH_Helps Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<Helps> _CategoryCache = new(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IHelps ToModel()
    {
        var model = new Helps();
        model.Copy(this);

        return model;
    }

    /// <summary>
    /// 根据ID集合删除数据
    /// </summary>
    /// <param name="Ids">ID集合</param>
    public static void DelByIds(String Ids)
    {
        if (Delete(_.Id.In(Ids.Trim(','))) > 0)
            Meta.Cache.Clear("");
    }

    #endregion
}
