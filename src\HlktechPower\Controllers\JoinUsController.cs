﻿using HlktechPower.Entity;
using Microsoft.AspNetCore.Mvc;
using Pek.NCube;

namespace HlktechPower.Controllers
{
    /// <summary>
    /// 加入我们
    /// </summary>
    public class JoinUsController : PekBaseControllerX
    {
        public IActionResult Index()
        {
            var model = SingleArticle.FindByCode("JoinUs");
            if (model == null)
                return Content(GetResource("文章内容不存在"));

            var modelLan = SingleArticleLan.FindBySIdAndLId(model.Id, WorkingLanguage.Id);

            if (!string.IsNullOrEmpty(modelLan?.Content))
                model.Content = modelLan.Content;

            return View(model);
        }
    }
}
