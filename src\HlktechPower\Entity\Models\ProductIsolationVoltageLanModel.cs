﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechPower.Entity;

/// <summary>产品隔离电压翻译表</summary>
public partial class ProductIsolationVoltageLanModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>产品隔离电压Id</summary>
    public Int32 IId { get; set; }

    /// <summary>所属语言Id</summary>
    public Int32 LId { get; set; }

    /// <summary>名称</summary>
    public String? Name { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductIsolationVoltageLan model)
    {
        Id = model.Id;
        IId = model.IId;
        LId = model.LId;
        Name = model.Name;
    }
    #endregion
}
