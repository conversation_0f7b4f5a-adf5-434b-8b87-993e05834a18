﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechPower.Entity;

/// <summary>应用视频翻译表</summary>
public partial class AppVideoLanModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>应用视频Id</summary>
    public Int32 AId { get; set; }

    /// <summary>所属语言Id</summary>
    public Int32 LId { get; set; }

    /// <summary>标题</summary>
    public String? Name { get; set; }

    /// <summary>链接</summary>
    public String? Url { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IAppVideoLan model)
    {
        Id = model.Id;
        AId = model.AId;
        LId = model.LId;
        Name = model.Name;
        Url = model.Url;
    }
    #endregion
}
