﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechPower.Entity;

/// <summary>帮助分类</summary>
[Serializable]
[DataObject]
[Description("帮助分类")]
[BindIndex("IX_DH_HelpsCategory_ParentId", false, "ParentId")]
[BindIndex("IX_DH_HelpsCategory_Id_Level", false, "Id,Level")]
[BindIndex("IU_DH_HelpsCategory_Name_Level", true, "Name,Level")]
[BindTable("DH_HelpsCategory", Description = "帮助分类", ConnName = "Pek", DbType = DatabaseType.None)]
public partial class HelpsCategory : IHelpsCategory, IEntity<IHelpsCategory>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _Name;
    /// <summary>分类名称</summary>
    [DisplayName("分类名称")]
    [Description("分类名称")]
    [DataObjectField(false, false, true, 30)]
    [BindColumn("Name", "分类名称", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private Int32 _ParentId;
    /// <summary>所属父级Id</summary>
    [DisplayName("所属父级Id")]
    [Description("所属父级Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ParentId", "所属父级Id", "")]
    public Int32 ParentId { get => _ParentId; set { if (OnPropertyChanging("ParentId", value)) { _ParentId = value; OnPropertyChanged("ParentId"); } } }

    private String? _ParentIdList;
    /// <summary>父级Id集合</summary>
    [DisplayName("父级Id集合")]
    [Description("父级Id集合")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("ParentIdList", "父级Id集合", "")]
    public String? ParentIdList { get => _ParentIdList; set { if (OnPropertyChanging("ParentIdList", value)) { _ParentIdList = value; OnPropertyChanged("ParentIdList"); } } }

    private Int32 _Level;
    /// <summary>当前层级</summary>
    [DisplayName("当前层级")]
    [Description("当前层级")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Level", "当前层级", "")]
    public Int32 Level { get => _Level; set { if (OnPropertyChanging("Level", value)) { _Level = value; OnPropertyChanged("Level"); } } }

    private Int16 _DisplayOrder;
    /// <summary>排序</summary>
    [DisplayName("排序")]
    [Description("排序")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("DisplayOrder", "排序", "")]
    public Int16 DisplayOrder { get => _DisplayOrder; set { if (OnPropertyChanging("DisplayOrder", value)) { _DisplayOrder = value; OnPropertyChanged("DisplayOrder"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IHelpsCategory model)
    {
        Id = model.Id;
        Name = model.Name;
        ParentId = model.ParentId;
        ParentIdList = model.ParentIdList;
        Level = model.Level;
        DisplayOrder = model.DisplayOrder;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Name" => _Name,
            "ParentId" => _ParentId,
            "ParentIdList" => _ParentIdList,
            "Level" => _Level,
            "DisplayOrder" => _DisplayOrder,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "ParentId": _ParentId = value.ToInt(); break;
                case "ParentIdList": _ParentIdList = Convert.ToString(value); break;
                case "Level": _Level = value.ToInt(); break;
                case "DisplayOrder": _DisplayOrder = Convert.ToInt16(value); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static HelpsCategory? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据所属父级Id查找</summary>
    /// <param name="parentId">所属父级Id</param>
    /// <returns>实体列表</returns>
    public static IList<HelpsCategory> FindAllByParentId(Int32 parentId)
    {
        if (parentId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.ParentId == parentId);

        return FindAll(_.ParentId == parentId);
    }

    /// <summary>根据编号、当前层级查找</summary>
    /// <param name="id">编号</param>
    /// <param name="level">当前层级</param>
    /// <returns>实体列表</returns>
    public static IList<HelpsCategory> FindAllByIdAndLevel(Int32 id, Int32 level)
    {
        if (id < 0) return [];
        if (level < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Id == id && e.Level == level);

        return FindAll(_.Id == id & _.Level == level);
    }

    /// <summary>根据分类名称、当前层级查找</summary>
    /// <param name="name">分类名称</param>
    /// <param name="level">当前层级</param>
    /// <returns>实体对象</returns>
    public static HelpsCategory? FindByNameAndLevel(String? name, Int32 level)
    {
        if (name == null) return null;
        if (level < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Name.EqualIgnoreCase(name) && e.Level == level);

        return Find(_.Name == name & _.Level == level);
    }

    /// <summary>根据分类名称查找</summary>
    /// <param name="name">分类名称</param>
    /// <returns>实体列表</returns>
    public static IList<HelpsCategory> FindAllByName(String? name)
    {
        if (name == null) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Name.EqualIgnoreCase(name));

        return FindAll(_.Name == name);
    }
    #endregion

    #region 字段名
    /// <summary>取得帮助分类字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>分类名称</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>所属父级Id</summary>
        public static readonly Field ParentId = FindByName("ParentId");

        /// <summary>父级Id集合</summary>
        public static readonly Field ParentIdList = FindByName("ParentIdList");

        /// <summary>当前层级</summary>
        public static readonly Field Level = FindByName("Level");

        /// <summary>排序</summary>
        public static readonly Field DisplayOrder = FindByName("DisplayOrder");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得帮助分类字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>分类名称</summary>
        public const String Name = "Name";

        /// <summary>所属父级Id</summary>
        public const String ParentId = "ParentId";

        /// <summary>父级Id集合</summary>
        public const String ParentIdList = "ParentIdList";

        /// <summary>当前层级</summary>
        public const String Level = "Level";

        /// <summary>排序</summary>
        public const String DisplayOrder = "DisplayOrder";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
