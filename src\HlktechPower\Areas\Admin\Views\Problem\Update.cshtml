﻿@using HlktechPower.Entity
@{
    var localizationSettings = LocalizationSettings.Current;

}
<style asp-location="true">
    .page {
    min-height: 415px
    }

    .layui-tab-brief > .layui-tab-title .layui-this {
    color: #419DFD !important;
    }

    .layui-tab-brief > .layui-tab-more li.layui-this:after,
    .layui-tab-brief > .layui-tab-title .layui-this:after {
    border-bottom: 2px solid #419DFD !important;
    }

    .layui-tab-content {
    padding: 0px !important;
    }

    .bd {
    border: 2px solid red;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("问题管理")</h3>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="javascript:void(0)" class="current"><span>@T("回复")</span></a></li>
            </ul>
        </div>
    </div>

    <div class="explanation" id="explanation">
    </div>

    <form id="goods_class_form" name="goodsClassForm" enctype="multipart/form-data" method="post">
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <table class="ds-default-table">
                        <tbody>
                            <tr class="noborder">
                                <td colspan="2" class="">
                                    <label class="gc_name"
                                    for="Name">@T("姓名"):</label>
                                </td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td colspan="2" class="vatop rowform">
                                    <input type="text" maxlength="50" value="@Model.Name" disabled
                                    name="Name" id="Name" class="txt">
                                </td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td colspan="2" class="">
                                    <label class="gc_name "
                                    for="Mail">@T("邮箱"):</label>
                                </td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td colspan="2" class="vatop rowform">
                                    <input type="text" maxlength="50" value="@Model.Mail" disabled
                                    name="Mail" id="Mail" class="txt">
                                </td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td colspan="2" class="">
                                    <label class="gc_name"
                                    for="Phone">@T("手机"):</label>
                                </td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td colspan="2" class="vatop rowform">
                                    <input type="text" maxlength="50" value="@Model.Phone" disabled
                                    name="Phone" id="Phone" class="txt">
                                </td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td colspan="2" class="">
                                    <label class="gc_name"
                                    for="Address">@T("联系地址"):</label>
                                </td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td colspan="2" class="vatop rowform">
                                    <input type="text" value="@Model.Address" disabled
                                    name="Address" id="Address" class="txt">
                                </td>
                                <td class="vatop tips"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td colspan="2" class="">
                        <label class=""
                        for="dataFileClass">@T("类型"):</label>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td colspan="2" class="vatop rowform">
                        <select name="dataFileClass" disabled>
                            <!option value="0" @(Model.ProblemType == 0 ? "selected" : "")>@T("网站问题")</!option>
                            <!option value="1" @(Model.ProblemType == 1 ? "selected" : "")>@T("产品问题")</!option>
                            <!option value="2" @(Model.ProblemType == 2 ? "selected" : "")>@T("技术问题")</!option>
                            <!option value="3" @(Model.ProblemType == 3 ? "selected" : "")>@T("其他问题")</!option>
                        </select>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                @if(Model.PId > 0)
                {
                    <tr class="noborder">
                        <td colspan="2" class="">
                            <label class="gc_name"
                                   for="Address">@T("产品"):</label>
                        </td>
                        <td class="vatop tips"></td>
                    </tr>
                    <tr class="noborder">
                        <td colspan="2" class="vatop rowform">
                            <input type="text" value="@Model.ProductClass.Name" disabled
                                   name="PId" id="PId" class="txt">
                        </td>
                        <td class="vatop tips"></td>
                    </tr>
                }
                <tr class="noborder">
                    <td colspan="2" class="">
                        <label class=""
                               for="Content">@T("问题"):</label>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td colspan="2" class="vatop rowform">
                        <textarea type="text" name="Content" id="Content" class="txt" style="height:200px" disabled>@Model.Content</textarea>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td colspan="2" class="">
                        <label class=""
                               for="Content">@T("回复"):</label>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td colspan="2" class="vatop rowform">
                        <textarea type="text" name="Answer" id="Answer" class="txt" style="height:200px">@Model.Answer</textarea>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
            </tbody>
            <tfoot>
                <tr><td><input class="text" hidden name="Id" value="@Model.Id" /></td></tr>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<script type="text/javascript" src="/static/plugins/mlselection.js"></script>
<script type="text/javascript" src="/static/plugins/jquery.mousewheel.js"></script>
<script asp-location="Footer">

</script>