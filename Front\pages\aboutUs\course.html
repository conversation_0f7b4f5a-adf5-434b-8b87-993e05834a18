<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电源网</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <!-- 第三方库js -->
    <script src="../../modules/jq.js"></script>
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/media.css">
    <link rel="stylesheet" href="../../css/mySidebar.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="../../css/pageCss/course.css">
    <!-- 综合-script -->
    <script src="../../script/index.js" defer></script>
    <!-- 产品分类模块 -->
    <script src="../../script/productCategory.js" defer></script>
    <!-- 所有页面组成部分 -->
    <script src="../../components/common.js" defer></script>
    <script src="../../components/mySidebar.js" defer></script>
    <!-- 单页面组成部分 -->
    <!-- <script src="./components/page.js" defer></script> -->
</head>

<body>
    <!-- 引入头部组件 -->
    <div id="header"></div>

    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="./providerDetail.html">企业历程</div>
        </div>
        <div class="myContent">
            <!-- 侧边栏 -->
            <div class="mySidebar"></div>

            <div class="introBox">
                <h2>企业历程</h2>
                <div class="layui-timeline">
                    <div class="layui-timeline-item">
                        <i class="layui-icon layui-timeline-axis">&#xe619;</i>
                        <div class="layui-timeline-content layui-text">
                            <div class="layui-timeline-title">&nbsp;</div>
                        </div>
                    </div>
                    <div class="layui-timeline-item">
                        <i class="layui-icon layui-timeline-axis"></i>
                        <div class="layui-timeline-content layui-text">
                            <h3 class="layui-timeline-title">2019年</h3>
                            <p>
                                武汉分公司成立
                                <br>
                                电源模组全系列产品多达100余款
                                <br>
                                公司进入蓝牙模组和lora模组市场
                            </p>
                        </div>
                    </div>
                    <div class="layui-timeline-item">
                        <i class="layui-icon layui-timeline-axis"></i>
                        <div class="layui-timeline-content layui-text">
                            <h3 class="layui-timeline-title">2018年</h3>
                            <p>成为阿里云金牌合作商，电源模块系列产品20余款量产<br>印度分公司成立</p>

                        </div>
                    </div>
                    <div class="layui-timeline-item">
                        <i class="layui-icon layui-timeline-axis"></i>
                        <div class="layui-timeline-content layui-text">
                            <h3 class="layui-timeline-title">2017年</h3>
                            <div class="layui-timeline-title">
                                <p>MTK全系列产品量产,型号多达30余款，<br>月出货量突破500K，自建加工厂严格把控产品质量</p>
                            </div>
                        </div>
                    </div>
                    <div class="layui-timeline-item">
                        <i class="layui-icon layui-timeline-axis"></i>
                        <div class="layui-timeline-content layui-text">
                            <h3 class="layui-timeline-title">2016年</h3>
                            <div class="layui-timeline-title">
                                <p>荣获国家高新技术企业认证，前海股权中心挂牌 <br>(股票代码:668537)</p>
                            </div>
                        </div>
                    </div>
                    <div class="layui-timeline-item">
                        <i class="layui-icon layui-timeline-axis"></i>
                        <div class="layui-timeline-content layui-text">
                            <h3 class="layui-timeline-title">2014年</h3>
                            <div class="layui-timeline-title">
                                <p>RM10、M30和M35物联网串口WiFi模块量产</p>
                            </div>
                        </div>
                    </div>
                    <div class="layui-timeline-item">
                        <i class="layui-icon layui-timeline-axis"></i>
                        <div class="layui-timeline-content layui-text">
                            <h3 class="layui-timeline-title">2013年</h3>
                            <div class="layui-timeline-title">
                                <p>RM04引领物联网应用市场，出货量屡创新高</p>
                            </div>
                        </div>
                    </div>
                    <div class="layui-timeline-item">
                        <i class="layui-icon layui-timeline-axis"></i>
                        <div class="layui-timeline-content layui-text">
                            <h3 class="layui-timeline-title">2012年</h3>
                            <div class="layui-timeline-title">
                                <p>自主研发的RMO4物联网路由模组量产</p>
                            </div>
                        </div>
                    </div>
                    <div class="layui-timeline-item">
                        <i class="layui-icon layui-timeline-axis"></i>
                        <div class="layui-timeline-content layui-text">
                            <h3 class="layui-timeline-title">2011年</h3>
                            <div class="layui-timeline-title">
                                <p>串口WiFi模组占华大整体WiFi模组出货量的50%</p>
                            </div>
                        </div>
                    </div>
                    <div class="layui-timeline-item">
                        <i class="layui-icon layui-timeline-axis"></i>
                        <div class="layui-timeline-content layui-text">
                            <h3 class="layui-timeline-title">2010年4月</h3>
                            <div class="layui-timeline-title">
                                <p>签下北京中电华大WiFi正式代理</p>
                            </div>
                        </div>
                    </div>
                    <div class="layui-timeline-item">
                        <i class="layui-icon layui-timeline-axis"></i>
                        <div class="layui-timeline-content layui-text">
                            <h3 class="layui-timeline-title">2009年10月</h3>
                            <div class="layui-timeline-title">
                                <p>分销北京中电华大串口WiFi模块</p>
                            </div>
                        </div>
                    </div>
                    <div class="layui-timeline-item">
                        <i class="layui-icon layui-timeline-axis"></i>
                        <div class="layui-timeline-content layui-text">
                            <h3 class="layui-timeline-title">2009年9月</h3>
                            <div class="layui-timeline-title">
                                <p>深圳海凌科电子科技有限公司注册成立</p>
                            </div>
                        </div>
                    </div>









                </div>

            </div>




        </div>
    </div>

    <div class="bug"></div>

    <!-- 引入底部组件 -->
    <div id="footer"></div>
</body>
<script>
    const data = [
        { text: '关于我们', link: '#' },  // 标题
        { text: '企业简介', link: './intro.html' },
        { text: '荣誉资质', link: './honor.html' },
        { text: '企业历程', link: './course.html' },
        { text: '企业文化', link: './culture.html' },

    ];


</script>

</html>