.company-intro {
    border-bottom: 1px solid var(--line);
}

.company-intro h3 {
    font-size: 1.4rem;
    font-weight: 400;
    color: var(--text-color);
    margin-bottom: 1.3vw;
}

.intro-content {
    display: flex;
    gap: 2.6vw;
    margin-bottom: 40px;
    align-items: flex-start;
}

.intro-img {
    flex: 0 0 25vw;
    max-width: 25vw;
    height: auto; 
    overflow: hidden; 
    
}
.intro-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.intro-text {
    flex: 1;
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-color3);
}



.intro-stats {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 2vw; 
    margin-top: 1.4vw;
}

.stat-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: row;
    text-align: left;
    flex: none;
    padding: 0;
    background-color: transparent;
    border-radius: 0;
    box-shadow: none;
}

.stat-item img {
    width: 2.4vw;
    height: 36px;
    object-fit: contain;
    margin-right: 1vw;
    margin-bottom: 0;
}

.stat-item .text-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.stat-item p {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 2px;
}

.stat-item span {
    font-size: 1.2rem;
    color: var(--text-color3);
}

.company-advantages {
    width: 100%;
    max-width: 1200px;
    margin-top: 1.3vw;
}

.company-advantages h2 {
    font-size: 1.4rem;
    color: var(--text-color);
    margin-bottom: 1.3vw;
}

.company-advantages p {
    font-size: 1.2rem;
    line-height: 1.8;
    color: var(--text-color3);
    margin-bottom: 2.6vw;
}

.advantages-cards {
    display: flex;
    justify-content: space-around;
    gap: 1.3vw;
}

.advantage-card {
    flex: 0 0 calc(33.333% - 40px/3);
    max-width: calc(33.333% - 40px/3);
    height: 524px;
    box-sizing: border-box;
    border: 1px solid var(--line);
    border-radius: 5px;
    text-align: center;
    background-color: var(--white);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}



.advantage-card img {
    width: 100%;
    height: 340px;
    object-fit: over;
    margin-bottom: 1.3vw;
}

.advantage-card h3 {
    font-size: 1.3rem;
    font-weight: 400;
    color: var(--text-color);
    margin-bottom: .7vw;
}

.advantage-card p {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color3);
    text-align: left;
    margin: 0 .7vw;
}


