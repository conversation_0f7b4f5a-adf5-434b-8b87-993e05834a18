/* 常见问题 */
.item2 li {
  display: flex;
  gap: 10px;
  padding: 10px 140px 10px 20px;
  border-bottom: 1px solid var(--line);
  font-size: 14px;
  justify-content: flex-start;
  flex-direction: column;
}

.item2 li:hover {
  background-color: var(--myBlueBg);
}

.li-left {
  display: flex;
  gap: 20px;
  align-items: center;
}

.li-right {
  display: flex;
  align-items: center;
  margin-left: 36px;
  color: var(--text-color3);
}

.li-right span {
  color: var(--text-color);
}

.layui-tab-brief>.layui-tab-title .layui-this {
  color: var(--myBlue);
}