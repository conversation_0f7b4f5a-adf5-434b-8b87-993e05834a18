﻿@{
    var localizationSettings = LocalizationSettings.Current;

}
<style asp-location="true">
    .page {
        min-height: 415px
    }

    .layui-tab-brief > .layui-tab-title .layui-this {
        color: #419DFD !important;
    }

        .layui-tab-brief > .layui-tab-more li.layui-this:after,
        .layui-tab-brief > .layui-tab-title .layui-this:after {
            border-bottom: 2px solid #419DFD !important;
        }

    .layui-tab-content {
        padding: 0px !important;
    }

    .bd {
        border: 2px solid red;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("应用视频")</h3>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="javascript:void(0)" class="current"><span>@T("新增")</span></a></li>
            </ul>
        </div>
    </div>

    <div class="explanation" id="explanation">
    </div>

    <form id="goods_class_form" name="goodsClassForm" enctype="multipart/form-data" method="post">
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准"):</li>
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)Model.listLanguage)
                    {
                        <li>@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <table class="ds-default-table">
                        <tbody>
                            <tr class="noborder">
                                <td colspan="2" class="required">
                                    <label class="gc_name validation"
                                           for="Name">@T("标题"):</label>
                                </td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td colspan="2" class="vatop rowform">
                                    <input type="text" maxlength="50" value=""
                                           name="Name" id="Name" class="txt">
                                </td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td colspan="2" class="required"><label for="pic">@T("视频"):</label></td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td class="vatop rowform">
                                    <span class="type-file-box">
                                        <input type='text' id='textfield1' class='type-file-text' value="" />
                                        <input type='button' name='button' id='button1' value='@T("上传")'
                                               class='type-file-button' />
                                        <input name="claPic" type="file" class="type-file-file" id="pic" size="30"
                                               hidefocus="true" ds_type="change_pic">
                                    </span>
                                </td>
                                <td>
                                </td>
                                <td class="vatop tips">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)Model.listLanguage)
                    {
                        <div class="layui-tab-item">
                            <table class="ds-default-table">
                                <tbody>
                                    <tr class="noborder">
                                        <td colspan="2" class="required">
                                            <label class="gc_name validation"
                                                   for="[@item.Id].Name">@T("标题"):</label>
                                        </td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="vatop rowform">
                                            <input type="text" value="" name="[@item.Id].Name"
                                                   id="[@item.Id].Name" class="txt">
                                        </td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td colspan="2" class="required"><label for="pic">@T("视频"):</label></td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="vatop rowform">
                                            <span class="type-file-box">
                                                <input type='text' id='<EMAIL>' value=""
                                                       class='type-file-text' />
                                                <input type='button' name='button' id='button1' value='@T("上传")'
                                                       class='type-file-button' />
                                                <input name="[@item.Id].claPic" type="file" class="type-file-file" id="<EMAIL>" onchange="change_pic(this,'@item.Id')"
                                                       size="30" hidefocus="true" ds_type="change_pic">
                                            </span>
                                        </td>
                                        <td>
                                        </td>
                                        <td class="vatop tips">
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    }
                }
            </div>
        </div>
        <table class="ds-default-table">
            <tbody>
                <tr class="noborder">
                    <td colspan="2" class="">
                        <label class=""
                               for="Sort">@T("排序"):</label>
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td colspan="2" class="vatop rowform">
                        <input type="text" maxlength="20" value=""
                               name="Sort" id="Sort" class="txt">
                    </td>
                    <td class="vatop tips"></td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<script type="text/javascript" src="/static/plugins/mlselection.js"></script>
<script type="text/javascript" src="/static/plugins/jquery.mousewheel.js"></script>
<script asp-location="Footer">
    $('#pic').change(function (event) {
        const preview = document.getElementById('previewImage')
        // 获取选中的文件
        const file = event.target.files[0];
        // 检查是否选择了文件
        if (file) {
            // 创建一个URL对象
            const imageUrl = URL.createObjectURL(file);
            $('#textfield1').val(imageUrl);
            // console.log(preview,imageUrl);
            // 将URL赋值给img标签的src属性
            preview.src = imageUrl;
        }
    })
     // 多语言选中上传图片
     function change_pic(event,id) {
        // console.log(id);
        // 获取选中的文件
        const file = event.files[0];
        // 检查是否选择了文件
        if (file) {
            // 创建一个URL对象
            const imageUrl = URL.createObjectURL(file);
            $('#_previewImage_' + id).attr('src', imageUrl);
            $('#_textfield_' + id).val(imageUrl);
            // console.log($('#_textfield_' + id).val(),$('#_picImageBox_' + id));
        }
    }
</script>