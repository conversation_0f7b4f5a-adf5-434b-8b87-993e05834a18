.myContent-right .icon-search,
.wen {
  color: var(--white);
}

.layui-tab-brief>.layui-tab-title .layui-this {
  color: var(--white);
  background-color: var(--myBlue);

}

.layui-tab .layui-tab-title li {
  margin: 0 20px;
  border: 1px solid var(--line);
}

.layui-tab-content {
  margin-top: 20px;
}

.layui-tab-content2 {
  margin-top: 10px;
}

.layui-tab .layui-tab-title:after {
  bottom: -20px;
  border-bottom-color: var(--myBlue);
  border-bottom-width: 2px;
}

.item li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 140px 10px 40px;
  border-bottom: 1px solid var(--line);
  font-size: 14px;
}

.item li:first-child {
  padding: 15px 140px 15px 40px;
}

/* 取消下划线 */
.layui-tab-title .layui-this:after {
  width: 0;
}