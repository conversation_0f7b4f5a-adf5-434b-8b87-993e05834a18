.videoBox {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 20px;
}
 /* 隐藏默认的播放/暂停按钮和其他控件 */
 video::-webkit-media-controls-play-button,
 video::-webkit-media-controls-pause-button,
 video::-webkit-media-controls-timeline,
 video::-webkit-media-controls-current-time-display,
 video::-webkit-media-controls-time-remaining-display,
 video::-webkit-media-controls-mute-button,
 video::-webkit-media-controls-volume-slider,
 video::-webkit-media-controls-fullscreen-button {
   display: none;
 }

 /* 播放时显示控件 */
 .videoItem.playing video::-webkit-media-controls-timeline,
 .videoItem.playing video::-webkit-media-controls-current-time-display,
 .videoItem.playing video::-webkit-media-controls-time-remaining-display,
 .videoItem.playing video::-webkit-media-controls-mute-button,
 .videoItem.playing video::-webkit-media-controls-volume-slider,
 .videoItem.playing video::-webkit-media-controls-fullscreen-button {
   display: block;
 }


 /* 确保视频区域可以接收点击事件 */
 .videoItem.playing video {
   pointer-events: auto;
 }

 /* 针对 WebKit 浏览器 (Chrome, Safari, Edge) - 不播放时透明背景 */
 video::-webkit-media-controls-panel {
   background: transparent !important;
 }

 /* 针对 Firefox - 不播放时透明背景 */
 video::-moz-media-controls-panel {
   background: transparent !important;
 }

 /* 播放时使用渐变背景 - 更高优先级 */
 .videoItem.playing video::-webkit-media-controls-panel {
   background: linear-gradient(to bottom, transparent 0%, transparent 50%, rgba(0, 0, 0, 0.1) 60%, rgba(0, 0, 0, 0.4) 80%, rgba(0, 0, 0, 0.8) 100%) !important;
 }

 .videoItem.playing video::-moz-media-controls-panel {
   background: linear-gradient(to bottom, transparent 0%, transparent 50%, rgba(0, 0, 0, 0.1) 60%, rgba(0, 0, 0, 0.4) 80%, rgba(0, 0, 0, 0.8) 100%) !important;
 }

 /* 确保控件可以正常交互 */
 .videoItem.playing video::-webkit-media-controls-timeline,
 .videoItem.playing video::-webkit-media-controls-mute-button,
 .videoItem.playing video::-webkit-media-controls-volume-slider,
 .videoItem.playing video::-webkit-media-controls-fullscreen-button {
   pointer-events: auto;
 }

.videoItem {
  width: 31%;
  background: #f0f0f0;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 5px;
  position: relative;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.4);
}

.videoItem video {
  width: 100%;
  height: 180px;
  /* object-fit: cover; */
  cursor: pointer;
}




.videoItem .play-button {
  position: absolute;
  top: 36%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.videoItem .play-button:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: translate(-50%, -50%) scale(1.1);
}

.videoItem .play-button::after {
  content: '';
  width: 20px;
  height: 20px;
  background: #ffffff;
  margin-left: 5px;
  clip-path: polygon(15% 10%, 15% 90%, 85% 50%);
  border-radius: 10px;
}

/* 暂停按钮样式 */
.videoItem .pause-button {
  position: absolute;
  top: 36%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: none;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0;
  z-index: 10;
}

.videoItem .pause-button:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: translate(-50%, -50%) scale(1.1);
}

.videoItem .pause-button::before,
.videoItem .pause-button::after {
  content: '';
  width: 6px;
  height: 20px;
  background: var(--white);
  border-radius: 2px;
  position: absolute;
}

.videoItem .pause-button::before {
  left: 22px;
}

.videoItem .pause-button::after {
  right: 22px;
}

.title{
  font-size: 16px;
  font-weight: 500;
  margin-top: 10px;
  margin-left: 10px;

}
.num{
  text-align: right;
  color: var(--text-color3);
  margin-right: 20px;
  margin-bottom: 10px;
}