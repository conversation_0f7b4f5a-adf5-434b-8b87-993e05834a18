﻿using DH.Entity;
using HlktechPower.Common;
using NewLife.Log;

using Pek.Events;
using Pek.NCube.Events.EventModel;

using XCode;

namespace HlktechPower.Events;

/// <summary>
/// 初始化区域数据事件消费者
/// </summary>
public class InitRegionDataEventConsumer
    : IConsumer<InitRegionDataEvent> {

    public Int32 Sort { get; set; } = 0;

    public async Task HandleEventAsync(InitRegionDataEvent eventMessage)
    {
        RegionChinaHelper.Init();

        XTrace.WriteLine($"进来执行国外区域了么？");
        var list = new List<Regions>
        {
            new() { CId = "AF", Name = "Badakhshan" },
            new() { CId = "AF", Name = "Badghis" },
            new() { CId = "AF", Name = "Baghlan" },
            new() { CId = "AF", Name = "Balkh" },
            new() { CId = "AF", Name = "Bamian" },
            new() { CId = "AF", Name = "Farah" },
            new() { CId = "AF", Name = "Faryab" },
            new() { CId = "AF", Name = "Ghazni" },
            new() { CId = "AF", Name = "Ghowr" },
            new() { CId = "AF", Name = "Helmand" },
            new() { CId = "AF", Name = "Herat" },
            new() { CId = "AF", Name = "Jowzjan" },
            new() { CId = "AF", Name = "Kabul" },
            new() { CId = "AF", Name = "Kandahar" },
            new() { CId = "AF", Name = "Kapisa" },
            new() { CId = "AF", Name = "Khost" },
            new() { CId = "AF", Name = "Konar" },
            new() { CId = "AF", Name = "Kondoz" },
            new() { CId = "AF", Name = "Laghman" },
            new() { CId = "AF", Name = "Lowgar" },
            new() { CId = "AF", Name = "Nangrahar" },
            new() { CId = "AF", Name = "Nimruz" },
            new() { CId = "AF", Name = "Nurestan" },
            new() { CId = "AF", Name = "Oruzgan" },
            new() { CId = "AF", Name = "Paktia" },
            new() { CId = "AF", Name = "Paktika" },
            new() { CId = "AF", Name = "Parwan" },
            new() { CId = "AF", Name = "Samangan" },
            new() { CId = "AF", Name = "Sar-e Pol" },
            new() { CId = "AF", Name = "Takhar" },
            new() { CId = "AF", Name = "Wardak" },
            new() { CId = "AF", Name = "Zabol" },
            new() { CId = "AL", Name = "Berat" },
            new() { CId = "AL", Name = "Bulqize" },
            new() { CId = "AL", Name = "Delvine" },
            new() { CId = "AL", Name = "Devoll" },
            new() { CId = "AL", Name = "Diber" },
            new() { CId = "AL", Name = "Durres" },
            new() { CId = "AL", Name = "Elbasan" },
            new() { CId = "AL", Name = "Kolonje" },
            new() { CId = "AL", Name = "Fier" },
            new() { CId = "AL", Name = "Gjirokaster" },
            new() { CId = "AL", Name = "Gramsh" },
            new() { CId = "AL", Name = "Has" },
            new() { CId = "AL", Name = "Kavaje" },
            new() { CId = "AL", Name = "Kurbin" },
            new() { CId = "AL", Name = "Kucove" },
            new() { CId = "AL", Name = "Korce" },
            new() { CId = "AL", Name = "Kruje" },
            new() { CId = "AL", Name = "Kukes" },
            new() { CId = "AL", Name = "Librazhd" },
            new() { CId = "AL", Name = "Lezhe" },
            new() { CId = "AL", Name = "Lushnje" },
            new() { CId = "AL", Name = "Malesi e Madhe" },
            new() { CId = "AL", Name = "Mallakaster" },
            new() { CId = "AL", Name = "Mat" },
            new() { CId = "AL", Name = "Mirdite" },
            new() { CId = "AL", Name = "Peqin" },
            new() { CId = "AL", Name = "Permet" },
            new() { CId = "AL", Name = "Pogradec" },
            new() { CId = "AL", Name = "Puke" },
            new() { CId = "AL", Name = "Shkoder" },
            new() { CId = "AL", Name = "Skrapar" },
            new() { CId = "AL", Name = "Sarande" },
            new() { CId = "AL", Name = "Tepelene" },
            new() { CId = "AL", Name = "Tropoje" },
            new() { CId = "AL", Name = "Tirane" },
            new() { CId = "AL", Name = "Vlore" },
            new() { CId = "DZ", Name = "Adrar" },
            new() { CId = "DZ", Name = "Ain Defla" },
            new() { CId = "DZ", Name = "Ain Temouchent" },
            new() { CId = "DZ", Name = "Alger" },
            new() { CId = "DZ", Name = "Annaba" },
            new() { CId = "DZ", Name = "Batna" },
            new() { CId = "DZ", Name = "Bechar" },
            new() { CId = "DZ", Name = "Bejaia" },
            new() { CId = "DZ", Name = "Biskra" },
            new() { CId = "DZ", Name = "Blida" },
            new() { CId = "DZ", Name = "Bordj Bou Arreridj" },
            new() { CId = "DZ", Name = "Bouira" },
            new() { CId = "DZ", Name = "Boumerdes" },
            new() { CId = "DZ", Name = "Chlef" },
            new() { CId = "DZ", Name = "Constantine" },
            new() { CId = "DZ", Name = "Djelfa" },
            new() { CId = "DZ", Name = "El Bayadh" },
            new() { CId = "DZ", Name = "El Oued" },
            new() { CId = "DZ", Name = "El Tarf" },
            new() { CId = "DZ", Name = "Ghardaia" },
            new() { CId = "DZ", Name = "Guelma" },
            new() { CId = "DZ", Name = "Illizi" },
            new() { CId = "DZ", Name = "Jijel" },
            new() { CId = "DZ", Name = "Khenchela" },
            new() { CId = "DZ", Name = "Laghouat" },
            new() { CId = "DZ", Name = "Muaskar" },
            new() { CId = "DZ", Name = "Medea" },
            new() { CId = "DZ", Name = "Mila" },
            new() { CId = "DZ", Name = "Mostaganem" },
            new() { CId = "DZ", Name = "M'Sila" },
            new() { CId = "DZ", Name = "Naama" },
            new() { CId = "DZ", Name = "Oran" },
            new() { CId = "DZ", Name = "Ouargla" },
            new() { CId = "DZ", Name = "Oum el-Bouaghi" },
            new() { CId = "DZ", Name = "Relizane" },
            new() { CId = "DZ", Name = "Saida" },
            new() { CId = "DZ", Name = "Setif" },
            new() { CId = "DZ", Name = "Sidi Bel Abbes" },
            new() { CId = "DZ", Name = "Skikda" },
            new() { CId = "DZ", Name = "Souk Ahras" },
            new() { CId = "DZ", Name = "Tamanghasset" },
            new() { CId = "DZ", Name = "Tebessa" },
            new() { CId = "DZ", Name = "Tiaret" },
            new() { CId = "DZ", Name = "Tindouf" },
            new() { CId = "DZ", Name = "Tipaza" },
            new() { CId = "DZ", Name = "Tissemsilt" },
            new() { CId = "DZ", Name = "Tizi Ouzou" },
            new() { CId = "DZ", Name = "Tlemcen" },
            new() { CId = "AS", Name = "Eastern" },
            new() { CId = "AS", Name = "Manu'a" },
            new() { CId = "AS", Name = "Rose Island" },
            new() { CId = "AS", Name = "Swains Island" },
            new() { CId = "AS", Name = "Western" },
            new() { CId = "AD", Name = "Andorra la Vella" },
            new() { CId = "AD", Name = "Canillo" },
            new() { CId = "AD", Name = "Encamp" },
            new() { CId = "AD", Name = "Escaldes-Engordany" },
            new() { CId = "AD", Name = "La Massana" },
            new() { CId = "AD", Name = "Ordino" },
            new() { CId = "AD", Name = "Sant Julia de Loria" },
            new() { CId = "AO", Name = "Bengo" },
            new() { CId = "AO", Name = "Benguela" },
            new() { CId = "AO", Name = "Bie" },
            new() { CId = "AO", Name = "Cabinda" },
            new() { CId = "AO", Name = "Cuando-Cubango" },
            new() { CId = "AO", Name = "Cuanza Norte" },
            new() { CId = "AO", Name = "Cuanza Sul" },
            new() { CId = "AO", Name = "Cunene" },
            new() { CId = "AO", Name = "Huambo" },
            new() { CId = "AO", Name = "Huila" },
            new() { CId = "AO", Name = "Luanda" },
            new() { CId = "AO", Name = "Lunda Norte" },
            new() { CId = "AO", Name = "Lunda Sul" },
            new() { CId = "AO", Name = "Malange" },
            new() { CId = "AO", Name = "Moxico" },
            new() { CId = "AO", Name = "Namibe" },
            new() { CId = "AO", Name = "Uige" },
            new() { CId = "AO", Name = "Zaire" },
            new() { CId = "AG", Name = "Saint George" },
            new() { CId = "AG", Name = "Saint John" },
            new() { CId = "AG", Name = "Saint Mary" },
            new() { CId = "AG", Name = "Saint Paul" },
            new() { CId = "AG", Name = "Saint Peter" },
            new() { CId = "AG", Name = "Saint Philip" },
            new() { CId = "AG", Name = "Barbuda" },
            new() { CId = "AG", Name = "Redonda" },
            new() { CId = "AR", Name = "Antartida e Islas del Atlantico" },
            new() { CId = "AR", Name = "Buenos Aires" },
            new() { CId = "AR", Name = "Catamarca" },
            new() { CId = "AR", Name = "Chaco" },
            new() { CId = "AR", Name = "Chubut" },
            new() { CId = "AR", Name = "Cordoba" },
            new() { CId = "AR", Name = "Corrientes" },
            new() { CId = "AR", Name = "Distrito Federal" },
            new() { CId = "AR", Name = "Entre Rios" },
            new() { CId = "AR", Name = "Formosa" },
            new() { CId = "AR", Name = "Jujuy" },
            new() { CId = "AR", Name = "La Pampa" },
            new() { CId = "AR", Name = "La Rioja" },
            new() { CId = "AR", Name = "Mendoza" },
            new() { CId = "AR", Name = "Misiones" },
            new() { CId = "AR", Name = "Neuquen" },
            new() { CId = "AR", Name = "Rio Negro" },
            new() { CId = "AR", Name = "Salta" },
            new() { CId = "AR", Name = "San Juan" },
            new() { CId = "AR", Name = "San Luis" },
            new() { CId = "AR", Name = "Santa Cruz" },
            new() { CId = "AR", Name = "Santa Fe" },
            new() { CId = "AR", Name = "Santiago del Estero" },
            new() { CId = "AR", Name = "Tierra del Fuego" },
            new() { CId = "AR", Name = "Tucuman" },
            new() { CId = "AM", Name = "Aragatsotn" },
            new() { CId = "AM", Name = "Ararat" },
            new() { CId = "AM", Name = "Armavir" },
            new() { CId = "AM", Name = "Geghark'unik'" },
            new() { CId = "AM", Name = "Kotayk'" },
            new() { CId = "AM", Name = "Lorri" },
            new() { CId = "AM", Name = "Shirak" },
            new() { CId = "AM", Name = "Syunik'" },
            new() { CId = "AM", Name = "Tavush" },
            new() { CId = "AM", Name = "Vayots' Dzor" },
            new() { CId = "AM", Name = "Yerevan" },
            new() { CId = "AU", Name = "Australian Capital Territory" },
            new() { CId = "AU", Name = "New South Wales" },
            new() { CId = "AU", Name = "Northern Territory" },
            new() { CId = "AU", Name = "Queensland" },
            new() { CId = "AU", Name = "South Australia" },
            new() { CId = "AU", Name = "Tasmania" },
            new() { CId = "AU", Name = "Victoria" },
            new() { CId = "AU", Name = "Western Australia" },
            new() { CId = "AT", Name = "Burgenland" },
            new() { CId = "AT", Name = "Kärnten" },
            new() { CId = "AT", Name = "Niederösterreich" },
            new() { CId = "AT", Name = "Oberösterreich" },
            new() { CId = "AT", Name = "Salzburg" },
            new() { CId = "AT", Name = "Steiermark" },
            new() { CId = "AT", Name = "Tirol" },
            new() { CId = "AT", Name = "Vorarlberg" },
            new() { CId = "AT", Name = "Wien" },
            new() { CId = "AZ", Name = "Ali Bayramli" },
            new() { CId = "AZ", Name = "Abseron" },
            new() { CId = "AZ", Name = "AgcabAdi" },
            new() { CId = "AZ", Name = "Agdam" },
            new() { CId = "AZ", Name = "Agdas" },
            new() { CId = "AZ", Name = "Agstafa" },
            new() { CId = "AZ", Name = "Agsu" },
            new() { CId = "AZ", Name = "Astara" },
            new() { CId = "AZ", Name = "Baki" },
            new() { CId = "AZ", Name = "BabAk" },
            new() { CId = "AZ", Name = "BalakAn" },
            new() { CId = "AZ", Name = "BArdA" },
            new() { CId = "AZ", Name = "Beylaqan" },
            new() { CId = "AZ", Name = "Bilasuvar" },
            new() { CId = "AZ", Name = "Cabrayil" },
            new() { CId = "AZ", Name = "Calilabab" },
            new() { CId = "AZ", Name = "Culfa" },
            new() { CId = "AZ", Name = "Daskasan" },
            new() { CId = "AZ", Name = "Davaci" },
            new() { CId = "AZ", Name = "Fuzuli" },
            new() { CId = "AZ", Name = "Ganca" },
            new() { CId = "AZ", Name = "Gadabay" },
            new() { CId = "AZ", Name = "Goranboy" },
            new() { CId = "AZ", Name = "Goycay" },
            new() { CId = "AZ", Name = "Haciqabul" },
            new() { CId = "AZ", Name = "Imisli" },
            new() { CId = "AZ", Name = "Ismayilli" },
            new() { CId = "AZ", Name = "Kalbacar" },
            new() { CId = "AZ", Name = "Kurdamir" },
            new() { CId = "AZ", Name = "Lankaran" },
            new() { CId = "AZ", Name = "Lacin" },
            new() { CId = "AZ", Name = "Lerik" },
            new() { CId = "AZ", Name = "Masalli" },
            new() { CId = "AZ", Name = "Mingacevir" },
            new() { CId = "AZ", Name = "Naftalan" },
            new() { CId = "AZ", Name = "Neftcala" },
            new() { CId = "AZ", Name = "Oguz" },
            new() { CId = "AZ", Name = "Ordubad" },
            new() { CId = "AZ", Name = "Qabala" },
            new() { CId = "AZ", Name = "Qax" },
            new() { CId = "AZ", Name = "Qazax" },
            new() { CId = "AZ", Name = "Qobustan" },
            new() { CId = "AZ", Name = "Quba" },
            new() { CId = "AZ", Name = "Qubadli" },
            new() { CId = "AZ", Name = "Qusar" },
            new() { CId = "AZ", Name = "Saki" },
            new() { CId = "AZ", Name = "Saatli" },
            new() { CId = "AZ", Name = "Sabirabad" },
            new() { CId = "AZ", Name = "Sadarak" },
            new() { CId = "AZ", Name = "Sahbuz" },
            new() { CId = "AZ", Name = "Salyan" },
            new() { CId = "AZ", Name = "Sumqayit" },
            new() { CId = "AZ", Name = "Samaxi" },
            new() { CId = "AZ", Name = "Samkir" },
            new() { CId = "AZ", Name = "Samux" },
            new() { CId = "AZ", Name = "Sarur" },
            new() { CId = "AZ", Name = "Siyazan" },
            new() { CId = "AZ", Name = "Susa" },
            new() { CId = "AZ", Name = "Tartar" },
            new() { CId = "AZ", Name = "Tovuz" },
            new() { CId = "AZ", Name = "Ucar" },
            new() { CId = "AZ", Name = "Xankandi" },
            new() { CId = "AZ", Name = "Xacmaz" },
            new() { CId = "AZ", Name = "Xanlar" },
            new() { CId = "AZ", Name = "Xizi" },
            new() { CId = "AZ", Name = "Xocali" },
            new() { CId = "AZ", Name = "Xocavand" },
            new() { CId = "AZ", Name = "Yardimli" },
            new() { CId = "AZ", Name = "Yevlax" },
            new() { CId = "AZ", Name = "Zangilan" },
            new() { CId = "AZ", Name = "Zaqatala" },
            new() { CId = "AZ", Name = "Zardab" },
            new() { CId = "AZ", Name = "Naxcivan" },
            new() { CId = "BS", Name = "Acklins" },
            new() { CId = "BS", Name = "Berry Islands" },
            new() { CId = "BS", Name = "Bimini" },
            new() { CId = "BS", Name = "Black Point" },
            new() { CId = "BS", Name = "Cat Island" },
            new() { CId = "BS", Name = "Central Abaco" },
            new() { CId = "BS", Name = "Central Andros" },
            new() { CId = "BS", Name = "Central Eleuthera" },
            new() { CId = "BS", Name = "City of Freeport" },
            new() { CId = "BS", Name = "Crooked Island" },
            new() { CId = "BS", Name = "East Grand Bahama" },
            new() { CId = "BS", Name = "Exuma" },
            new() { CId = "BS", Name = "Grand Cay" },
            new() { CId = "BS", Name = "Harbour Island" },
            new() { CId = "BS", Name = "Hope Town" },
            new() { CId = "BS", Name = "Inagua" },
            new() { CId = "BS", Name = "Long Island" },
            new() { CId = "BS", Name = "Mangrove Cay" },
            new() { CId = "BS", Name = "Mayaguana" },
            new() { CId = "BS", Name = "Moore's Island" },
            new() { CId = "BS", Name = "North Abaco" },
            new() { CId = "BS", Name = "North Andros" },
            new() { CId = "BS", Name = "North Eleuthera" },
            new() { CId = "BS", Name = "Ragged Island" },
            new() { CId = "BS", Name = "Rum Cay" },
            new() { CId = "BS", Name = "San Salvador" },
            new() { CId = "BS", Name = "South Abaco" },
            new() { CId = "BS", Name = "South Andros" },
            new() { CId = "BS", Name = "South Eleuthera" },
            new() { CId = "BS", Name = "Spanish Wells" },
            new() { CId = "BS", Name = "West Grand Bahama" },
            new() { CId = "BH", Name = "Capital" },
            new() { CId = "BH", Name = "Central" },
            new() { CId = "BH", Name = "Muharraq" },
            new() { CId = "BH", Name = "Northern" },
            new() { CId = "BH", Name = "Southern" },
            new() { CId = "BD", Name = "Barisal" },
            new() { CId = "BD", Name = "Chittagong" },
            new() { CId = "BD", Name = "Dhaka" },
            new() { CId = "BD", Name = "Khulna" },
            new() { CId = "BD", Name = "Rajshahi" },
            new() { CId = "BD", Name = "Sylhet" },
            new() { CId = "BB", Name = "Christ Church" },
            new() { CId = "BB", Name = "Saint Andrew" },
            new() { CId = "BB", Name = "Saint George" },
            new() { CId = "BB", Name = "Saint James" },
            new() { CId = "BB", Name = "Saint John" },
            new() { CId = "BB", Name = "Saint Joseph" },
            new() { CId = "BB", Name = "Saint Lucy" },
            new() { CId = "BB", Name = "Saint Michael" },
            new() { CId = "BB", Name = "Saint Peter" },
            new() { CId = "BB", Name = "Saint Philip" },
            new() { CId = "BB", Name = "Saint Thomas" },
            new() { CId = "BY", Name = "Brestskaya (Brest)" },
            new() { CId = "BY", Name = "Homyel'skaya (Homyel')" },
            new() { CId = "BY", Name = "Horad Minsk" },
            new() { CId = "BY", Name = "Hrodzyenskaya (Hrodna)" },
            new() { CId = "BY", Name = "Mahilyowskaya (Mahilyow)" },
            new() { CId = "BY", Name = "Minskaya" },
            new() { CId = "BY", Name = "Vitsyebskaya (Vitsyebsk)" },
            new() { CId = "BE", Name = "Antwerpen" },
            new() { CId = "BE", Name = "Brabant Wallon" },
            new() { CId = "BE", Name = "Hainaut" },
            new() { CId = "BE", Name = "Liège" },
            new() { CId = "BE", Name = "Limburg" },
            new() { CId = "BE", Name = "Luxembourg" },
            new() { CId = "BE", Name = "Namur" },
            new() { CId = "BE", Name = "Oost-Vlaanderen" },
            new() { CId = "BE", Name = "Vlaams Brabant" },
            new() { CId = "BE", Name = "West-Vlaanderen" },
            new() { CId = "BZ", Name = "Belize" },
            new() { CId = "BZ", Name = "Cayo" },
            new() { CId = "BZ", Name = "Corozal" },
            new() { CId = "BZ", Name = "Orange Walk" },
            new() { CId = "BZ", Name = "Stann Creek" },
            new() { CId = "BZ", Name = "Toledo" },
            new() { CId = "BJ", Name = "Alibori" },
            new() { CId = "BJ", Name = "Atakora" },
            new() { CId = "BJ", Name = "Atlantique" },
            new() { CId = "BJ", Name = "Borgou" },
            new() { CId = "BJ", Name = "Collines" },
            new() { CId = "BJ", Name = "Donga" },
            new() { CId = "BJ", Name = "Kouffo" },
            new() { CId = "BJ", Name = "Littoral" },
            new() { CId = "BJ", Name = "Mono" },
            new() { CId = "BJ", Name = "Oueme" },
            new() { CId = "BJ", Name = "Plateau" },
            new() { CId = "BJ", Name = "Zou" },
            new() { CId = "BM", Name = "Devonshire" },
            new() { CId = "BM", Name = "Hamilton City" },
            new() { CId = "BM", Name = "Hamilton" },
            new() { CId = "BM", Name = "Paget" },
            new() { CId = "BM", Name = "Pembroke" },
            new() { CId = "BM", Name = "Saint George City" },
            new() { CId = "BM", Name = "Saint George's" },
            new() { CId = "BM", Name = "Sandys" },
            new() { CId = "BM", Name = "Smith's" },
            new() { CId = "BM", Name = "Southampton" },
            new() { CId = "BM", Name = "Warwick" },
            new() { CId = "BT", Name = "Bumthang" },
            new() { CId = "BT", Name = "Chukha" },
            new() { CId = "BT", Name = "Dagana" },
            new() { CId = "BT", Name = "Gasa" },
            new() { CId = "BT", Name = "Haa" },
            new() { CId = "BT", Name = "Lhuntse" },
            new() { CId = "BT", Name = "Mongar" },
            new() { CId = "BT", Name = "Paro" },
            new() { CId = "BT", Name = "Pemagatshel" },
            new() { CId = "BT", Name = "Punakha" },
            new() { CId = "BT", Name = "Samdrup Jongkhar" },
            new() { CId = "BT", Name = "Samtse" },
            new() { CId = "BT", Name = "Sarpang" },
            new() { CId = "BT", Name = "Thimphu" },
            new() { CId = "BT", Name = "Trashigang" },
            new() { CId = "BT", Name = "Trashiyangste" },
            new() { CId = "BT", Name = "Trongsa" },
            new() { CId = "BT", Name = "Tsirang" },
            new() { CId = "BT", Name = "Wangdue Phodrang" },
            new() { CId = "BT", Name = "Zhemgang" },
            new() { CId = "BO", Name = "Beni" },
            new() { CId = "BO", Name = "Chuquisaca" },
            new() { CId = "BO", Name = "Cochabamba" },
            new() { CId = "BO", Name = "La Paz" },
            new() { CId = "BO", Name = "Oruro" },
            new() { CId = "BO", Name = "Pando" },
            new() { CId = "BO", Name = "Potosi" },
            new() { CId = "BO", Name = "Santa Cruz" },
            new() { CId = "BO", Name = "Tarija" },
            new() { CId = "BA", Name = "Brcko district" },
            new() { CId = "BA", Name = "Unsko-Sanski Kanton" },
            new() { CId = "BA", Name = "Posavski Kanton" },
            new() { CId = "BA", Name = "Tuzlanski Kanton" },
            new() { CId = "BA", Name = "Zenicko-Dobojski Kanton" },
            new() { CId = "BA", Name = "Bosanskopodrinjski Kanton" },
            new() { CId = "BA", Name = "Srednjebosanski Kanton" },
            new() { CId = "BA", Name = "Hercegovacko-neretvanski Kanton" },
            new() { CId = "BA", Name = "Zapadnohercegovacka Zupanija" },
            new() { CId = "BA", Name = "Kanton Sarajevo" },
            new() { CId = "BA", Name = "Zapadnobosanska" },
            new() { CId = "BA", Name = "Banja Luka" },
            new() { CId = "BA", Name = "Doboj" },
            new() { CId = "BA", Name = "Bijeljina" },
            new() { CId = "BA", Name = "Vlasenica" },
            new() { CId = "BA", Name = "Sarajevo-Romanija or Sokolac" },
            new() { CId = "BA", Name = "Foca" },
            new() { CId = "BA", Name = "Trebinje" },
            new() { CId = "BW", Name = "Central" },
            new() { CId = "BW", Name = "Ghanzi" },
            new() { CId = "BW", Name = "Kgalagadi" },
            new() { CId = "BW", Name = "Kgatleng" },
            new() { CId = "BW", Name = "Kweneng" },
            new() { CId = "BW", Name = "Ngamiland" },
            new() { CId = "BW", Name = "North East" },
            new() { CId = "BW", Name = "North West" },
            new() { CId = "BW", Name = "South East" },
            new() { CId = "BW", Name = "Southern" },
            new() { CId = "BR", Name = "Acre" },
            new() { CId = "BR", Name = "Alagoas" },
            new() { CId = "BR", Name = "Amapá" },
            new() { CId = "BR", Name = "Amazonas" },
            new() { CId = "BR", Name = "Bahia" },
            new() { CId = "BR", Name = "Ceará" },
            new() { CId = "BR", Name = "Distrito Federal" },
            new() { CId = "BR", Name = "Espírito Santo" },
            new() { CId = "BR", Name = "Goiás" },
            new() { CId = "BR", Name = "Maranhão" },
            new() { CId = "BR", Name = "Mato Grosso" },
            new() { CId = "BR", Name = "Mato Grosso do Sul" },
            new() { CId = "BR", Name = "Minas Gerais" },
            new() { CId = "BR", Name = "Pará" },
            new() { CId = "BR", Name = "Paraíba" },
            new() { CId = "BR", Name = "Paraná" },
            new() { CId = "BR", Name = "Pernambuco" },
            new() { CId = "BR", Name = "Piauí" },
            new() { CId = "BR", Name = "Rio de Janeiro" },
            new() { CId = "BR", Name = "Rio Grande do Norte" },
            new() { CId = "BR", Name = "Rio Grande do Sul" },
            new() { CId = "BR", Name = "Rondônia" },
            new() { CId = "BR", Name = "Roraima" },
            new() { CId = "BR", Name = "Santa Catarina" },
            new() { CId = "BR", Name = "São Paulo" },
            new() { CId = "BR", Name = "Sergipe" },
            new() { CId = "BR", Name = "Tocantins" },
            new() { CId = "IO", Name = "Peros Banhos" },
            new() { CId = "IO", Name = "Salomon Islands" },
            new() { CId = "IO", Name = "Nelsons Island" },
            new() { CId = "IO", Name = "Three Brothers" },
            new() { CId = "IO", Name = "Eagle Islands" },
            new() { CId = "IO", Name = "Danger Island" },
            new() { CId = "IO", Name = "Egmont Islands" },
            new() { CId = "IO", Name = "Diego Garcia" },
            new() { CId = "BN", Name = "Belait" },
            new() { CId = "BN", Name = "Brunei and Muara" },
            new() { CId = "BN", Name = "Temburong" },
            new() { CId = "BN", Name = "Tutong" },
            new() { CId = "BG", Name = "Blagoevgrad" },
            new() { CId = "BG", Name = "Burgas" },
            new() { CId = "BG", Name = "Dobrich" },
            new() { CId = "BG", Name = "Gabrovo" },
            new() { CId = "BG", Name = "Haskovo" },
            new() { CId = "BG", Name = "Kardjali" },
            new() { CId = "BG", Name = "Kyustendil" },
            new() { CId = "BG", Name = "Lovech" },
            new() { CId = "BG", Name = "Montana" },
            new() { CId = "BG", Name = "Pazardjik" },
            new() { CId = "BG", Name = "Pernik" },
            new() { CId = "BG", Name = "Pleven" },
            new() { CId = "BG", Name = "Plovdiv" },
            new() { CId = "BG", Name = "Razgrad" },
            new() { CId = "BG", Name = "Shumen" },
            new() { CId = "BG", Name = "Silistra" },
            new() { CId = "BG", Name = "Sliven" },
            new() { CId = "BG", Name = "Smolyan" },
            new() { CId = "BG", Name = "Sofia" },
            new() { CId = "BG", Name = "Sofia - town" },
            new() { CId = "BG", Name = "Stara Zagora" },
            new() { CId = "BG", Name = "Targovishte" },
            new() { CId = "BG", Name = "Varna" },
            new() { CId = "BG", Name = "Veliko Tarnovo" },
            new() { CId = "BG", Name = "Vidin" },
            new() { CId = "BG", Name = "Vratza" },
            new() { CId = "BG", Name = "Yambol" },
            new() { CId = "BF", Name = "Bale" },
            new() { CId = "BF", Name = "Bam" },
            new() { CId = "BF", Name = "Banwa" },
            new() { CId = "BF", Name = "Bazega" },
            new() { CId = "BF", Name = "Bougouriba" },
            new() { CId = "BF", Name = "Boulgou" },
            new() { CId = "BF", Name = "Boulkiemde" },
            new() { CId = "BF", Name = "Comoe" },
            new() { CId = "BF", Name = "Ganzourgou" },
            new() { CId = "BF", Name = "Gnagna" },
            new() { CId = "BF", Name = "Gourma" },
            new() { CId = "BF", Name = "Houet" },
            new() { CId = "BF", Name = "Ioba" },
            new() { CId = "BF", Name = "Kadiogo" },
            new() { CId = "BF", Name = "Kenedougou" },
            new() { CId = "BF", Name = "Komondjari" },
            new() { CId = "BF", Name = "Kompienga" },
            new() { CId = "BF", Name = "Kossi" },
            new() { CId = "BF", Name = "Koulpelogo" },
            new() { CId = "BF", Name = "Kouritenga" },
            new() { CId = "BF", Name = "Kourweogo" },
            new() { CId = "BF", Name = "Leraba" },
            new() { CId = "BF", Name = "Loroum" },
            new() { CId = "BF", Name = "Mouhoun" },
            new() { CId = "BF", Name = "Nahouri" },
            new() { CId = "BF", Name = "Namentenga" },
            new() { CId = "BF", Name = "Nayala" },
            new() { CId = "BF", Name = "Noumbiel" },
            new() { CId = "BF", Name = "Oubritenga" },
            new() { CId = "BF", Name = "Oudalan" },
            new() { CId = "BF", Name = "Passore" },
            new() { CId = "BF", Name = "Poni" },
            new() { CId = "BF", Name = "Sanguie" },
            new() { CId = "BF", Name = "Sanmatenga" },
            new() { CId = "BF", Name = "Seno" },
            new() { CId = "BF", Name = "Sissili" },
            new() { CId = "BF", Name = "Soum" },
            new() { CId = "BF", Name = "Sourou" },
            new() { CId = "BF", Name = "Tapoa" },
            new() { CId = "BF", Name = "Tuy" },
            new() { CId = "BF", Name = "Yagha" },
            new() { CId = "BF", Name = "Yatenga" },
            new() { CId = "BF", Name = "Ziro" },
            new() { CId = "BF", Name = "Zondoma" },
            new() { CId = "BF", Name = "Zoundweogo" },
            new() { CId = "BI", Name = "Bubanza" },
            new() { CId = "BI", Name = "Bujumbura" },
            new() { CId = "BI", Name = "Bururi" },
            new() { CId = "BI", Name = "Cankuzo" },
            new() { CId = "BI", Name = "Cibitoke" },
            new() { CId = "BI", Name = "Gitega" },
            new() { CId = "BI", Name = "Karuzi" },
            new() { CId = "BI", Name = "Kayanza" },
            new() { CId = "BI", Name = "Kirundo" },
            new() { CId = "BI", Name = "Makamba" },
            new() { CId = "BI", Name = "Muramvya" },
            new() { CId = "BI", Name = "Muyinga" },
            new() { CId = "BI", Name = "Mwaro" },
            new() { CId = "BI", Name = "Ngozi" },
            new() { CId = "BI", Name = "Rutana" },
            new() { CId = "BI", Name = "Ruyigi" },
            new() { CId = "KH", Name = "Phnom Penh" },
            new() { CId = "KH", Name = "Preah Seihanu (Kompong Som or Sihanoukville)" },
            new() { CId = "KH", Name = "Pailin" },
            new() { CId = "KH", Name = "Keb" },
            new() { CId = "KH", Name = "Banteay Meanchey" },
            new() { CId = "KH", Name = "Battambang" },
            new() { CId = "KH", Name = "Kampong Cham" },
            new() { CId = "KH", Name = "Kampong Chhnang" },
            new() { CId = "KH", Name = "Kampong Speu" },
            new() { CId = "KH", Name = "Kampong Som" },
            new() { CId = "KH", Name = "Kampong Thom" },
            new() { CId = "KH", Name = "Kampot" },
            new() { CId = "KH", Name = "Kandal" },
            new() { CId = "KH", Name = "Kaoh Kong" },
            new() { CId = "KH", Name = "Kratie" },
            new() { CId = "KH", Name = "Mondul Kiri" },
            new() { CId = "KH", Name = "Oddar Meancheay" },
            new() { CId = "KH", Name = "Pursat" },
            new() { CId = "KH", Name = "Preah Vihear" },
            new() { CId = "KH", Name = "Prey Veng" },
            new() { CId = "KH", Name = "Ratanak Kiri" },
            new() { CId = "KH", Name = "Siemreap" },
            new() { CId = "KH", Name = "Stung Treng" },
            new() { CId = "KH", Name = "Svay Rieng" },
            new() { CId = "KH", Name = "Takeo" },
            new() { CId = "CM", Name = "Adamawa (Adamaoua)" },
            new() { CId = "CM", Name = "Centre" },
            new() { CId = "CM", Name = "East (Est)" },
            new() { CId = "CM", Name = "Extreme North (Extreme-Nord)" },
            new() { CId = "CM", Name = "Littoral" },
            new() { CId = "CM", Name = "North (Nord)" },
            new() { CId = "CM", Name = "Northwest (Nord-Ouest)" },
            new() { CId = "CM", Name = "West (Ouest)" },
            new() { CId = "CM", Name = "South (Sud)" },
            new() { CId = "CM", Name = "Southwest (Sud-Ouest)." },
            new() { CId = "CA", Name = "Alberta" },
            new() { CId = "CA", Name = "British Columbia" },
            new() { CId = "CA", Name = "Manitoba" },
            new() { CId = "CA", Name = "New Brunswick" },
            new() { CId = "CA", Name = "Newfoundland and Labrador" },
            new() { CId = "CA", Name = "Northwest Territories" },
            new() { CId = "CA", Name = "Nova Scotia" },
            new() { CId = "CA", Name = "Nunavut" },
            new() { CId = "CA", Name = "Ontario" },
            new() { CId = "CA", Name = "Prince Edward Island" },
            new() { CId = "CA", Name = "Qu&eacute;bec" },
            new() { CId = "CA", Name = "Saskatchewan" },
            new() { CId = "CA", Name = "Yukon Territory" },
            new() { CId = "CV", Name = "Boa Vista" },
            new() { CId = "CV", Name = "Brava" },
            new() { CId = "CV", Name = "Calheta de Sao Miguel" },
            new() { CId = "CV", Name = "Maio" },
            new() { CId = "CV", Name = "Mosteiros" },
            new() { CId = "CV", Name = "Paul" },
            new() { CId = "CV", Name = "Porto Novo" },
            new() { CId = "CV", Name = "Praia" },
            new() { CId = "CV", Name = "Ribeira Grande" },
            new() { CId = "CV", Name = "Sal" },
            new() { CId = "CV", Name = "Santa Catarina" },
            new() { CId = "CV", Name = "Santa Cruz" },
            new() { CId = "CV", Name = "Sao Domingos" },
            new() { CId = "CV", Name = "Sao Filipe" },
            new() { CId = "CV", Name = "Sao Nicolau" },
            new() { CId = "CV", Name = "Sao Vicente" },
            new() { CId = "CV", Name = "Tarrafal" },
            new() { CId = "KY", Name = "Creek" },
            new() { CId = "KY", Name = "Eastern" },
            new() { CId = "KY", Name = "Midland" },
            new() { CId = "KY", Name = "South Town" },
            new() { CId = "KY", Name = "Spot Bay" },
            new() { CId = "KY", Name = "Stake Bay" },
            new() { CId = "KY", Name = "West End" },
            new() { CId = "KY", Name = "Western" },
            new() { CId = "CF", Name = "Bamingui-Bangoran" },
            new() { CId = "CF", Name = "Basse-Kotto" },
            new() { CId = "CF", Name = "Haute-Kotto" },
            new() { CId = "CF", Name = "Haut-Mbomou" },
            new() { CId = "CF", Name = "Kemo" },
            new() { CId = "CF", Name = "Lobaye" },
            new() { CId = "CF", Name = "Mambere-KadeÔ" },
            new() { CId = "CF", Name = "Mbomou" },
            new() { CId = "CF", Name = "Nana-Mambere" },
            new() { CId = "CF", Name = "Ombella-M'Poko" },
            new() { CId = "CF", Name = "Ouaka" },
            new() { CId = "CF", Name = "Ouham" },
            new() { CId = "CF", Name = "Ouham-Pende" },
            new() { CId = "CF", Name = "Vakaga" },
            new() { CId = "CF", Name = "Nana-Grebizi" },
            new() { CId = "CF", Name = "Sangha-Mbaere" },
            new() { CId = "CF", Name = "Bangui" },
            new() { CId = "TD", Name = "Batha" },
            new() { CId = "TD", Name = "Biltine" },
            new() { CId = "TD", Name = "Borkou-Ennedi-Tibesti" },
            new() { CId = "TD", Name = "Chari-Baguirmi" },
            new() { CId = "TD", Name = "Guera" },
            new() { CId = "TD", Name = "Kanem" },
            new() { CId = "TD", Name = "Lac" },
            new() { CId = "TD", Name = "Logone Occidental" },
            new() { CId = "TD", Name = "Logone Oriental" },
            new() { CId = "TD", Name = "Mayo-Kebbi" },
            new() { CId = "TD", Name = "Moyen-Chari" },
            new() { CId = "TD", Name = "Ouaddai" },
            new() { CId = "TD", Name = "Salamat" },
            new() { CId = "TD", Name = "Tandjile" },
            new() { CId = "CL", Name = "Aisen del General Carlos Ibanez" },
            new() { CId = "CL", Name = "Antofagasta" },
            new() { CId = "CL", Name = "Araucania" },
            new() { CId = "CL", Name = "Atacama" },
            new() { CId = "CL", Name = "Bio-Bio" },
            new() { CId = "CL", Name = "Coquimbo" },
            new() { CId = "CL", Name = "Libertador General Bernardo O'Higgins" },
            new() { CId = "CL", Name = "Los Lagos" },
            new() { CId = "CL", Name = "Magallanes y de la Antartica Chilena" },
            new() { CId = "CL", Name = "Maule" },
            new() { CId = "CL", Name = "Region Metropolitana" },
            new() { CId = "CL", Name = "Tarapaca" },
            new() { CId = "CL", Name = "Valparaiso" },
            new() { CId = "CC", Name = "Direction Island" },
            new() { CId = "CC", Name = "Home Island" },
            new() { CId = "CC", Name = "Horsburgh Island" },
            new() { CId = "CC", Name = "South Island" },
            new() { CId = "CC", Name = "West Island" },
            new() { CId = "CO", Name = "Amazonas" },
            new() { CId = "CO", Name = "Antioquia" },
            new() { CId = "CO", Name = "Arauca" },
            new() { CId = "CO", Name = "Atlantico" },
            new() { CId = "CO", Name = "Bogota D.C." },
            new() { CId = "CO", Name = "Bolivar" },
            new() { CId = "CO", Name = "Boyaca" },
            new() { CId = "CO", Name = "Caldas" },
            new() { CId = "CO", Name = "Caqueta" },
            new() { CId = "CO", Name = "Casanare" },
            new() { CId = "CO", Name = "Cauca" },
            new() { CId = "CO", Name = "Cesar" },
            new() { CId = "CO", Name = "Choco" },
            new() { CId = "CO", Name = "Cordoba" },
            new() { CId = "CO", Name = "Cundinamarca" },
            new() { CId = "CO", Name = "Guainia" },
            new() { CId = "CO", Name = "Guajira" },
            new() { CId = "CO", Name = "Guaviare" },
            new() { CId = "CO", Name = "Huila" },
            new() { CId = "CO", Name = "Magdalena" },
            new() { CId = "CO", Name = "Meta" },
            new() { CId = "CO", Name = "Narino" },
            new() { CId = "CO", Name = "Norte de Santander" },
            new() { CId = "CO", Name = "Putumayo" },
            new() { CId = "CO", Name = "Quindio" },
            new() { CId = "CO", Name = "Risaralda" },
            new() { CId = "CO", Name = "San Andres y Providencia" },
            new() { CId = "CO", Name = "Santander" },
            new() { CId = "CO", Name = "Sucre" },
            new() { CId = "CO", Name = "Tolima" },
            new() { CId = "CO", Name = "Valle del Cauca" },
            new() { CId = "CO", Name = "Vaupes" },
            new() { CId = "CO", Name = "Vichada" },
            new() { CId = "KM", Name = "Grande Comore" },
            new() { CId = "KM", Name = "Anjouan" },
            new() { CId = "KM", Name = "Moheli" },
            new() { CId = "CG", Name = "Bouenza" },
            new() { CId = "CG", Name = "Brazzaville" },
            new() { CId = "CG", Name = "Cuvette" },
            new() { CId = "CG", Name = "Cuvette-Ouest" },
            new() { CId = "CG", Name = "Kouilou" },
            new() { CId = "CG", Name = "Lekoumou" },
            new() { CId = "CG", Name = "Likouala" },
            new() { CId = "CG", Name = "Niari" },
            new() { CId = "CG", Name = "Plateaux" },
            new() { CId = "CG", Name = "Pool" },
            new() { CId = "CG", Name = "Sangha" },
            new() { CId = "CK", Name = "Pukapuka" },
            new() { CId = "CK", Name = "Rakahanga" },
            new() { CId = "CK", Name = "Manihiki" },
            new() { CId = "CK", Name = "Penrhyn" },
            new() { CId = "CK", Name = "Nassau Island" },
            new() { CId = "CK", Name = "Surwarrow" },
            new() { CId = "CK", Name = "Palmerston" },
            new() { CId = "CK", Name = "Aitutaki" },
            new() { CId = "CK", Name = "Manuae" },
            new() { CId = "CK", Name = "Takutea" },
            new() { CId = "CK", Name = "Mitiaro" },
            new() { CId = "CK", Name = "Atiu" },
            new() { CId = "CK", Name = "Mauke" },
            new() { CId = "CK", Name = "Rarotonga" },
            new() { CId = "CK", Name = "Mangaia" },
            new() { CId = "CR", Name = "Alajuela" },
            new() { CId = "CR", Name = "Cartago" },
            new() { CId = "CR", Name = "Guanacaste" },
            new() { CId = "CR", Name = "Heredia" },
            new() { CId = "CR", Name = "Limon" },
            new() { CId = "CR", Name = "Puntarenas" },
            new() { CId = "CR", Name = "San Jose" },
            new() { CId = "CI", Name = "Abengourou" },
            new() { CId = "CI", Name = "Abidjan" },
            new() { CId = "CI", Name = "Aboisso" },
            new() { CId = "CI", Name = "Adiake" },
            new() { CId = "CI", Name = "Adzope" },
            new() { CId = "CI", Name = "Agboville" },
            new() { CId = "CI", Name = "Agnibilekrou" },
            new() { CId = "CI", Name = "Alepe" },
            new() { CId = "CI", Name = "Bocanda" },
            new() { CId = "CI", Name = "Bangolo" },
            new() { CId = "CI", Name = "Beoumi" },
            new() { CId = "CI", Name = "Biankouma" },
            new() { CId = "CI", Name = "Bondoukou" },
            new() { CId = "CI", Name = "Bongouanou" },
            new() { CId = "CI", Name = "Bouafle" },
            new() { CId = "CI", Name = "Bouake" },
            new() { CId = "CI", Name = "Bouna" },
            new() { CId = "CI", Name = "Boundiali" },
            new() { CId = "CI", Name = "Dabakala" },
            new() { CId = "CI", Name = "Dabou" },
            new() { CId = "CI", Name = "Daloa" },
            new() { CId = "CI", Name = "Danane" },
            new() { CId = "CI", Name = "Daoukro" },
            new() { CId = "CI", Name = "Dimbokro" },
            new() { CId = "CI", Name = "Divo" },
            new() { CId = "CI", Name = "Duekoue" },
            new() { CId = "CI", Name = "Ferkessedougou" },
            new() { CId = "CI", Name = "Gagnoa" },
            new() { CId = "CI", Name = "Grand-Bassam" },
            new() { CId = "CI", Name = "Grand-Lahou" },
            new() { CId = "CI", Name = "Guiglo" },
            new() { CId = "CI", Name = "Issia" },
            new() { CId = "CI", Name = "Jacqueville" },
            new() { CId = "CI", Name = "Katiola" },
            new() { CId = "CI", Name = "Korhogo" },
            new() { CId = "CI", Name = "Lakota" },
            new() { CId = "CI", Name = "Man" },
            new() { CId = "CI", Name = "Mankono" },
            new() { CId = "CI", Name = "Mbahiakro" },
            new() { CId = "CI", Name = "Odienne" },
            new() { CId = "CI", Name = "Oume" },
            new() { CId = "CI", Name = "Sakassou" },
            new() { CId = "CI", Name = "San-Pedro" },
            new() { CId = "CI", Name = "Sassandra" },
            new() { CId = "CI", Name = "Seguela" },
            new() { CId = "CI", Name = "Sinfra" },
            new() { CId = "CI", Name = "Soubre" },
            new() { CId = "CI", Name = "Tabou" },
            new() { CId = "CI", Name = "Tanda" },
            new() { CId = "CI", Name = "Tiebissou" },
            new() { CId = "CI", Name = "Tingrela" },
            new() { CId = "CI", Name = "Tiassale" },
            new() { CId = "CI", Name = "Touba" },
            new() { CId = "CI", Name = "Toulepleu" },
            new() { CId = "CI", Name = "Toumodi" },
            new() { CId = "CI", Name = "Vavoua" },
            new() { CId = "CI", Name = "Yamoussoukro" },
            new() { CId = "CI", Name = "Zuenoula" },
            new() { CId = "HR", Name = "Bjelovarsko-bilogorska" },
            new() { CId = "HR", Name = "Grad Zagreb" },
            new() { CId = "HR", Name = "Dubrovačko-neretvanska" },
            new() { CId = "HR", Name = "Istarska" },
            new() { CId = "HR", Name = "Karlovačka" },
            new() { CId = "HR", Name = "Koprivničko-križevačka" },
            new() { CId = "HR", Name = "Krapinsko-zagorska" },
            new() { CId = "HR", Name = "Ličko-senjska" },
            new() { CId = "HR", Name = "Međimurska" },
            new() { CId = "HR", Name = "Osječko-baranjska" },
            new() { CId = "HR", Name = "Požeško-slavonska" },
            new() { CId = "HR", Name = "Primorsko-goranska" },
            new() { CId = "HR", Name = "Šibensko-kninska" },
            new() { CId = "HR", Name = "Sisačko-moslavačka" },
            new() { CId = "HR", Name = "Brodsko-posavska" },
            new() { CId = "HR", Name = "Splitsko-dalmatinska" },
            new() { CId = "HR", Name = "Varaždinska" },
            new() { CId = "HR", Name = "Virovitičko-podravska" },
            new() { CId = "HR", Name = "Vukovarsko-srijemska" },
            new() { CId = "HR", Name = "Zadarska" },
            new() { CId = "HR", Name = "Zagrebačka" },
            new() { CId = "CU", Name = "Camaguey" },
            new() { CId = "CU", Name = "Ciego de Avila" },
            new() { CId = "CU", Name = "Cienfuegos" },
            new() { CId = "CU", Name = "Ciudad de La Habana" },
            new() { CId = "CU", Name = "Granma" },
            new() { CId = "CU", Name = "Guantanamo" },
            new() { CId = "CU", Name = "Holguin" },
            new() { CId = "CU", Name = "Isla de la Juventud" },
            new() { CId = "CU", Name = "La Habana" },
            new() { CId = "CU", Name = "Las Tunas" },
            new() { CId = "CU", Name = "Matanzas" },
            new() { CId = "CU", Name = "Pinar del Rio" },
            new() { CId = "CU", Name = "Sancti Spiritus" },
            new() { CId = "CU", Name = "Santiago de Cuba" },
            new() { CId = "CU", Name = "Villa Clara" },
            new() { CId = "CY", Name = "Famagusta" },
            new() { CId = "CY", Name = "Kyrenia" },
            new() { CId = "CY", Name = "Larnaca" },
            new() { CId = "CY", Name = "Limassol" },
            new() { CId = "CY", Name = "Nicosia" },
            new() { CId = "CY", Name = "Paphos" },
            new() { CId = "CZ", Name = "Ústecký" },
            new() { CId = "CZ", Name = "Jihočeský" },
            new() { CId = "CZ", Name = "Jihomoravský" },
            new() { CId = "CZ", Name = "Karlovarský" },
            new() { CId = "CZ", Name = "Královehradecký" },
            new() { CId = "CZ", Name = "Liberecký" },
            new() { CId = "CZ", Name = "Moravskoslezský" },
            new() { CId = "CZ", Name = "Olomoucký" },
            new() { CId = "CZ", Name = "Pardubický" },
            new() { CId = "CZ", Name = "Plzeňský" },
            new() { CId = "CZ", Name = "Praha" },
            new() { CId = "CZ", Name = "Středočeský" },
            new() { CId = "CZ", Name = "Vysočina" },
            new() { CId = "CZ", Name = "Zlínský" },
            new() { CId = "DK", Name = "Arhus" },
            new() { CId = "DK", Name = "Bornholm" },
            new() { CId = "DK", Name = "Copenhagen" },
            new() { CId = "DK", Name = "Faroe Islands" },
            new() { CId = "DK", Name = "Frederiksborg" },
            new() { CId = "DK", Name = "Fyn" },
            new() { CId = "DK", Name = "Kobenhavn" },
            new() { CId = "DK", Name = "Nordjylland" },
            new() { CId = "DK", Name = "Ribe" },
            new() { CId = "DK", Name = "Ringkobing" },
            new() { CId = "DK", Name = "Roskilde" },
            new() { CId = "DK", Name = "Sonderjylland" },
            new() { CId = "DK", Name = "Storstrom" },
            new() { CId = "DK", Name = "Vejle" },
            new() { CId = "DK", Name = "Vestj&aelig;lland" },
            new() { CId = "DK", Name = "Viborg" },
            new() { CId = "DJ", Name = "'Ali Sabih" },
            new() { CId = "DJ", Name = "Dikhil" },
            new() { CId = "DJ", Name = "Djibouti" },
            new() { CId = "DJ", Name = "Obock" },
            new() { CId = "DJ", Name = "Tadjoura" },
            new() { CId = "DM", Name = "Saint Andrew Parish" },
            new() { CId = "DM", Name = "Saint David Parish" },
            new() { CId = "DM", Name = "Saint George Parish" },
            new() { CId = "DM", Name = "Saint John Parish" },
            new() { CId = "DM", Name = "Saint Joseph Parish" },
            new() { CId = "DM", Name = "Saint Luke Parish" },
            new() { CId = "DM", Name = "Saint Mark Parish" },
            new() { CId = "DM", Name = "Saint Patrick Parish" },
            new() { CId = "DM", Name = "Saint Paul Parish" },
            new() { CId = "DM", Name = "Saint Peter Parish" },
            new() { CId = "DO", Name = "Distrito Nacional" },
            new() { CId = "DO", Name = "Azua" },
            new() { CId = "DO", Name = "Baoruco" },
            new() { CId = "DO", Name = "Barahona" },
            new() { CId = "DO", Name = "Dajabon" },
            new() { CId = "DO", Name = "Duarte" },
            new() { CId = "DO", Name = "Elias Pina" },
            new() { CId = "DO", Name = "El Seybo" },
            new() { CId = "DO", Name = "Espaillat" },
            new() { CId = "DO", Name = "Hato Mayor" },
            new() { CId = "DO", Name = "Independencia" },
            new() { CId = "DO", Name = "La Altagracia" },
            new() { CId = "DO", Name = "La Romana" },
            new() { CId = "DO", Name = "La Vega" },
            new() { CId = "DO", Name = "Maria Trinidad Sanchez" },
            new() { CId = "DO", Name = "Monsenor Nouel" },
            new() { CId = "DO", Name = "Monte Cristi" },
            new() { CId = "DO", Name = "Monte Plata" },
            new() { CId = "DO", Name = "Pedernales" },
            new() { CId = "DO", Name = "Peravia (Bani)" },
            new() { CId = "DO", Name = "Puerto Plata" },
            new() { CId = "DO", Name = "Salcedo" },
            new() { CId = "DO", Name = "Samana" },
            new() { CId = "DO", Name = "Sanchez Ramirez" },
            new() { CId = "DO", Name = "San Cristobal" },
            new() { CId = "DO", Name = "San Jose de Ocoa" },
            new() { CId = "DO", Name = "San Juan" },
            new() { CId = "DO", Name = "San Pedro de Macoris" },
            new() { CId = "DO", Name = "Santiago" },
            new() { CId = "DO", Name = "Santiago Rodriguez" },
            new() { CId = "DO", Name = "Santo Domingo" },
            new() { CId = "DO", Name = "Valverde" },
            new() { CId = "TL", Name = "Aileu" },
            new() { CId = "TL", Name = "Ainaro" },
            new() { CId = "TL", Name = "Baucau" },
            new() { CId = "TL", Name = "Bobonaro" },
            new() { CId = "TL", Name = "Cova Lima" },
            new() { CId = "TL", Name = "Dili" },
            new() { CId = "TL", Name = "Ermera" },
            new() { CId = "TL", Name = "Lautem" },
            new() { CId = "TL", Name = "Liquica" },
            new() { CId = "TL", Name = "Manatuto" },
            new() { CId = "TL", Name = "Manufahi" },
            new() { CId = "TL", Name = "Oecussi" },
            new() { CId = "TL", Name = "Viqueque" },
            new() { CId = "EC", Name = "Azuay" },
            new() { CId = "EC", Name = "Bolivar" },
            new() { CId = "EC", Name = "Ca&ntilde;ar" },
            new() { CId = "EC", Name = "Carchi" },
            new() { CId = "EC", Name = "Chimborazo" },
            new() { CId = "EC", Name = "Cotopaxi" },
            new() { CId = "EC", Name = "El Oro" },
            new() { CId = "EC", Name = "Esmeraldas" },
            new() { CId = "EC", Name = "Gal&aacute;pagos" },
            new() { CId = "EC", Name = "Guayas" },
            new() { CId = "EC", Name = "Imbabura" },
            new() { CId = "EC", Name = "Loja" },
            new() { CId = "EC", Name = "Los Rios" },
            new() { CId = "EC", Name = "Manab&iacute;" },
            new() { CId = "EC", Name = "Morona Santiago" },
            new() { CId = "EC", Name = "Napo" },
            new() { CId = "EC", Name = "Orellana" },
            new() { CId = "EC", Name = "Pastaza" },
            new() { CId = "EC", Name = "Pichincha" },
            new() { CId = "EC", Name = "Sucumb&iacute;os" },
            new() { CId = "EC", Name = "Tungurahua" },
            new() { CId = "EC", Name = "Zamora Chinchipe" },
            new() { CId = "EG", Name = "Ad Daqahliyah" },
            new() { CId = "EG", Name = "Al Bahr al Ahmar" },
            new() { CId = "EG", Name = "Al Buhayrah" },
            new() { CId = "EG", Name = "Al Fayyum" },
            new() { CId = "EG", Name = "Al Gharbiyah" },
            new() { CId = "EG", Name = "Al Iskandariyah" },
            new() { CId = "EG", Name = "Al Isma'iliyah" },
            new() { CId = "EG", Name = "Al Jizah" },
            new() { CId = "EG", Name = "Al Minufiyah" },
            new() { CId = "EG", Name = "Al Minya" },
            new() { CId = "EG", Name = "Al Qahirah" },
            new() { CId = "EG", Name = "Al Qalyubiyah" },
            new() { CId = "EG", Name = "Al Wadi al Jadid" },
            new() { CId = "EG", Name = "Ash Sharqiyah" },
            new() { CId = "EG", Name = "As Suways" },
            new() { CId = "EG", Name = "Aswan" },
            new() { CId = "EG", Name = "Asyut" },
            new() { CId = "EG", Name = "Bani Suwayf" },
            new() { CId = "EG", Name = "Bur Sa'id" },
            new() { CId = "EG", Name = "Dumyat" },
            new() { CId = "EG", Name = "Janub Sina'" },
            new() { CId = "EG", Name = "Kafr ash Shaykh" },
            new() { CId = "EG", Name = "Matruh" },
            new() { CId = "EG", Name = "Qina" },
            new() { CId = "EG", Name = "Shamal Sina'" },
            new() { CId = "EG", Name = "Suhaj" },
            new() { CId = "SV", Name = "Ahuachapan" },
            new() { CId = "SV", Name = "Cabanas" },
            new() { CId = "SV", Name = "Chalatenango" },
            new() { CId = "SV", Name = "Cuscatlan" },
            new() { CId = "SV", Name = "La Libertad" },
            new() { CId = "SV", Name = "La Paz" },
            new() { CId = "SV", Name = "La Union" },
            new() { CId = "SV", Name = "Morazan" },
            new() { CId = "SV", Name = "San Miguel" },
            new() { CId = "SV", Name = "San Salvador" },
            new() { CId = "SV", Name = "San Vicente" },
            new() { CId = "SV", Name = "Santa Ana" },
            new() { CId = "SV", Name = "Sonsonate" },
            new() { CId = "SV", Name = "Usulutan" },
            new() { CId = "GQ", Name = "Provincia Annobon" },
            new() { CId = "GQ", Name = "Provincia Bioko Norte" },
            new() { CId = "GQ", Name = "Provincia Bioko Sur" },
            new() { CId = "GQ", Name = "Provincia Centro Sur" },
            new() { CId = "GQ", Name = "Provincia Kie-Ntem" },
            new() { CId = "GQ", Name = "Provincia Litoral" },
            new() { CId = "GQ", Name = "Provincia Wele-Nzas" },
            new() { CId = "ER", Name = "Central (Maekel)" },
            new() { CId = "ER", Name = "Anseba (Keren)" },
            new() { CId = "ER", Name = "Southern Red Sea (Debub-Keih-Bahri)" },
            new() { CId = "ER", Name = "Northern Red Sea (Semien-Keih-Bahri)" },
            new() { CId = "ER", Name = "Southern (Debub)" },
            new() { CId = "ER", Name = "Gash-Barka (Barentu)" },
            new() { CId = "EE", Name = "Harjumaa (Tallinn)" },
            new() { CId = "EE", Name = "Hiiumaa (Kardla)" },
            new() { CId = "EE", Name = "Ida-Virumaa (Johvi)" },
            new() { CId = "EE", Name = "Jarvamaa (Paide)" },
            new() { CId = "EE", Name = "Jogevamaa (Jogeva)" },
            new() { CId = "EE", Name = "Laane-Virumaa (Rakvere)" },
            new() { CId = "EE", Name = "Laanemaa (Haapsalu)" },
            new() { CId = "EE", Name = "Parnumaa (Parnu)" },
            new() { CId = "EE", Name = "Polvamaa (Polva)" },
            new() { CId = "EE", Name = "Raplamaa (Rapla)" },
            new() { CId = "EE", Name = "Saaremaa (Kuessaare)" },
            new() { CId = "EE", Name = "Tartumaa (Tartu)" },
            new() { CId = "EE", Name = "Valgamaa (Valga)" },
            new() { CId = "EE", Name = "Viljandimaa (Viljandi)" },
            new() { CId = "EE", Name = "Vorumaa (Voru)" },
            new() { CId = "ET", Name = "Afar" },
            new() { CId = "ET", Name = "Amhara" },
            new() { CId = "ET", Name = "Benishangul-Gumaz" },
            new() { CId = "ET", Name = "Gambela" },
            new() { CId = "ET", Name = "Hariai" },
            new() { CId = "ET", Name = "Oromia" },
            new() { CId = "ET", Name = "Somali" },
            new() { CId = "ET", Name = "Southern Nations" },
            new() { CId = "ET", Name = "Tigray" },
            new() { CId = "ET", Name = "Addis Ababa" },
            new() { CId = "ET", Name = "Dire Dawa" },
            new() { CId = "FJ", Name = "Central Division" },
            new() { CId = "FJ", Name = "Northern Division" },
            new() { CId = "FJ", Name = "Eastern Division" },
            new() { CId = "FJ", Name = "Western Division" },
            new() { CId = "FJ", Name = "Rotuma" },
            new() { CId = "FI", Name = "Ahvenanmaan lääni" },
            new() { CId = "FI", Name = "Etelä-Suomen lääni" },
            new() { CId = "FI", Name = "Itä-Suomen lääni" },
            new() { CId = "FI", Name = "Länsi-Suomen lääni" },
            new() { CId = "FI", Name = "Lapin lääni" },
            new() { CId = "FI", Name = "Oulun lääni" },
            new() { CId = "FR", Name = "Ain" },
            new() { CId = "FR", Name = "Aisne" },
            new() { CId = "FR", Name = "Allier" },
            new() { CId = "FR", Name = "Alpes de Haute Provence" },
            new() { CId = "FR", Name = "Hautes-Alpes" },
            new() { CId = "FR", Name = "Alpes Maritimes" },
            new() { CId = "FR", Name = "Ard&egrave;che" },
            new() { CId = "FR", Name = "Ardennes" },
            new() { CId = "FR", Name = "Ari&egrave;ge" },
            new() { CId = "FR", Name = "Aube" },
            new() { CId = "FR", Name = "Aude" },
            new() { CId = "FR", Name = "Aveyron" },
            new() { CId = "FR", Name = "Bouches du Rh&ocirc;ne" },
            new() { CId = "FR", Name = "Calvados" },
            new() { CId = "FR", Name = "Cantal" },
            new() { CId = "FR", Name = "Charente" },
            new() { CId = "FR", Name = "Charente Maritime" },
            new() { CId = "FR", Name = "Cher" },
            new() { CId = "FR", Name = "Corr&egrave;ze" },
            new() { CId = "FR", Name = "Corse du Sud" },
            new() { CId = "FR", Name = "Haute Corse" },
            new() { CId = "FR", Name = "C&ocirc;te d&#039;or" },
            new() { CId = "FR", Name = "C&ocirc;tes d&#039;Armor" },
            new() { CId = "FR", Name = "Creuse" },
            new() { CId = "FR", Name = "Dordogne" },
            new() { CId = "FR", Name = "Doubs" },
            new() { CId = "FR", Name = "Dr&ocirc;me" },
            new() { CId = "FR", Name = "Eure" },
            new() { CId = "FR", Name = "Eure et Loir" },
            new() { CId = "FR", Name = "Finist&egrave;re" },
            new() { CId = "FR", Name = "Gard" },
            new() { CId = "FR", Name = "Haute Garonne" },
            new() { CId = "FR", Name = "Gers" },
            new() { CId = "FR", Name = "Gironde" },
            new() { CId = "FR", Name = "H&eacute;rault" },
            new() { CId = "FR", Name = "Ille et Vilaine" },
            new() { CId = "FR", Name = "Indre" },
            new() { CId = "FR", Name = "Indre et Loire" },
            new() { CId = "FR", Name = "Is&eacute;re" },
            new() { CId = "FR", Name = "Jura" },
            new() { CId = "FR", Name = "Landes" },
            new() { CId = "FR", Name = "Loir et Cher" },
            new() { CId = "FR", Name = "Loire" },
            new() { CId = "FR", Name = "Haute Loire" },
            new() { CId = "FR", Name = "Loire Atlantique" },
            new() { CId = "FR", Name = "Loiret" },
            new() { CId = "FR", Name = "Lot" },
            new() { CId = "FR", Name = "Lot et Garonne" },
            new() { CId = "FR", Name = "Loz&egrave;re" },
            new() { CId = "FR", Name = "Maine et Loire" },
            new() { CId = "FR", Name = "Manche" },
            new() { CId = "FR", Name = "Marne" },
            new() { CId = "FR", Name = "Haute Marne" },
            new() { CId = "FR", Name = "Mayenne" },
            new() { CId = "FR", Name = "Meurthe et Moselle" },
            new() { CId = "FR", Name = "Meuse" },
            new() { CId = "FR", Name = "Morbihan" },
            new() { CId = "FR", Name = "Moselle" },
            new() { CId = "FR", Name = "Ni&egrave;vre" },
            new() { CId = "FR", Name = "Nord" },
            new() { CId = "FR", Name = "Oise" },
            new() { CId = "FR", Name = "Orne" },
            new() { CId = "FR", Name = "Pas de Calais" },
            new() { CId = "FR", Name = "Puy de D&ocirc;me" },
            new() { CId = "FR", Name = "Pyr&eacute;n&eacute;es Atlantiques" },
            new() { CId = "FR", Name = "Hautes Pyr&eacute;n&eacute;es" },
            new() { CId = "FR", Name = "Pyr&eacute;n&eacute;es Orientales" },
            new() { CId = "FR", Name = "Bas Rhin" },
            new() { CId = "FR", Name = "Haut Rhin" },
            new() { CId = "FR", Name = "Rh&ocirc;ne" },
            new() { CId = "FR", Name = "Haute Sa&ocirc;ne" },
            new() { CId = "FR", Name = "Sa&ocirc;ne et Loire" },
            new() { CId = "FR", Name = "Sarthe" },
            new() { CId = "FR", Name = "Savoie" },
            new() { CId = "FR", Name = "Haute Savoie" },
            new() { CId = "FR", Name = "Paris" },
            new() { CId = "FR", Name = "Seine Maritime" },
            new() { CId = "FR", Name = "Seine et Marne" },
            new() { CId = "FR", Name = "Yvelines" },
            new() { CId = "FR", Name = "Deux S&egrave;vres" },
            new() { CId = "FR", Name = "Somme" },
            new() { CId = "FR", Name = "Tarn" },
            new() { CId = "FR", Name = "Tarn et Garonne" },
            new() { CId = "FR", Name = "Var" },
            new() { CId = "FR", Name = "Vaucluse" },
            new() { CId = "FR", Name = "Vend&eacute;e" },
            new() { CId = "FR", Name = "Vienne" },
            new() { CId = "FR", Name = "Haute Vienne" },
            new() { CId = "FR", Name = "Vosges" },
            new() { CId = "FR", Name = "Yonne" },
            new() { CId = "FR", Name = "Territoire de Belfort" },
            new() { CId = "FR", Name = "Essonne" },
            new() { CId = "FR", Name = "Hauts de Seine" },
            new() { CId = "FR", Name = "Seine St-Denis" },
            new() { CId = "FR", Name = "Val de Marne" },
            new() { CId = "FR", Name = "Val d'Oise" },
            new() { CId = "PF", Name = "Archipel des Marquises" },
            new() { CId = "PF", Name = "Archipel des Tuamotu" },
            new() { CId = "PF", Name = "Archipel des Tubuai" },
            new() { CId = "PF", Name = "Iles du Vent" },
            new() { CId = "PF", Name = "Iles Sous-le-Vent" },
            new() { CId = "TF", Name = "Iles Crozet" },
            new() { CId = "TF", Name = "Iles Kerguelen" },
            new() { CId = "TF", Name = "Ile Amsterdam" },
            new() { CId = "TF", Name = "Ile Saint-Paul" },
            new() { CId = "TF", Name = "Adelie Land" },
            new() { CId = "GA", Name = "Estuaire" },
            new() { CId = "GA", Name = "Haut-Ogooue" },
            new() { CId = "GA", Name = "Moyen-Ogooue" },
            new() { CId = "GA", Name = "Ngounie" },
            new() { CId = "GA", Name = "Nyanga" },
            new() { CId = "GA", Name = "Ogooue-Ivindo" },
            new() { CId = "GA", Name = "Ogooue-Lolo" },
            new() { CId = "GA", Name = "Ogooue-Maritime" },
            new() { CId = "GA", Name = "Woleu-Ntem" },
            new() { CId = "GM", Name = "Banjul" },
            new() { CId = "GM", Name = "Basse" },
            new() { CId = "GM", Name = "Brikama" },
            new() { CId = "GM", Name = "Janjangbure" },
            new() { CId = "GM", Name = "Kanifeng" },
            new() { CId = "GM", Name = "Kerewan" },
            new() { CId = "GM", Name = "Kuntaur" },
            new() { CId = "GM", Name = "Mansakonko" },
            new() { CId = "GM", Name = "Lower River" },
            new() { CId = "GM", Name = "Central River" },
            new() { CId = "GM", Name = "North Bank" },
            new() { CId = "GM", Name = "Upper River" },
            new() { CId = "GM", Name = "Western" },
            new() { CId = "GE", Name = "Abkhazia" },
            new() { CId = "GE", Name = "Ajaria" },
            new() { CId = "GE", Name = "Tbilisi" },
            new() { CId = "GE", Name = "Guria" },
            new() { CId = "GE", Name = "Imereti" },
            new() { CId = "GE", Name = "Kakheti" },
            new() { CId = "GE", Name = "Kvemo Kartli" },
            new() { CId = "GE", Name = "Mtskheta-Mtianeti" },
            new() { CId = "GE", Name = "Racha Lechkhumi and Kvemo Svanet" },
            new() { CId = "GE", Name = "Samegrelo-Zemo Svaneti" },
            new() { CId = "GE", Name = "Samtskhe-Javakheti" },
            new() { CId = "GE", Name = "Shida Kartli" },
            new() { CId = "DE", Name = "Baden-Württemberg" },
            new() { CId = "DE", Name = "Bayern" },
            new() { CId = "DE", Name = "Berlin" },
            new() { CId = "DE", Name = "Brandenburg" },
            new() { CId = "DE", Name = "Bremen" },
            new() { CId = "DE", Name = "Hamburg" },
            new() { CId = "DE", Name = "Hessen" },
            new() { CId = "DE", Name = "Mecklenburg-Vorpommern" },
            new() { CId = "DE", Name = "Niedersachsen" },
            new() { CId = "DE", Name = "Nordrhein-Westfalen" },
            new() { CId = "DE", Name = "Rheinland-Pfalz" },
            new() { CId = "DE", Name = "Saarland" },
            new() { CId = "DE", Name = "Sachsen" },
            new() { CId = "DE", Name = "Sachsen-Anhalt" },
            new() { CId = "DE", Name = "Schleswig-Holstein" },
            new() { CId = "DE", Name = "Thüringen" },
            new() { CId = "GH", Name = "Ashanti Region" },
            new() { CId = "GH", Name = "Brong-Ahafo Region" },
            new() { CId = "GH", Name = "Central Region" },
            new() { CId = "GH", Name = "Eastern Region" },
            new() { CId = "GH", Name = "Greater Accra Region" },
            new() { CId = "GH", Name = "Northern Region" },
            new() { CId = "GH", Name = "Upper East Region" },
            new() { CId = "GH", Name = "Upper West Region" },
            new() { CId = "GH", Name = "Volta Region" },
            new() { CId = "GH", Name = "Western Region" },
            new() { CId = "GR", Name = "Attica" },
            new() { CId = "GR", Name = "Central Greece" },
            new() { CId = "GR", Name = "Central Macedonia" },
            new() { CId = "GR", Name = "Crete" },
            new() { CId = "GR", Name = "East Macedonia and Thrace" },
            new() { CId = "GR", Name = "Epirus" },
            new() { CId = "GR", Name = "Ionian Islands" },
            new() { CId = "GR", Name = "North Aegean" },
            new() { CId = "GR", Name = "Peloponnesos" },
            new() { CId = "GR", Name = "South Aegean" },
            new() { CId = "GR", Name = "Thessaly" },
            new() { CId = "GR", Name = "West Greece" },
            new() { CId = "GR", Name = "West Macedonia" },
            new() { CId = "GL", Name = "Avannaa" },
            new() { CId = "GL", Name = "Tunu" },
            new() { CId = "GL", Name = "Kitaa" },
            new() { CId = "GD", Name = "Saint Andrew" },
            new() { CId = "GD", Name = "Saint David" },
            new() { CId = "GD", Name = "Saint George" },
            new() { CId = "GD", Name = "Saint John" },
            new() { CId = "GD", Name = "Saint Mark" },
            new() { CId = "GD", Name = "Saint Patrick" },
            new() { CId = "GD", Name = "Carriacou" },
            new() { CId = "GD", Name = "Petit Martinique" },
            new() { CId = "GT", Name = "Alta Verapaz" },
            new() { CId = "GT", Name = "Baja Verapaz" },
            new() { CId = "GT", Name = "Chimaltenango" },
            new() { CId = "GT", Name = "Chiquimula" },
            new() { CId = "GT", Name = "El Peten" },
            new() { CId = "GT", Name = "El Progreso" },
            new() { CId = "GT", Name = "El Quiche" },
            new() { CId = "GT", Name = "Escuintla" },
            new() { CId = "GT", Name = "Guatemala" },
            new() { CId = "GT", Name = "Huehuetenango" },
            new() { CId = "GT", Name = "Izabal" },
            new() { CId = "GT", Name = "Jalapa" },
            new() { CId = "GT", Name = "Jutiapa" },
            new() { CId = "GT", Name = "Quetzaltenango" },
            new() { CId = "GT", Name = "Retalhuleu" },
            new() { CId = "GT", Name = "Sacatepequez" },
            new() { CId = "GT", Name = "San Marcos" },
            new() { CId = "GT", Name = "Santa Rosa" },
            new() { CId = "GT", Name = "Solola" },
            new() { CId = "GT", Name = "Suchitepequez" },
            new() { CId = "GT", Name = "Totonicapan" },
            new() { CId = "GT", Name = "Zacapa" },
            new() { CId = "GN", Name = "Conakry" },
            new() { CId = "GN", Name = "Beyla" },
            new() { CId = "GN", Name = "Boffa" },
            new() { CId = "GN", Name = "Boke" },
            new() { CId = "GN", Name = "Coyah" },
            new() { CId = "GN", Name = "Dabola" },
            new() { CId = "GN", Name = "Dalaba" },
            new() { CId = "GN", Name = "Dinguiraye" },
            new() { CId = "GN", Name = "Dubreka" },
            new() { CId = "GN", Name = "Faranah" },
            new() { CId = "GN", Name = "Forecariah" },
            new() { CId = "GN", Name = "Fria" },
            new() { CId = "GN", Name = "Gaoual" },
            new() { CId = "GN", Name = "Gueckedou" },
            new() { CId = "GN", Name = "Kankan" },
            new() { CId = "GN", Name = "Kerouane" },
            new() { CId = "GN", Name = "Kindia" },
            new() { CId = "GN", Name = "Kissidougou" },
            new() { CId = "GN", Name = "Koubia" },
            new() { CId = "GN", Name = "Koundara" },
            new() { CId = "GN", Name = "Kouroussa" },
            new() { CId = "GN", Name = "Labe" },
            new() { CId = "GN", Name = "Lelouma" },
            new() { CId = "GN", Name = "Lola" },
            new() { CId = "GN", Name = "Macenta" },
            new() { CId = "GN", Name = "Mali" },
            new() { CId = "GN", Name = "Mamou" },
            new() { CId = "GN", Name = "Mandiana" },
            new() { CId = "GN", Name = "Nzerekore" },
            new() { CId = "GN", Name = "Pita" },
            new() { CId = "GN", Name = "Siguiri" },
            new() { CId = "GN", Name = "Telimele" },
            new() { CId = "GN", Name = "Tougue" },
            new() { CId = "GN", Name = "Yomou" },
            new() { CId = "GW", Name = "Bafata Region" },
            new() { CId = "GW", Name = "Biombo Region" },
            new() { CId = "GW", Name = "Bissau Region" },
            new() { CId = "GW", Name = "Bolama Region" },
            new() { CId = "GW", Name = "Cacheu Region" },
            new() { CId = "GW", Name = "Gabu Region" },
            new() { CId = "GW", Name = "Oio Region" },
            new() { CId = "GW", Name = "Quinara Region" },
            new() { CId = "GW", Name = "Tombali Region" },
            new() { CId = "GY", Name = "Barima-Waini" },
            new() { CId = "GY", Name = "Cuyuni-Mazaruni" },
            new() { CId = "GY", Name = "Demerara-Mahaica" },
            new() { CId = "GY", Name = "East Berbice-Corentyne" },
            new() { CId = "GY", Name = "Essequibo Islands-West Demerara" },
            new() { CId = "GY", Name = "Mahaica-Berbice" },
            new() { CId = "GY", Name = "Pomeroon-Supenaam" },
            new() { CId = "GY", Name = "Potaro-Siparuni" },
            new() { CId = "GY", Name = "Upper Demerara-Berbice" },
            new() { CId = "GY", Name = "Upper Takutu-Upper Essequibo" },
            new() { CId = "HT", Name = "Artibonite" },
            new() { CId = "HT", Name = "Centre" },
            new() { CId = "HT", Name = "Grand'Anse" },
            new() { CId = "HT", Name = "Nord" },
            new() { CId = "HT", Name = "Nord-Est" },
            new() { CId = "HT", Name = "Nord-Ouest" },
            new() { CId = "HT", Name = "Ouest" },
            new() { CId = "HT", Name = "Sud" },
            new() { CId = "HT", Name = "Sud-Est" },
            new() { CId = "HM", Name = "Flat Island" },
            new() { CId = "HM", Name = "McDonald Island" },
            new() { CId = "HM", Name = "Shag Island" },
            new() { CId = "HM", Name = "Heard Island" },
            new() { CId = "HN", Name = "Atlantida" },
            new() { CId = "HN", Name = "Choluteca" },
            new() { CId = "HN", Name = "Colon" },
            new() { CId = "HN", Name = "Comayagua" },
            new() { CId = "HN", Name = "Copan" },
            new() { CId = "HN", Name = "Cortes" },
            new() { CId = "HN", Name = "El Paraiso" },
            new() { CId = "HN", Name = "Francisco Morazan" },
            new() { CId = "HN", Name = "Gracias a Dios" },
            new() { CId = "HN", Name = "Intibuca" },
            new() { CId = "HN", Name = "Islas de la Bahia (Bay Islands)" },
            new() { CId = "HN", Name = "La Paz" },
            new() { CId = "HN", Name = "Lempira" },
            new() { CId = "HN", Name = "Ocotepeque" },
            new() { CId = "HN", Name = "Olancho" },
            new() { CId = "HN", Name = "Santa Barbara" },
            new() { CId = "HN", Name = "Valle" },
            new() { CId = "HN", Name = "Yoro" },
            new() { CId = "IS", Name = "Austurland" },
            new() { CId = "IS", Name = "Hofuoborgarsvaeoi" },
            new() { CId = "IS", Name = "Norourland eystra" },
            new() { CId = "IS", Name = "Norourland vestra" },
            new() { CId = "IS", Name = "Suourland" },
            new() { CId = "IS", Name = "Suournes" },
            new() { CId = "IS", Name = "Vestfiroir" },
            new() { CId = "IS", Name = "Vesturland" },
            new() { CId = "IN", Name = "Andaman and Nicobar Islands" },
            new() { CId = "IN", Name = "Andhra Pradesh" },
            new() { CId = "IN", Name = "Arunachal Pradesh" },
            new() { CId = "IN", Name = "Assam" },
            new() { CId = "IN", Name = "Bihar" },
            new() { CId = "IN", Name = "Chandigarh" },
            new() { CId = "IN", Name = "Dadra and Nagar Haveli" },
            new() { CId = "IN", Name = "Daman and Diu" },
            new() { CId = "IN", Name = "Delhi" },
            new() { CId = "IN", Name = "Goa" },
            new() { CId = "IN", Name = "Gujarat" },
            new() { CId = "IN", Name = "Haryana" },
            new() { CId = "IN", Name = "Himachal Pradesh" },
            new() { CId = "IN", Name = "Jammu and Kashmir" },
            new() { CId = "IN", Name = "Karnataka" },
            new() { CId = "IN", Name = "Kerala" },
            new() { CId = "IN", Name = "Lakshadweep Islands" },
            new() { CId = "IN", Name = "Madhya Pradesh" },
            new() { CId = "IN", Name = "Maharashtra" },
            new() { CId = "IN", Name = "Manipur" },
            new() { CId = "IN", Name = "Meghalaya" },
            new() { CId = "IN", Name = "Mizoram" },
            new() { CId = "IN", Name = "Nagaland" },
            new() { CId = "IN", Name = "Orissa" },
            new() { CId = "IN", Name = "Puducherry" },
            new() { CId = "IN", Name = "Punjab" },
            new() { CId = "IN", Name = "Rajasthan" },
            new() { CId = "IN", Name = "Sikkim" },
            new() { CId = "IN", Name = "Tamil Nadu" },
            new() { CId = "IN", Name = "Tripura" },
            new() { CId = "IN", Name = "Uttar Pradesh" },
            new() { CId = "IN", Name = "West Bengal" },
            new() { CId = "ID", Name = "Aceh" },
            new() { CId = "ID", Name = "Bali" },
            new() { CId = "ID", Name = "Banten" },
            new() { CId = "ID", Name = "Bengkulu" },
            new() { CId = "ID", Name = "Kalimantan Utara" },
            new() { CId = "ID", Name = "Gorontalo" },
            new() { CId = "ID", Name = "Jakarta" },
            new() { CId = "ID", Name = "Jambi" },
            new() { CId = "ID", Name = "Jawa Barat" },
            new() { CId = "ID", Name = "Jawa Tengah" },
            new() { CId = "ID", Name = "Jawa Timur" },
            new() { CId = "ID", Name = "Kalimantan Barat" },
            new() { CId = "ID", Name = "Kalimantan Selatan" },
            new() { CId = "ID", Name = "Kalimantan Tengah" },
            new() { CId = "ID", Name = "Kalimantan Timur" },
            new() { CId = "ID", Name = "Kepulauan Bangka Belitung" },
            new() { CId = "ID", Name = "Lampung" },
            new() { CId = "ID", Name = "Maluku" },
            new() { CId = "ID", Name = "Maluku Utara" },
            new() { CId = "ID", Name = "Nusa Tenggara Barat" },
            new() { CId = "ID", Name = "Nusa Tenggara Timur" },
            new() { CId = "ID", Name = "Papua" },
            new() { CId = "ID", Name = "Riau" },
            new() { CId = "ID", Name = "Sulawesi Selatan" },
            new() { CId = "ID", Name = "Sulawesi Tengah" },
            new() { CId = "ID", Name = "Sulawesi Tenggara" },
            new() { CId = "ID", Name = "Sulawesi Utara" },
            new() { CId = "ID", Name = "Sumatera Barat" },
            new() { CId = "ID", Name = "Sumatera Selatan" },
            new() { CId = "ID", Name = "Sumatera Utara" },
            new() { CId = "ID", Name = "Yogyakarta" },
            new() { CId = "IR", Name = "Tehran" },
            new() { CId = "IR", Name = "Qom" },
            new() { CId = "IR", Name = "Markazi" },
            new() { CId = "IR", Name = "Qazvin" },
            new() { CId = "IR", Name = "Gilan" },
            new() { CId = "IR", Name = "Ardabil" },
            new() { CId = "IR", Name = "Zanjan" },
            new() { CId = "IR", Name = "East Azarbaijan" },
            new() { CId = "IR", Name = "West Azarbaijan" },
            new() { CId = "IR", Name = "Kurdistan" },
            new() { CId = "IR", Name = "Hamadan" },
            new() { CId = "IR", Name = "Kermanshah" },
            new() { CId = "IR", Name = "Ilam" },
            new() { CId = "IR", Name = "Lorestan" },
            new() { CId = "IR", Name = "Khuzestan" },
            new() { CId = "IR", Name = "Chahar Mahaal and Bakhtiari" },
            new() { CId = "IR", Name = "Kohkiluyeh and Buyer Ahmad" },
            new() { CId = "IR", Name = "Bushehr" },
            new() { CId = "IR", Name = "Fars" },
            new() { CId = "IR", Name = "Hormozgan" },
            new() { CId = "IR", Name = "Sistan and Baluchistan" },
            new() { CId = "IR", Name = "Kerman" },
            new() { CId = "IR", Name = "Yazd" },
            new() { CId = "IR", Name = "Esfahan" },
            new() { CId = "IR", Name = "Semnan" },
            new() { CId = "IR", Name = "Mazandaran" },
            new() { CId = "IR", Name = "Golestan" },
            new() { CId = "IR", Name = "North Khorasan" },
            new() { CId = "IR", Name = "Razavi Khorasan" },
            new() { CId = "IR", Name = "South Khorasan" },
            new() { CId = "IQ", Name = "Baghdad" },
            new() { CId = "IQ", Name = "Salah ad Din" },
            new() { CId = "IQ", Name = "Diyala" },
            new() { CId = "IQ", Name = "Wasit" },
            new() { CId = "IQ", Name = "Maysan" },
            new() { CId = "IQ", Name = "Al Basrah" },
            new() { CId = "IQ", Name = "Dhi Qar" },
            new() { CId = "IQ", Name = "Al Muthanna" },
            new() { CId = "IQ", Name = "Al Qadisyah" },
            new() { CId = "IQ", Name = "Babil" },
            new() { CId = "IQ", Name = "Al Karbala" },
            new() { CId = "IQ", Name = "An Najaf" },
            new() { CId = "IQ", Name = "Al Anbar" },
            new() { CId = "IQ", Name = "Ninawa" },
            new() { CId = "IQ", Name = "Dahuk" },
            new() { CId = "IQ", Name = "Arbil" },
            new() { CId = "IQ", Name = "At Ta'mim" },
            new() { CId = "IQ", Name = "As Sulaymaniyah" },
            new() { CId = "IE", Name = "Carlow" },
            new() { CId = "IE", Name = "Cavan" },
            new() { CId = "IE", Name = "Clare" },
            new() { CId = "IE", Name = "Cork" },
            new() { CId = "IE", Name = "Donegal" },
            new() { CId = "IE", Name = "Dublin" },
            new() { CId = "IE", Name = "Galway" },
            new() { CId = "IE", Name = "Kerry" },
            new() { CId = "IE", Name = "Kildare" },
            new() { CId = "IE", Name = "Kilkenny" },
            new() { CId = "IE", Name = "Laois" },
            new() { CId = "IE", Name = "Leitrim" },
            new() { CId = "IE", Name = "Limerick" },
            new() { CId = "IE", Name = "Longford" },
            new() { CId = "IE", Name = "Louth" },
            new() { CId = "IE", Name = "Mayo" },
            new() { CId = "IE", Name = "Meath" },
            new() { CId = "IE", Name = "Monaghan" },
            new() { CId = "IE", Name = "Offaly" },
            new() { CId = "IE", Name = "Roscommon" },
            new() { CId = "IE", Name = "Sligo" },
            new() { CId = "IE", Name = "Tipperary" },
            new() { CId = "IE", Name = "Waterford" },
            new() { CId = "IE", Name = "Westmeath" },
            new() { CId = "IE", Name = "Wexford" },
            new() { CId = "IE", Name = "Wicklow" },
            new() { CId = "IL", Name = "Be'er Sheva" },
            new() { CId = "IL", Name = "Bika'at Hayarden" },
            new() { CId = "IL", Name = "Eilat and Arava" },
            new() { CId = "IL", Name = "Galil" },
            new() { CId = "IL", Name = "Haifa" },
            new() { CId = "IL", Name = "Jehuda Mountains" },
            new() { CId = "IL", Name = "Jerusalem" },
            new() { CId = "IL", Name = "Negev" },
            new() { CId = "IL", Name = "Semaria" },
            new() { CId = "IL", Name = "Sharon" },
            new() { CId = "IL", Name = "Tel Aviv (Gosh Dan)" },
            new() { CId = "JM", Name = "Clarendon Parish" },
            new() { CId = "JM", Name = "Hanover Parish" },
            new() { CId = "JM", Name = "Kingston Parish" },
            new() { CId = "JM", Name = "Manchester Parish" },
            new() { CId = "JM", Name = "Portland Parish" },
            new() { CId = "JM", Name = "Saint Andrew Parish" },
            new() { CId = "JM", Name = "Saint Ann Parish" },
            new() { CId = "JM", Name = "Saint Catherine Parish" },
            new() { CId = "JM", Name = "Saint Elizabeth Parish" },
            new() { CId = "JM", Name = "Saint James Parish" },
            new() { CId = "JM", Name = "Saint Mary Parish" },
            new() { CId = "JM", Name = "Saint Thomas Parish" },
            new() { CId = "JM", Name = "Trelawny Parish" },
            new() { CId = "JM", Name = "Westmoreland Parish" },
            new() { CId = "JP", Name = "Aichi" },
            new() { CId = "JP", Name = "Akita" },
            new() { CId = "JP", Name = "Aomori" },
            new() { CId = "JP", Name = "Chiba" },
            new() { CId = "JP", Name = "Ehime" },
            new() { CId = "JP", Name = "Fukui" },
            new() { CId = "JP", Name = "Fukuoka" },
            new() { CId = "JP", Name = "Fukushima" },
            new() { CId = "JP", Name = "Gifu" },
            new() { CId = "JP", Name = "Gumma" },
            new() { CId = "JP", Name = "Hiroshima" },
            new() { CId = "JP", Name = "Hokkaido" },
            new() { CId = "JP", Name = "Hyogo" },
            new() { CId = "JP", Name = "Ibaraki" },
            new() { CId = "JP", Name = "Ishikawa" },
            new() { CId = "JP", Name = "Iwate" },
            new() { CId = "JP", Name = "Kagawa" },
            new() { CId = "JP", Name = "Kagoshima" },
            new() { CId = "JP", Name = "Kanagawa" },
            new() { CId = "JP", Name = "Kochi" },
            new() { CId = "JP", Name = "Kumamoto" },
            new() { CId = "JP", Name = "Kyoto" },
            new() { CId = "JP", Name = "Mie" },
            new() { CId = "JP", Name = "Miyagi" },
            new() { CId = "JP", Name = "Miyazaki" },
            new() { CId = "JP", Name = "Nagano" },
            new() { CId = "JP", Name = "Nagasaki" },
            new() { CId = "JP", Name = "Nara" },
            new() { CId = "JP", Name = "Niigata" },
            new() { CId = "JP", Name = "Oita" },
            new() { CId = "JP", Name = "Okayama" },
            new() { CId = "JP", Name = "Okinawa" },
            new() { CId = "JP", Name = "Osaka" },
            new() { CId = "JP", Name = "Saga" },
            new() { CId = "JP", Name = "Saitama" },
            new() { CId = "JP", Name = "Shiga" },
            new() { CId = "JP", Name = "Shimane" },
            new() { CId = "JP", Name = "Shizuoka" },
            new() { CId = "JP", Name = "Tochigi" },
            new() { CId = "JP", Name = "Tokushima" },
            new() { CId = "JP", Name = "Tokyo" },
            new() { CId = "JP", Name = "Tottori" },
            new() { CId = "JP", Name = "Toyama" },
            new() { CId = "JP", Name = "Wakayama" },
            new() { CId = "JP", Name = "Yamagata" },
            new() { CId = "JP", Name = "Yamaguchi" },
            new() { CId = "JP", Name = "Yamanashi" },
            new() { CId = "JO", Name = "'Amman" },
            new() { CId = "JO", Name = "Ajlun" },
            new() { CId = "JO", Name = "Al 'Aqabah" },
            new() { CId = "JO", Name = "Al Balqa'" },
            new() { CId = "JO", Name = "Al Karak" },
            new() { CId = "JO", Name = "Al Mafraq" },
            new() { CId = "JO", Name = "At Tafilah" },
            new() { CId = "JO", Name = "Az Zarqa'" },
            new() { CId = "JO", Name = "Irbid" },
            new() { CId = "JO", Name = "Jarash" },
            new() { CId = "JO", Name = "Ma'an" },
            new() { CId = "JO", Name = "Madaba" },
            new() { CId = "KZ", Name = "Almaty" },
            new() { CId = "KZ", Name = "Almaty City" },
            new() { CId = "KZ", Name = "Aqmola" },
            new() { CId = "KZ", Name = "Aqtobe" },
            new() { CId = "KZ", Name = "Astana City" },
            new() { CId = "KZ", Name = "Atyrau" },
            new() { CId = "KZ", Name = "Batys Qazaqstan" },
            new() { CId = "KZ", Name = "Bayqongyr City" },
            new() { CId = "KZ", Name = "Mangghystau" },
            new() { CId = "KZ", Name = "Ongtustik Qazaqstan" },
            new() { CId = "KZ", Name = "Pavlodar" },
            new() { CId = "KZ", Name = "Qaraghandy" },
            new() { CId = "KZ", Name = "Qostanay" },
            new() { CId = "KZ", Name = "Qyzylorda" },
            new() { CId = "KZ", Name = "Shyghys Qazaqstan" },
            new() { CId = "KZ", Name = "Soltustik Qazaqstan" },
            new() { CId = "KZ", Name = "Zhambyl" },
            new() { CId = "KE", Name = "Central" },
            new() { CId = "KE", Name = "Coast" },
            new() { CId = "KE", Name = "Eastern" },
            new() { CId = "KE", Name = "Nairobi Area" },
            new() { CId = "KE", Name = "North Eastern" },
            new() { CId = "KE", Name = "Nyanza" },
            new() { CId = "KE", Name = "Rift Valley" },
            new() { CId = "KE", Name = "Western" },
            new() { CId = "KI", Name = "Abaiang" },
            new() { CId = "KI", Name = "Abemama" },
            new() { CId = "KI", Name = "Aranuka" },
            new() { CId = "KI", Name = "Arorae" },
            new() { CId = "KI", Name = "Banaba" },
            new() { CId = "KI", Name = "Beru" },
            new() { CId = "KI", Name = "Butaritari" },
            new() { CId = "KI", Name = "Kanton" },
            new() { CId = "KI", Name = "Kiritimati" },
            new() { CId = "KI", Name = "Kuria" },
            new() { CId = "KI", Name = "Maiana" },
            new() { CId = "KI", Name = "Makin" },
            new() { CId = "KI", Name = "Marakei" },
            new() { CId = "KI", Name = "Nikunau" },
            new() { CId = "KI", Name = "Nonouti" },
            new() { CId = "KI", Name = "Onotoa" },
            new() { CId = "KI", Name = "Tabiteuea" },
            new() { CId = "KI", Name = "Tabuaeran" },
            new() { CId = "KI", Name = "Tamana" },
            new() { CId = "KI", Name = "Tarawa" },
            new() { CId = "KI", Name = "Teraina" },
            new() { CId = "KP", Name = "Chagang-do" },
            new() { CId = "KP", Name = "Hamgyong-bukto" },
            new() { CId = "KP", Name = "Hamgyong-namdo" },
            new() { CId = "KP", Name = "Hwanghae-bukto" },
            new() { CId = "KP", Name = "Hwanghae-namdo" },
            new() { CId = "KP", Name = "Kangwon-do" },
            new() { CId = "KP", Name = "P'yongan-bukto" },
            new() { CId = "KP", Name = "P'yongan-namdo" },
            new() { CId = "KP", Name = "Ryanggang-do (Yanggang-do)" },
            new() { CId = "KP", Name = "Rason Directly Governed City" },
            new() { CId = "KP", Name = "P'yongyang Special City" },
            new() { CId = "KR", Name = "Ch'ungch'ong-bukto" },
            new() { CId = "KR", Name = "Ch'ungch'ong-namdo" },
            new() { CId = "KR", Name = "Cheju-do" },
            new() { CId = "KR", Name = "Cholla-bukto" },
            new() { CId = "KR", Name = "Cholla-namdo" },
            new() { CId = "KR", Name = "Inch'on-gwangyoksi" },
            new() { CId = "KR", Name = "Kangwon-do" },
            new() { CId = "KR", Name = "Kwangju-gwangyoksi" },
            new() { CId = "KR", Name = "Kyonggi-do" },
            new() { CId = "KR", Name = "Kyongsang-bukto" },
            new() { CId = "KR", Name = "Kyongsang-namdo" },
            new() { CId = "KR", Name = "Pusan-gwangyoksi" },
            new() { CId = "KR", Name = "Soul-t'ukpyolsi" },
            new() { CId = "KR", Name = "Taegu-gwangyoksi" },
            new() { CId = "KR", Name = "Taejon-gwangyoksi" },
            new() { CId = "KW", Name = "Al 'Asimah" },
            new() { CId = "KW", Name = "Al Ahmadi" },
            new() { CId = "KW", Name = "Al Farwaniyah" },
            new() { CId = "KW", Name = "Al Jahra'" },
            new() { CId = "KW", Name = "Hawalli" },
            new() { CId = "KG", Name = "Bishkek" },
            new() { CId = "KG", Name = "Batken" },
            new() { CId = "KG", Name = "Chu" },
            new() { CId = "KG", Name = "Jalal-Abad" },
            new() { CId = "KG", Name = "Naryn" },
            new() { CId = "KG", Name = "Osh" },
            new() { CId = "KG", Name = "Talas" },
            new() { CId = "KG", Name = "Ysyk-Kol" },
            new() { CId = "LA", Name = "Vientiane" },
            new() { CId = "LA", Name = "Attapu" },
            new() { CId = "LA", Name = "Bokeo" },
            new() { CId = "LA", Name = "Bolikhamxai" },
            new() { CId = "LA", Name = "Champasak" },
            new() { CId = "LA", Name = "Houaphan" },
            new() { CId = "LA", Name = "Khammouan" },
            new() { CId = "LA", Name = "Louang Namtha" },
            new() { CId = "LA", Name = "Louangphabang" },
            new() { CId = "LA", Name = "Oudomxai" },
            new() { CId = "LA", Name = "Phongsali" },
            new() { CId = "LA", Name = "Salavan" },
            new() { CId = "LA", Name = "Savannakhet" },
            new() { CId = "LA", Name = "Xaignabouli" },
            new() { CId = "LA", Name = "Xekong" },
            new() { CId = "LA", Name = "Xiangkhoang" },
            new() { CId = "LA", Name = "Xaisomboun" },
            new() { CId = "LS", Name = "Berea" },
            new() { CId = "LS", Name = "Butha-Buthe" },
            new() { CId = "LS", Name = "Leribe" },
            new() { CId = "LS", Name = "Mafeteng" },
            new() { CId = "LS", Name = "Maseru" },
            new() { CId = "LS", Name = "Mohale's Hoek" },
            new() { CId = "LS", Name = "Mokhotlong" },
            new() { CId = "LS", Name = "Qacha's Nek" },
            new() { CId = "LS", Name = "Quthing" },
            new() { CId = "LS", Name = "Thaba-Tseka" },
            new() { CId = "LR", Name = "Bomi" },
            new() { CId = "LR", Name = "Bong" },
            new() { CId = "LR", Name = "Grand Bassa" },
            new() { CId = "LR", Name = "Grand Cape Mount" },
            new() { CId = "LR", Name = "Grand Gedeh" },
            new() { CId = "LR", Name = "Grand Kru" },
            new() { CId = "LR", Name = "Lofa" },
            new() { CId = "LR", Name = "Margibi" },
            new() { CId = "LR", Name = "Maryland" },
            new() { CId = "LR", Name = "Montserrado" },
            new() { CId = "LR", Name = "Nimba" },
            new() { CId = "LR", Name = "River Cess" },
            new() { CId = "LR", Name = "Sinoe" },
            new() { CId = "LY", Name = "Ajdabiya" },
            new() { CId = "LY", Name = "Al 'Aziziyah" },
            new() { CId = "LY", Name = "Al Fatih" },
            new() { CId = "LY", Name = "Al Jabal al Akhdar" },
            new() { CId = "LY", Name = "Al Jufrah" },
            new() { CId = "LY", Name = "Al Khums" },
            new() { CId = "LY", Name = "Al Kufrah" },
            new() { CId = "LY", Name = "An Nuqat al Khams" },
            new() { CId = "LY", Name = "Ash Shati'" },
            new() { CId = "LY", Name = "Awbari" },
            new() { CId = "LY", Name = "Az Zawiyah" },
            new() { CId = "LY", Name = "Banghazi" },
            new() { CId = "LY", Name = "Darnah" },
            new() { CId = "LY", Name = "Ghadamis" },
            new() { CId = "LY", Name = "Gharyan" },
            new() { CId = "LY", Name = "Misratah" },
            new() { CId = "LY", Name = "Murzuq" },
            new() { CId = "LY", Name = "Sabha" },
            new() { CId = "LY", Name = "Sawfajjin" },
            new() { CId = "LY", Name = "Surt" },
            new() { CId = "LY", Name = "Tarabulus (Tripoli)" },
            new() { CId = "LY", Name = "Tarhunah" },
            new() { CId = "LY", Name = "Tubruq" },
            new() { CId = "LY", Name = "Yafran" },
            new() { CId = "LY", Name = "Zlitan" },
            new() { CId = "LI", Name = "Vaduz" },
            new() { CId = "LI", Name = "Schaan" },
            new() { CId = "LI", Name = "Balzers" },
            new() { CId = "LI", Name = "Triesen" },
            new() { CId = "LI", Name = "Eschen" },
            new() { CId = "LI", Name = "Mauren" },
            new() { CId = "LI", Name = "Triesenberg" },
            new() { CId = "LI", Name = "Ruggell" },
            new() { CId = "LI", Name = "Gamprin" },
            new() { CId = "LI", Name = "Schellenberg" },
            new() { CId = "LI", Name = "Planken" },
            new() { CId = "LT", Name = "Alytus" },
            new() { CId = "LT", Name = "Kaunas" },
            new() { CId = "LT", Name = "Klaipeda" },
            new() { CId = "LT", Name = "Marijampole" },
            new() { CId = "LT", Name = "Panevezys" },
            new() { CId = "LT", Name = "Siauliai" },
            new() { CId = "LT", Name = "Taurage" },
            new() { CId = "LT", Name = "Telsiai" },
            new() { CId = "LT", Name = "Utena" },
            new() { CId = "LT", Name = "Vilnius" },
            new() { CId = "LU", Name = "Diekirch" },
            new() { CId = "LU", Name = "Clervaux" },
            new() { CId = "LU", Name = "Redange" },
            new() { CId = "LU", Name = "Vianden" },
            new() { CId = "LU", Name = "Wiltz" },
            new() { CId = "LU", Name = "Grevenmacher" },
            new() { CId = "LU", Name = "Echternach" },
            new() { CId = "LU", Name = "Remich" },
            new() { CId = "LU", Name = "Luxembourg" },
            new() { CId = "LU", Name = "Capellen" },
            new() { CId = "LU", Name = "Esch-sur-Alzette" },
            new() { CId = "LU", Name = "Mersch" },
            new() { CId = "MG", Name = "Antananarivo" },
            new() { CId = "MG", Name = "Antsiranana" },
            new() { CId = "MG", Name = "Fianarantsoa" },
            new() { CId = "MG", Name = "Mahajanga" },
            new() { CId = "MG", Name = "Toamasina" },
            new() { CId = "MG", Name = "Toliara" },
            new() { CId = "MW", Name = "Balaka" },
            new() { CId = "MW", Name = "Blantyre" },
            new() { CId = "MW", Name = "Chikwawa" },
            new() { CId = "MW", Name = "Chiradzulu" },
            new() { CId = "MW", Name = "Chitipa" },
            new() { CId = "MW", Name = "Dedza" },
            new() { CId = "MW", Name = "Dowa" },
            new() { CId = "MW", Name = "Karonga" },
            new() { CId = "MW", Name = "Kasungu" },
            new() { CId = "MW", Name = "Likoma" },
            new() { CId = "MW", Name = "Lilongwe" },
            new() { CId = "MW", Name = "Machinga" },
            new() { CId = "MW", Name = "Mangochi" },
            new() { CId = "MW", Name = "Mchinji" },
            new() { CId = "MW", Name = "Mulanje" },
            new() { CId = "MW", Name = "Mwanza" },
            new() { CId = "MW", Name = "Mzimba" },
            new() { CId = "MW", Name = "Ntcheu" },
            new() { CId = "MW", Name = "Nkhata Bay" },
            new() { CId = "MW", Name = "Nkhotakota" },
            new() { CId = "MW", Name = "Nsanje" },
            new() { CId = "MW", Name = "Ntchisi" },
            new() { CId = "MW", Name = "Phalombe" },
            new() { CId = "MW", Name = "Rumphi" },
            new() { CId = "MW", Name = "Salima" },
            new() { CId = "MW", Name = "Thyolo" },
            new() { CId = "MW", Name = "Zomba" },
            new() { CId = "MY", Name = "Johor" },
            new() { CId = "MY", Name = "Kedah" },
            new() { CId = "MY", Name = "Kelantan" },
            new() { CId = "MY", Name = "Labuan" },
            new() { CId = "MY", Name = "Melaka" },
            new() { CId = "MY", Name = "Negeri Sembilan" },
            new() { CId = "MY", Name = "Pahang" },
            new() { CId = "MY", Name = "Perak" },
            new() { CId = "MY", Name = "Perlis" },
            new() { CId = "MY", Name = "Pulau Pinang" },
            new() { CId = "MY", Name = "Sabah" },
            new() { CId = "MY", Name = "Sarawak" },
            new() { CId = "MY", Name = "Selangor" },
            new() { CId = "MY", Name = "Terengganu" },
            new() { CId = "MY", Name = "Kuala Lumpur" },
            new() { CId = "MV", Name = "Thiladhunmathi Uthuru" },
            new() { CId = "MV", Name = "Thiladhunmathi Dhekunu" },
            new() { CId = "MV", Name = "Miladhunmadulu Uthuru" },
            new() { CId = "MV", Name = "Miladhunmadulu Dhekunu" },
            new() { CId = "MV", Name = "Maalhosmadulu Uthuru" },
            new() { CId = "MV", Name = "Maalhosmadulu Dhekunu" },
            new() { CId = "MV", Name = "Faadhippolhu" },
            new() { CId = "MV", Name = "Male Atoll" },
            new() { CId = "MV", Name = "Ari Atoll Uthuru" },
            new() { CId = "MV", Name = "Ari Atoll Dheknu" },
            new() { CId = "MV", Name = "Felidhe Atoll" },
            new() { CId = "MV", Name = "Mulaku Atoll" },
            new() { CId = "MV", Name = "Nilandhe Atoll Uthuru" },
            new() { CId = "MV", Name = "Nilandhe Atoll Dhekunu" },
            new() { CId = "MV", Name = "Kolhumadulu" },
            new() { CId = "MV", Name = "Hadhdhunmathi" },
            new() { CId = "MV", Name = "Huvadhu Atoll Uthuru" },
            new() { CId = "MV", Name = "Huvadhu Atoll Dhekunu" },
            new() { CId = "MV", Name = "Fua Mulaku" },
            new() { CId = "MV", Name = "Addu" },
            new() { CId = "ML", Name = "Gao" },
            new() { CId = "ML", Name = "Kayes" },
            new() { CId = "ML", Name = "Kidal" },
            new() { CId = "ML", Name = "Koulikoro" },
            new() { CId = "ML", Name = "Mopti" },
            new() { CId = "ML", Name = "Segou" },
            new() { CId = "ML", Name = "Sikasso" },
            new() { CId = "ML", Name = "Tombouctou" },
            new() { CId = "ML", Name = "Bamako Capital District" },
            new() { CId = "MT", Name = "Attard" },
            new() { CId = "MT", Name = "Balzan" },
            new() { CId = "MT", Name = "Birgu" },
            new() { CId = "MT", Name = "Birkirkara" },
            new() { CId = "MT", Name = "Birzebbuga" },
            new() { CId = "MT", Name = "Bormla" },
            new() { CId = "MT", Name = "Dingli" },
            new() { CId = "MT", Name = "Fgura" },
            new() { CId = "MT", Name = "Floriana" },
            new() { CId = "MT", Name = "Gudja" },
            new() { CId = "MT", Name = "Gzira" },
            new() { CId = "MT", Name = "Gargur" },
            new() { CId = "MT", Name = "Gaxaq" },
            new() { CId = "MT", Name = "Hamrun" },
            new() { CId = "MT", Name = "Iklin" },
            new() { CId = "MT", Name = "Isla" },
            new() { CId = "MT", Name = "Kalkara" },
            new() { CId = "MT", Name = "Kirkop" },
            new() { CId = "MT", Name = "Lija" },
            new() { CId = "MT", Name = "Luqa" },
            new() { CId = "MT", Name = "Marsa" },
            new() { CId = "MT", Name = "Marsaskala" },
            new() { CId = "MT", Name = "Marsaxlokk" },
            new() { CId = "MT", Name = "Mdina" },
            new() { CId = "MT", Name = "Melliea" },
            new() { CId = "MT", Name = "Mgarr" },
            new() { CId = "MT", Name = "Mosta" },
            new() { CId = "MT", Name = "Mqabba" },
            new() { CId = "MT", Name = "Msida" },
            new() { CId = "MT", Name = "Mtarfa" },
            new() { CId = "MT", Name = "Naxxar" },
            new() { CId = "MT", Name = "Paola" },
            new() { CId = "MT", Name = "Pembroke" },
            new() { CId = "MT", Name = "Pieta" },
            new() { CId = "MT", Name = "Qormi" },
            new() { CId = "MT", Name = "Qrendi" },
            new() { CId = "MT", Name = "Rabat" },
            new() { CId = "MT", Name = "Safi" },
            new() { CId = "MT", Name = "San Giljan" },
            new() { CId = "MT", Name = "Santa Lucija" },
            new() { CId = "MT", Name = "San Pawl il-Bahar" },
            new() { CId = "MT", Name = "San Gwann" },
            new() { CId = "MT", Name = "Santa Venera" },
            new() { CId = "MT", Name = "Siggiewi" },
            new() { CId = "MT", Name = "Sliema" },
            new() { CId = "MT", Name = "Swieqi" },
            new() { CId = "MT", Name = "Ta Xbiex" },
            new() { CId = "MT", Name = "Tarxien" },
            new() { CId = "MT", Name = "Valletta" },
            new() { CId = "MT", Name = "Xgajra" },
            new() { CId = "MT", Name = "Zabbar" },
            new() { CId = "MT", Name = "Zebbug" },
            new() { CId = "MT", Name = "Zejtun" },
            new() { CId = "MT", Name = "Zurrieq" },
            new() { CId = "MT", Name = "Fontana" },
            new() { CId = "MT", Name = "Ghajnsielem" },
            new() { CId = "MT", Name = "Gharb" },
            new() { CId = "MT", Name = "Ghasri" },
            new() { CId = "MT", Name = "Kercem" },
            new() { CId = "MT", Name = "Munxar" },
            new() { CId = "MT", Name = "Nadur" },
            new() { CId = "MT", Name = "Qala" },
            new() { CId = "MT", Name = "Victoria" },
            new() { CId = "MT", Name = "San Lawrenz" },
            new() { CId = "MT", Name = "Sannat" },
            new() { CId = "MT", Name = "Xagra" },
            new() { CId = "MT", Name = "Xewkija" },
            new() { CId = "MH", Name = "Ailinginae" },
            new() { CId = "MH", Name = "Ailinglaplap" },
            new() { CId = "MH", Name = "Ailuk" },
            new() { CId = "MH", Name = "Arno" },
            new() { CId = "MH", Name = "Aur" },
            new() { CId = "MH", Name = "Bikar" },
            new() { CId = "MH", Name = "Bikini" },
            new() { CId = "MH", Name = "Bokak" },
            new() { CId = "MH", Name = "Ebon" },
            new() { CId = "MH", Name = "Enewetak" },
            new() { CId = "MH", Name = "Erikub" },
            new() { CId = "MH", Name = "Jabat" },
            new() { CId = "MH", Name = "Jaluit" },
            new() { CId = "MH", Name = "Jemo" },
            new() { CId = "MH", Name = "Kili" },
            new() { CId = "MH", Name = "Kwajalein" },
            new() { CId = "MH", Name = "Lae" },
            new() { CId = "MH", Name = "Lib" },
            new() { CId = "MH", Name = "Likiep" },
            new() { CId = "MH", Name = "Majuro" },
            new() { CId = "MH", Name = "Maloelap" },
            new() { CId = "MH", Name = "Mejit" },
            new() { CId = "MH", Name = "Mili" },
            new() { CId = "MH", Name = "Namorik" },
            new() { CId = "MH", Name = "Namu" },
            new() { CId = "MH", Name = "Rongelap" },
            new() { CId = "MH", Name = "Rongrik" },
            new() { CId = "MH", Name = "Toke" },
            new() { CId = "MH", Name = "Ujae" },
            new() { CId = "MH", Name = "Ujelang" },
            new() { CId = "MH", Name = "Utirik" },
            new() { CId = "MH", Name = "Wotho" },
            new() { CId = "MH", Name = "Wotje" },
            new() { CId = "MR", Name = "Adrar" },
            new() { CId = "MR", Name = "Assaba" },
            new() { CId = "MR", Name = "Brakna" },
            new() { CId = "MR", Name = "Dakhlet Nouadhibou" },
            new() { CId = "MR", Name = "Gorgol" },
            new() { CId = "MR", Name = "Guidimaka" },
            new() { CId = "MR", Name = "Hodh Ech Chargui" },
            new() { CId = "MR", Name = "Hodh El Gharbi" },
            new() { CId = "MR", Name = "Inchiri" },
            new() { CId = "MR", Name = "Tagant" },
            new() { CId = "MR", Name = "Tiris Zemmour" },
            new() { CId = "MR", Name = "Trarza" },
            new() { CId = "MR", Name = "Nouakchott" },
            new() { CId = "MU", Name = "Beau Bassin-Rose Hill" },
            new() { CId = "MU", Name = "Curepipe" },
            new() { CId = "MU", Name = "Port Louis" },
            new() { CId = "MU", Name = "Quatre Bornes" },
            new() { CId = "MU", Name = "Vacoas-Phoenix" },
            new() { CId = "MU", Name = "Agalega Islands" },
            new() { CId = "MU", Name = "Cargados Carajos Shoals (Saint Brandon Islands)" },
            new() { CId = "MU", Name = "Rodrigues" },
            new() { CId = "MU", Name = "Black River" },
            new() { CId = "MU", Name = "Flacq" },
            new() { CId = "MU", Name = "Grand Port" },
            new() { CId = "MU", Name = "Moka" },
            new() { CId = "MU", Name = "Pamplemousses" },
            new() { CId = "MU", Name = "Plaines Wilhems" },
            new() { CId = "MU", Name = "Riviere du Rempart" },
            new() { CId = "MU", Name = "Savanne" },
            new() { CId = "MX", Name = "Baja California Norte" },
            new() { CId = "MX", Name = "Baja California Sur" },
            new() { CId = "MX", Name = "Campeche" },
            new() { CId = "MX", Name = "Chiapas" },
            new() { CId = "MX", Name = "Chihuahua" },
            new() { CId = "MX", Name = "Coahuila de Zaragoza" },
            new() { CId = "MX", Name = "Colima" },
            new() { CId = "MX", Name = "Distrito Federal" },
            new() { CId = "MX", Name = "Durango" },
            new() { CId = "MX", Name = "Guanajuato" },
            new() { CId = "MX", Name = "Guerrero" },
            new() { CId = "MX", Name = "Hidalgo" },
            new() { CId = "MX", Name = "Jalisco" },
            new() { CId = "MX", Name = "Mexico" },
            new() { CId = "MX", Name = "Michoacan de Ocampo" },
            new() { CId = "MX", Name = "Morelos" },
            new() { CId = "MX", Name = "Nayarit" },
            new() { CId = "MX", Name = "Nuevo Leon" },
            new() { CId = "MX", Name = "Oaxaca" },
            new() { CId = "MX", Name = "Puebla" },
            new() { CId = "MX", Name = "Queretaro de Arteaga" },
            new() { CId = "MX", Name = "Quintana Roo" },
            new() { CId = "MX", Name = "San Luis Potosi" },
            new() { CId = "MX", Name = "Sinaloa" },
            new() { CId = "MX", Name = "Sonora" },
            new() { CId = "MX", Name = "Tabasco" },
            new() { CId = "MX", Name = "Tamaulipas" },
            new() { CId = "MX", Name = "Tlaxcala" },
            new() { CId = "MX", Name = "Veracruz-Llave" },
            new() { CId = "MX", Name = "Yucatan" },
            new() { CId = "MX", Name = "Zacatecas" },
            new() { CId = "FM", Name = "Chuuk" },
            new() { CId = "FM", Name = "Kosrae" },
            new() { CId = "FM", Name = "Pohnpei" },
            new() { CId = "FM", Name = "Yap" },
            new() { CId = "MD", Name = "Gagauzia" },
            new() { CId = "MD", Name = "Chisinau" },
            new() { CId = "MD", Name = "Balti" },
            new() { CId = "MD", Name = "Cahul" },
            new() { CId = "MD", Name = "Edinet" },
            new() { CId = "MD", Name = "Lapusna" },
            new() { CId = "MD", Name = "Orhei" },
            new() { CId = "MD", Name = "Soroca" },
            new() { CId = "MD", Name = "Tighina" },
            new() { CId = "MD", Name = "Ungheni" },
            new() { CId = "MD", Name = "St‚nga Nistrului" },
            new() { CId = "MC", Name = "Fontvieille" },
            new() { CId = "MC", Name = "La Condamine" },
            new() { CId = "MC", Name = "Monaco-Ville" },
            new() { CId = "MC", Name = "Monte-Carlo" },
            new() { CId = "MN", Name = "Ulanbaatar" },
            new() { CId = "MN", Name = "Orhon" },
            new() { CId = "MN", Name = "Darhan uul" },
            new() { CId = "MN", Name = "Hentiy" },
            new() { CId = "MN", Name = "Hovsgol" },
            new() { CId = "MN", Name = "Hovd" },
            new() { CId = "MN", Name = "Uvs" },
            new() { CId = "MN", Name = "Tov" },
            new() { CId = "MN", Name = "Selenge" },
            new() { CId = "MN", Name = "Suhbaatar" },
            new() { CId = "MN", Name = "Omnogovi" },
            new() { CId = "MN", Name = "Ovorhangay" },
            new() { CId = "MN", Name = "Dzavhan" },
            new() { CId = "MN", Name = "DundgovL" },
            new() { CId = "MN", Name = "Dornod" },
            new() { CId = "MN", Name = "Dornogov" },
            new() { CId = "MN", Name = "Govi-Sumber" },
            new() { CId = "MN", Name = "Govi-Altay" },
            new() { CId = "MN", Name = "Bulgan" },
            new() { CId = "MN", Name = "Bayanhongor" },
            new() { CId = "MN", Name = "Bayan-Olgiy" },
            new() { CId = "MN", Name = "Arhangay" },
            new() { CId = "MS", Name = "Saint Anthony" },
            new() { CId = "MS", Name = "Saint Georges" },
            new() { CId = "MS", Name = "Saint Peter" },
            new() { CId = "MA", Name = "Agadir" },
            new() { CId = "MA", Name = "Al Hoceima" },
            new() { CId = "MA", Name = "Azilal" },
            new() { CId = "MA", Name = "Beni Mellal" },
            new() { CId = "MA", Name = "Ben Slimane" },
            new() { CId = "MA", Name = "Boulemane" },
            new() { CId = "MA", Name = "Casablanca" },
            new() { CId = "MA", Name = "Chaouen" },
            new() { CId = "MA", Name = "El Jadida" },
            new() { CId = "MA", Name = "El Kelaa des Sraghna" },
            new() { CId = "MA", Name = "Er Rachidia" },
            new() { CId = "MA", Name = "Essaouira" },
            new() { CId = "MA", Name = "Fes" },
            new() { CId = "MA", Name = "Figuig" },
            new() { CId = "MA", Name = "Guelmim" },
            new() { CId = "MA", Name = "Ifrane" },
            new() { CId = "MA", Name = "Kenitra" },
            new() { CId = "MA", Name = "Khemisset" },
            new() { CId = "MA", Name = "Khenifra" },
            new() { CId = "MA", Name = "Khouribga" },
            new() { CId = "MA", Name = "Laayoune" },
            new() { CId = "MA", Name = "Larache" },
            new() { CId = "MA", Name = "Marrakech" },
            new() { CId = "MA", Name = "Meknes" },
            new() { CId = "MA", Name = "Nador" },
            new() { CId = "MA", Name = "Ouarzazate" },
            new() { CId = "MA", Name = "Oujda" },
            new() { CId = "MA", Name = "Rabat-Sale" },
            new() { CId = "MA", Name = "Safi" },
            new() { CId = "MA", Name = "Settat" },
            new() { CId = "MA", Name = "Sidi Kacem" },
            new() { CId = "MA", Name = "Tangier" },
            new() { CId = "MA", Name = "Tan-Tan" },
            new() { CId = "MA", Name = "Taounate" },
            new() { CId = "MA", Name = "Taroudannt" },
            new() { CId = "MA", Name = "Tata" },
            new() { CId = "MA", Name = "Taza" },
            new() { CId = "MA", Name = "Tetouan" },
            new() { CId = "MA", Name = "Tiznit" },
            new() { CId = "MA", Name = "Ad Dakhla" },
            new() { CId = "MA", Name = "Boujdour" },
            new() { CId = "MA", Name = "Es Smara" },
            new() { CId = "MZ", Name = "Cabo Delgado" },
            new() { CId = "MZ", Name = "Gaza" },
            new() { CId = "MZ", Name = "Inhambane" },
            new() { CId = "MZ", Name = "Manica" },
            new() { CId = "MZ", Name = "Maputo (city)" },
            new() { CId = "MZ", Name = "Maputo" },
            new() { CId = "MZ", Name = "Nampula" },
            new() { CId = "MZ", Name = "Niassa" },
            new() { CId = "MZ", Name = "Sofala" },
            new() { CId = "MZ", Name = "Tete" },
            new() { CId = "MZ", Name = "Zambezia" },
            new() { CId = "MM", Name = "Ayeyarwady" },
            new() { CId = "MM", Name = "Bago" },
            new() { CId = "MM", Name = "Magway" },
            new() { CId = "MM", Name = "Mandalay" },
            new() { CId = "MM", Name = "Sagaing" },
            new() { CId = "MM", Name = "Tanintharyi" },
            new() { CId = "MM", Name = "Yangon" },
            new() { CId = "MM", Name = "Chin State" },
            new() { CId = "MM", Name = "Kachin State" },
            new() { CId = "MM", Name = "Kayah State" },
            new() { CId = "MM", Name = "Kayin State" },
            new() { CId = "MM", Name = "Mon State" },
            new() { CId = "MM", Name = "Rakhine State" },
            new() { CId = "MM", Name = "Shan State" },
            new() { CId = "NA", Name = "Caprivi" },
            new() { CId = "NA", Name = "Erongo" },
            new() { CId = "NA", Name = "Hardap" },
            new() { CId = "NA", Name = "Karas" },
            new() { CId = "NA", Name = "Kavango" },
            new() { CId = "NA", Name = "Khomas" },
            new() { CId = "NA", Name = "Kunene" },
            new() { CId = "NA", Name = "Ohangwena" },
            new() { CId = "NA", Name = "Omaheke" },
            new() { CId = "NA", Name = "Omusati" },
            new() { CId = "NA", Name = "Oshana" },
            new() { CId = "NA", Name = "Oshikoto" },
            new() { CId = "NA", Name = "Otjozondjupa" },
            new() { CId = "NR", Name = "Aiwo" },
            new() { CId = "NR", Name = "Anabar" },
            new() { CId = "NR", Name = "Anetan" },
            new() { CId = "NR", Name = "Anibare" },
            new() { CId = "NR", Name = "Baiti" },
            new() { CId = "NR", Name = "Boe" },
            new() { CId = "NR", Name = "Buada" },
            new() { CId = "NR", Name = "Denigomodu" },
            new() { CId = "NR", Name = "Ewa" },
            new() { CId = "NR", Name = "Ijuw" },
            new() { CId = "NR", Name = "Meneng" },
            new() { CId = "NR", Name = "Nibok" },
            new() { CId = "NR", Name = "Uaboe" },
            new() { CId = "NR", Name = "Yaren" },
            new() { CId = "NP", Name = "Bagmati" },
            new() { CId = "NP", Name = "Bheri" },
            new() { CId = "NP", Name = "Dhawalagiri" },
            new() { CId = "NP", Name = "Gandaki" },
            new() { CId = "NP", Name = "Janakpur" },
            new() { CId = "NP", Name = "Karnali" },
            new() { CId = "NP", Name = "Kosi" },
            new() { CId = "NP", Name = "Lumbini" },
            new() { CId = "NP", Name = "Mahakali" },
            new() { CId = "NP", Name = "Mechi" },
            new() { CId = "NP", Name = "Narayani" },
            new() { CId = "NP", Name = "Rapti" },
            new() { CId = "NP", Name = "Sagarmatha" },
            new() { CId = "NP", Name = "Seti" },
            new() { CId = "NL", Name = "Drenthe" },
            new() { CId = "NL", Name = "Flevoland" },
            new() { CId = "NL", Name = "Friesland" },
            new() { CId = "NL", Name = "Gelderland" },
            new() { CId = "NL", Name = "Groningen" },
            new() { CId = "NL", Name = "Limburg" },
            new() { CId = "NL", Name = "Noord-Brabant" },
            new() { CId = "NL", Name = "Noord-Holland" },
            new() { CId = "NL", Name = "Overijssel" },
            new() { CId = "NL", Name = "Utrecht" },
            new() { CId = "NL", Name = "Zeeland" },
            new() { CId = "NL", Name = "Zuid-Holland" },
            new() { CId = "NC", Name = "Iles Loyaute" },
            new() { CId = "NC", Name = "Nord" },
            new() { CId = "NC", Name = "Sud" },
            new() { CId = "NZ", Name = "Auckland" },
            new() { CId = "NZ", Name = "Bay of Plenty" },
            new() { CId = "NZ", Name = "Canterbury" },
            new() { CId = "NZ", Name = "Coromandel" },
            new() { CId = "NZ", Name = "Gisborne" },
            new() { CId = "NZ", Name = "Fiordland" },
            new() { CId = "NZ", Name = "Hawke's Bay" },
            new() { CId = "NZ", Name = "Marlborough" },
            new() { CId = "NZ", Name = "Manawatu-Wanganui" },
            new() { CId = "NZ", Name = "Mt Cook-Mackenzie" },
            new() { CId = "NZ", Name = "Nelson" },
            new() { CId = "NZ", Name = "Northland" },
            new() { CId = "NZ", Name = "Otago" },
            new() { CId = "NZ", Name = "Southland" },
            new() { CId = "NZ", Name = "Taranaki" },
            new() { CId = "NZ", Name = "Wellington" },
            new() { CId = "NZ", Name = "Waikato" },
            new() { CId = "NZ", Name = "Wairarapa" },
            new() { CId = "NZ", Name = "West Coast" },
            new() { CId = "NI", Name = "Atlantico Norte" },
            new() { CId = "NI", Name = "Atlantico Sur" },
            new() { CId = "NI", Name = "Boaco" },
            new() { CId = "NI", Name = "Carazo" },
            new() { CId = "NI", Name = "Chinandega" },
            new() { CId = "NI", Name = "Chontales" },
            new() { CId = "NI", Name = "Esteli" },
            new() { CId = "NI", Name = "Granada" },
            new() { CId = "NI", Name = "Jinotega" },
            new() { CId = "NI", Name = "Leon" },
            new() { CId = "NI", Name = "Madriz" },
            new() { CId = "NI", Name = "Managua" },
            new() { CId = "NI", Name = "Masaya" },
            new() { CId = "NI", Name = "Matagalpa" },
            new() { CId = "NI", Name = "Nuevo Segovia" },
            new() { CId = "NI", Name = "Rio San Juan" },
            new() { CId = "NI", Name = "Rivas" },
            new() { CId = "NE", Name = "Agadez" },
            new() { CId = "NE", Name = "Diffa" },
            new() { CId = "NE", Name = "Dosso" },
            new() { CId = "NE", Name = "Maradi" },
            new() { CId = "NE", Name = "Niamey" },
            new() { CId = "NE", Name = "Tahoua" },
            new() { CId = "NE", Name = "Tillaberi" },
            new() { CId = "NE", Name = "Zinder" },
            new() { CId = "NG", Name = "Abia" },
            new() { CId = "NG", Name = "Abuja Federal Capital Territory" },
            new() { CId = "NG", Name = "Adamawa" },
            new() { CId = "NG", Name = "Akwa Ibom" },
            new() { CId = "NG", Name = "Anambra" },
            new() { CId = "NG", Name = "Bauchi" },
            new() { CId = "NG", Name = "Bayelsa" },
            new() { CId = "NG", Name = "Benue" },
            new() { CId = "NG", Name = "Borno" },
            new() { CId = "NG", Name = "Cross River" },
            new() { CId = "NG", Name = "Delta" },
            new() { CId = "NG", Name = "Ebonyi" },
            new() { CId = "NG", Name = "Edo" },
            new() { CId = "NG", Name = "Ekiti" },
            new() { CId = "NG", Name = "Enugu" },
            new() { CId = "NG", Name = "Gombe" },
            new() { CId = "NG", Name = "Imo" },
            new() { CId = "NG", Name = "Jigawa" },
            new() { CId = "NG", Name = "Kaduna" },
            new() { CId = "NG", Name = "Kano" },
            new() { CId = "NG", Name = "Katsina" },
            new() { CId = "NG", Name = "Kebbi" },
            new() { CId = "NG", Name = "Kogi" },
            new() { CId = "NG", Name = "Kwara" },
            new() { CId = "NG", Name = "Lagos" },
            new() { CId = "NG", Name = "Nassarawa" },
            new() { CId = "NG", Name = "Niger" },
            new() { CId = "NG", Name = "Ogun" },
            new() { CId = "NG", Name = "Ondo" },
            new() { CId = "NG", Name = "Osun" },
            new() { CId = "NG", Name = "Oyo" },
            new() { CId = "NG", Name = "Plateau" },
            new() { CId = "NG", Name = "Rivers" },
            new() { CId = "NG", Name = "Sokoto" },
            new() { CId = "NG", Name = "Taraba" },
            new() { CId = "NG", Name = "Yobe" },
            new() { CId = "NG", Name = "Zamfara" },
            new() { CId = "MP", Name = "Northern Islands" },
            new() { CId = "MP", Name = "Rota" },
            new() { CId = "MP", Name = "Saipan" },
            new() { CId = "MP", Name = "Tinian" },
            new() { CId = "NO", Name = "Akershus" },
            new() { CId = "NO", Name = "Aust-Agder" },
            new() { CId = "NO", Name = "Buskerud" },
            new() { CId = "NO", Name = "Finnmark" },
            new() { CId = "NO", Name = "Hedmark" },
            new() { CId = "NO", Name = "Hordaland" },
            new() { CId = "NO", Name = "More og Romdal" },
            new() { CId = "NO", Name = "Nord-Trondelag" },
            new() { CId = "NO", Name = "Nordland" },
            new() { CId = "NO", Name = "Ostfold" },
            new() { CId = "NO", Name = "Oppland" },
            new() { CId = "NO", Name = "Oslo" },
            new() { CId = "NO", Name = "Rogaland" },
            new() { CId = "NO", Name = "Sor-Trondelag" },
            new() { CId = "NO", Name = "Sogn og Fjordane" },
            new() { CId = "NO", Name = "Svalbard" },
            new() { CId = "NO", Name = "Telemark" },
            new() { CId = "NO", Name = "Troms" },
            new() { CId = "NO", Name = "Vest-Agder" },
            new() { CId = "NO", Name = "Vestfold" },
            new() { CId = "OM", Name = "Ad Dakhiliyah" },
            new() { CId = "OM", Name = "Al Batinah" },
            new() { CId = "OM", Name = "Al Wusta" },
            new() { CId = "OM", Name = "Ash Sharqiyah" },
            new() { CId = "OM", Name = "Az Zahirah" },
            new() { CId = "OM", Name = "Masqat" },
            new() { CId = "OM", Name = "Musandam" },
            new() { CId = "OM", Name = "Zufar" },
            new() { CId = "PK", Name = "Balochistan" },
            new() { CId = "PK", Name = "Federally Administered Tribal Areas" },
            new() { CId = "PK", Name = "Islamabad Capital Territory" },
            new() { CId = "PK", Name = "North-West Frontier" },
            new() { CId = "PK", Name = "Punjab" },
            new() { CId = "PK", Name = "Sindh" },
            new() { CId = "PW", Name = "Aimeliik" },
            new() { CId = "PW", Name = "Airai" },
            new() { CId = "PW", Name = "Angaur" },
            new() { CId = "PW", Name = "Hatohobei" },
            new() { CId = "PW", Name = "Kayangel" },
            new() { CId = "PW", Name = "Koror" },
            new() { CId = "PW", Name = "Melekeok" },
            new() { CId = "PW", Name = "Ngaraard" },
            new() { CId = "PW", Name = "Ngarchelong" },
            new() { CId = "PW", Name = "Ngardmau" },
            new() { CId = "PW", Name = "Ngatpang" },
            new() { CId = "PW", Name = "Ngchesar" },
            new() { CId = "PW", Name = "Ngeremlengui" },
            new() { CId = "PW", Name = "Ngiwal" },
            new() { CId = "PW", Name = "Peleliu" },
            new() { CId = "PW", Name = "Sonsorol" },
            new() { CId = "PA", Name = "Bocas del Toro" },
            new() { CId = "PA", Name = "Chiriqui" },
            new() { CId = "PA", Name = "Cocle" },
            new() { CId = "PA", Name = "Colon" },
            new() { CId = "PA", Name = "Darien" },
            new() { CId = "PA", Name = "Herrera" },
            new() { CId = "PA", Name = "Los Santos" },
            new() { CId = "PA", Name = "Panama" },
            new() { CId = "PA", Name = "San Blas" },
            new() { CId = "PA", Name = "Veraguas" },
            new() { CId = "PG", Name = "Bougainville" },
            new() { CId = "PG", Name = "Central" },
            new() { CId = "PG", Name = "Chimbu" },
            new() { CId = "PG", Name = "Eastern Highlands" },
            new() { CId = "PG", Name = "East New Britain" },
            new() { CId = "PG", Name = "East Sepik" },
            new() { CId = "PG", Name = "Enga" },
            new() { CId = "PG", Name = "Gulf" },
            new() { CId = "PG", Name = "Madang" },
            new() { CId = "PG", Name = "Manus" },
            new() { CId = "PG", Name = "Milne Bay" },
            new() { CId = "PG", Name = "Morobe" },
            new() { CId = "PG", Name = "National Capital" },
            new() { CId = "PG", Name = "New Ireland" },
            new() { CId = "PG", Name = "Northern" },
            new() { CId = "PG", Name = "Sandaun" },
            new() { CId = "PG", Name = "Southern Highlands" },
            new() { CId = "PG", Name = "Western" },
            new() { CId = "PG", Name = "Western Highlands" },
            new() { CId = "PG", Name = "West New Britain" },
            new() { CId = "PY", Name = "Alto Paraguay" },
            new() { CId = "PY", Name = "Alto Parana" },
            new() { CId = "PY", Name = "Amambay" },
            new() { CId = "PY", Name = "Asuncion" },
            new() { CId = "PY", Name = "Boqueron" },
            new() { CId = "PY", Name = "Caaguazu" },
            new() { CId = "PY", Name = "Caazapa" },
            new() { CId = "PY", Name = "Canindeyu" },
            new() { CId = "PY", Name = "Central" },
            new() { CId = "PY", Name = "Concepcion" },
            new() { CId = "PY", Name = "Cordillera" },
            new() { CId = "PY", Name = "Guaira" },
            new() { CId = "PY", Name = "Itapua" },
            new() { CId = "PY", Name = "Misiones" },
            new() { CId = "PY", Name = "Neembucu" },
            new() { CId = "PY", Name = "Paraguari" },
            new() { CId = "PY", Name = "Presidente Hayes" },
            new() { CId = "PY", Name = "San Pedro" },
            new() { CId = "PE", Name = "Amazonas" },
            new() { CId = "PE", Name = "Ancash" },
            new() { CId = "PE", Name = "Apurimac" },
            new() { CId = "PE", Name = "Arequipa" },
            new() { CId = "PE", Name = "Ayacucho" },
            new() { CId = "PE", Name = "Cajamarca" },
            new() { CId = "PE", Name = "Callao" },
            new() { CId = "PE", Name = "Cusco" },
            new() { CId = "PE", Name = "Huancavelica" },
            new() { CId = "PE", Name = "Huanuco" },
            new() { CId = "PE", Name = "Ica" },
            new() { CId = "PE", Name = "Junin" },
            new() { CId = "PE", Name = "La Libertad" },
            new() { CId = "PE", Name = "Lambayeque" },
            new() { CId = "PE", Name = "Lima" },
            new() { CId = "PE", Name = "Loreto" },
            new() { CId = "PE", Name = "Madre de Dios" },
            new() { CId = "PE", Name = "Moquegua" },
            new() { CId = "PE", Name = "Pasco" },
            new() { CId = "PE", Name = "Piura" },
            new() { CId = "PE", Name = "Puno" },
            new() { CId = "PE", Name = "San Martin" },
            new() { CId = "PE", Name = "Tacna" },
            new() { CId = "PE", Name = "Tumbes" },
            new() { CId = "PE", Name = "Ucayali" },
            new() { CId = "PH", Name = "Abra" },
            new() { CId = "PH", Name = "Agusan del Norte" },
            new() { CId = "PH", Name = "Agusan del Sur" },
            new() { CId = "PH", Name = "Aklan" },
            new() { CId = "PH", Name = "Albay" },
            new() { CId = "PH", Name = "Antique" },
            new() { CId = "PH", Name = "Apayao" },
            new() { CId = "PH", Name = "Aurora" },
            new() { CId = "PH", Name = "Basilan" },
            new() { CId = "PH", Name = "Bataan" },
            new() { CId = "PH", Name = "Batanes" },
            new() { CId = "PH", Name = "Batangas" },
            new() { CId = "PH", Name = "Biliran" },
            new() { CId = "PH", Name = "Benguet" },
            new() { CId = "PH", Name = "Bohol" },
            new() { CId = "PH", Name = "Bukidnon" },
            new() { CId = "PH", Name = "Bulacan" },
            new() { CId = "PH", Name = "Cagayan" },
            new() { CId = "PH", Name = "Camarines Norte" },
            new() { CId = "PH", Name = "Camarines Sur" },
            new() { CId = "PH", Name = "Camiguin" },
            new() { CId = "PH", Name = "Capiz" },
            new() { CId = "PH", Name = "Catanduanes" },
            new() { CId = "PH", Name = "Cavite" },
            new() { CId = "PH", Name = "Cebu" },
            new() { CId = "PH", Name = "Compostela" },
            new() { CId = "PH", Name = "Davao del Norte" },
            new() { CId = "PH", Name = "Davao del Sur" },
            new() { CId = "PH", Name = "Davao Oriental" },
            new() { CId = "PH", Name = "Eastern Samar" },
            new() { CId = "PH", Name = "Guimaras" },
            new() { CId = "PH", Name = "Ifugao" },
            new() { CId = "PH", Name = "Ilocos Norte" },
            new() { CId = "PH", Name = "Ilocos Sur" },
            new() { CId = "PH", Name = "Iloilo" },
            new() { CId = "PH", Name = "Isabela" },
            new() { CId = "PH", Name = "Kalinga" },
            new() { CId = "PH", Name = "Laguna" },
            new() { CId = "PH", Name = "Lanao del Norte" },
            new() { CId = "PH", Name = "Lanao del Sur" },
            new() { CId = "PH", Name = "La Union" },
            new() { CId = "PH", Name = "Leyte" },
            new() { CId = "PH", Name = "Maguindanao" },
            new() { CId = "PH", Name = "Marinduque" },
            new() { CId = "PH", Name = "Masbate" },
            new() { CId = "PH", Name = "Mindoro Occidental" },
            new() { CId = "PH", Name = "Mindoro Oriental" },
            new() { CId = "PH", Name = "Misamis Occidental" },
            new() { CId = "PH", Name = "Misamis Oriental" },
            new() { CId = "PH", Name = "Mountain" },
            new() { CId = "PH", Name = "Negros Occidental" },
            new() { CId = "PH", Name = "Negros Oriental" },
            new() { CId = "PH", Name = "North Cotabato" },
            new() { CId = "PH", Name = "Northern Samar" },
            new() { CId = "PH", Name = "Nueva Ecija" },
            new() { CId = "PH", Name = "Nueva Vizcaya" },
            new() { CId = "PH", Name = "Palawan" },
            new() { CId = "PH", Name = "Pampanga" },
            new() { CId = "PH", Name = "Pangasinan" },
            new() { CId = "PH", Name = "Quezon" },
            new() { CId = "PH", Name = "Quirino" },
            new() { CId = "PH", Name = "Rizal" },
            new() { CId = "PH", Name = "Romblon" },
            new() { CId = "PH", Name = "Samar" },
            new() { CId = "PH", Name = "Sarangani" },
            new() { CId = "PH", Name = "Siquijor" },
            new() { CId = "PH", Name = "Sorsogon" },
            new() { CId = "PH", Name = "South Cotabato" },
            new() { CId = "PH", Name = "Southern Leyte" },
            new() { CId = "PH", Name = "Sultan Kudarat" },
            new() { CId = "PH", Name = "Sulu" },
            new() { CId = "PH", Name = "Surigao del Norte" },
            new() { CId = "PH", Name = "Surigao del Sur" },
            new() { CId = "PH", Name = "Tarlac" },
            new() { CId = "PH", Name = "Tawi-Tawi" },
            new() { CId = "PH", Name = "Zambales" },
            new() { CId = "PH", Name = "Zamboanga del Norte" },
            new() { CId = "PH", Name = "Zamboanga del Sur" },
            new() { CId = "PH", Name = "Zamboanga Sibugay" },
            new() { CId = "PL", Name = "Dolnoslaskie" },
            new() { CId = "PL", Name = "Kujawsko-Pomorskie" },
            new() { CId = "PL", Name = "Lodzkie" },
            new() { CId = "PL", Name = "Lubelskie" },
            new() { CId = "PL", Name = "Lubuskie" },
            new() { CId = "PL", Name = "Malopolskie" },
            new() { CId = "PL", Name = "Mazowieckie" },
            new() { CId = "PL", Name = "Opolskie" },
            new() { CId = "PL", Name = "Podkarpackie" },
            new() { CId = "PL", Name = "Podlaskie" },
            new() { CId = "PL", Name = "Pomorskie" },
            new() { CId = "PL", Name = "Slaskie" },
            new() { CId = "PL", Name = "Swietokrzyskie" },
            new() { CId = "PL", Name = "Warminsko-Mazurskie" },
            new() { CId = "PL", Name = "Wielkopolskie" },
            new() { CId = "PL", Name = "Zachodniopomorskie" },
            new() { CId = "PM", Name = "Saint Pierre" },
            new() { CId = "PM", Name = "Miquelon" },
            new() { CId = "PT", Name = "A&ccedil;ores" },
            new() { CId = "PT", Name = "Aveiro" },
            new() { CId = "PT", Name = "Beja" },
            new() { CId = "PT", Name = "Braga" },
            new() { CId = "PT", Name = "Bragan&ccedil;a" },
            new() { CId = "PT", Name = "Castelo Branco" },
            new() { CId = "PT", Name = "Coimbra" },
            new() { CId = "PT", Name = "&Eacute;vora" },
            new() { CId = "PT", Name = "Faro" },
            new() { CId = "PT", Name = "Guarda" },
            new() { CId = "PT", Name = "Leiria" },
            new() { CId = "PT", Name = "Lisboa" },
            new() { CId = "PT", Name = "Madeira" },
            new() { CId = "PT", Name = "Portalegre" },
            new() { CId = "PT", Name = "Porto" },
            new() { CId = "PT", Name = "Santar&eacute;m" },
            new() { CId = "PT", Name = "Set&uacute;bal" },
            new() { CId = "PT", Name = "Viana do Castelo" },
            new() { CId = "PT", Name = "Vila Real" },
            new() { CId = "PT", Name = "Viseu" },
            new() { CId = "QA", Name = "Ad Dawhah" },
            new() { CId = "QA", Name = "Al Ghuwayriyah" },
            new() { CId = "QA", Name = "Al Jumayliyah" },
            new() { CId = "QA", Name = "Al Khawr" },
            new() { CId = "QA", Name = "Al Wakrah" },
            new() { CId = "QA", Name = "Ar Rayyan" },
            new() { CId = "QA", Name = "Jarayan al Batinah" },
            new() { CId = "QA", Name = "Madinat ash Shamal" },
            new() { CId = "QA", Name = "Umm Sa'id" },
            new() { CId = "QA", Name = "Umm Salal" },
            new() { CId = "RO", Name = "Alba" },
            new() { CId = "RO", Name = "Arad" },
            new() { CId = "RO", Name = "Arges" },
            new() { CId = "RO", Name = "Bacau" },
            new() { CId = "RO", Name = "Bihor" },
            new() { CId = "RO", Name = "Bistrita-Nasaud" },
            new() { CId = "RO", Name = "Botosani" },
            new() { CId = "RO", Name = "Brasov" },
            new() { CId = "RO", Name = "Braila" },
            new() { CId = "RO", Name = "Bucuresti" },
            new() { CId = "RO", Name = "Buzau" },
            new() { CId = "RO", Name = "Caras-Severin" },
            new() { CId = "RO", Name = "Calarasi" },
            new() { CId = "RO", Name = "Cluj" },
            new() { CId = "RO", Name = "Constanta" },
            new() { CId = "RO", Name = "Covasna" },
            new() { CId = "RO", Name = "Dimbovita" },
            new() { CId = "RO", Name = "Dolj" },
            new() { CId = "RO", Name = "Galati" },
            new() { CId = "RO", Name = "Giurgiu" },
            new() { CId = "RO", Name = "Gorj" },
            new() { CId = "RO", Name = "Harghita" },
            new() { CId = "RO", Name = "Hunedoara" },
            new() { CId = "RO", Name = "Ialomita" },
            new() { CId = "RO", Name = "Iasi" },
            new() { CId = "RO", Name = "Ilfov" },
            new() { CId = "RO", Name = "Maramures" },
            new() { CId = "RO", Name = "Mehedinti" },
            new() { CId = "RO", Name = "Mures" },
            new() { CId = "RO", Name = "Neamt" },
            new() { CId = "RO", Name = "Olt" },
            new() { CId = "RO", Name = "Prahova" },
            new() { CId = "RO", Name = "Satu-Mare" },
            new() { CId = "RO", Name = "Salaj" },
            new() { CId = "RO", Name = "Sibiu" },
            new() { CId = "RO", Name = "Suceava" },
            new() { CId = "RO", Name = "Teleorman" },
            new() { CId = "RO", Name = "Timis" },
            new() { CId = "RO", Name = "Tulcea" },
            new() { CId = "RO", Name = "Vaslui" },
            new() { CId = "RO", Name = "Valcea" },
            new() { CId = "RO", Name = "Vrancea" },
            new() { CId = "RU", Name = "Abakan" },
            new() { CId = "RU", Name = "Aginskoye" },
            new() { CId = "RU", Name = "Anadyr" },
            new() { CId = "RU", Name = "Arkahangelsk" },
            new() { CId = "RU", Name = "Astrakhan" },
            new() { CId = "RU", Name = "Barnaul" },
            new() { CId = "RU", Name = "Belgorod" },
            new() { CId = "RU", Name = "Birobidzhan" },
            new() { CId = "RU", Name = "Blagoveshchensk" },
            new() { CId = "RU", Name = "Bryansk" },
            new() { CId = "RU", Name = "Cheboksary" },
            new() { CId = "RU", Name = "Chelyabinsk" },
            new() { CId = "RU", Name = "Cherkessk" },
            new() { CId = "RU", Name = "Chita" },
            new() { CId = "RU", Name = "Dudinka" },
            new() { CId = "RU", Name = "Elista" },
            new() { CId = "RU", Name = "Gorno-Altaysk" },
            new() { CId = "RU", Name = "Groznyy" },
            new() { CId = "RU", Name = "Irkutsk" },
            new() { CId = "RU", Name = "Ivanovo" },
            new() { CId = "RU", Name = "Izhevsk" },
            new() { CId = "RU", Name = "Kalinigrad" },
            new() { CId = "RU", Name = "Kaluga" },
            new() { CId = "RU", Name = "Kasnodar" },
            new() { CId = "RU", Name = "Kazan" },
            new() { CId = "RU", Name = "Kemerovo" },
            new() { CId = "RU", Name = "Khabarovsk" },
            new() { CId = "RU", Name = "Khanty-Mansiysk" },
            new() { CId = "RU", Name = "Kostroma" },
            new() { CId = "RU", Name = "Krasnodar" },
            new() { CId = "RU", Name = "Krasnoyarsk" },
            new() { CId = "RU", Name = "Kudymkar" },
            new() { CId = "RU", Name = "Kurgan" },
            new() { CId = "RU", Name = "Kursk" },
            new() { CId = "RU", Name = "Kyzyl" },
            new() { CId = "RU", Name = "Lipetsk" },
            new() { CId = "RU", Name = "Magadan" },
            new() { CId = "RU", Name = "Makhachkala" },
            new() { CId = "RU", Name = "Maykop" },
            new() { CId = "RU", Name = "Moscow" },
            new() { CId = "RU", Name = "Murmansk" },
            new() { CId = "RU", Name = "Nalchik" },
            new() { CId = "RU", Name = "Naryan Mar" },
            new() { CId = "RU", Name = "Nazran" },
            new() { CId = "RU", Name = "Nizhniy Novgorod" },
            new() { CId = "RU", Name = "Novgorod" },
            new() { CId = "RU", Name = "Novosibirsk" },
            new() { CId = "RU", Name = "Omsk" },
            new() { CId = "RU", Name = "Orel" },
            new() { CId = "RU", Name = "Orenburg" },
            new() { CId = "RU", Name = "Palana" },
            new() { CId = "RU", Name = "Penza" },
            new() { CId = "RU", Name = "Perm" },
            new() { CId = "RU", Name = "Petropavlovsk-Kamchatskiy" },
            new() { CId = "RU", Name = "Petrozavodsk" },
            new() { CId = "RU", Name = "Pskov" },
            new() { CId = "RU", Name = "Rostov-na-Donu" },
            new() { CId = "RU", Name = "Ryazan" },
            new() { CId = "RU", Name = "Salekhard" },
            new() { CId = "RU", Name = "Samara" },
            new() { CId = "RU", Name = "Saransk" },
            new() { CId = "RU", Name = "Saratov" },
            new() { CId = "RU", Name = "Smolensk" },
            new() { CId = "RU", Name = "St. Petersburg" },
            new() { CId = "RU", Name = "Stavropol" },
            new() { CId = "RU", Name = "Syktyvkar" },
            new() { CId = "RU", Name = "Tambov" },
            new() { CId = "RU", Name = "Tomsk" },
            new() { CId = "RU", Name = "Tula" },
            new() { CId = "RU", Name = "Tura" },
            new() { CId = "RU", Name = "Tver" },
            new() { CId = "RU", Name = "Tyumen" },
            new() { CId = "RU", Name = "Ufa" },
            new() { CId = "RU", Name = "Ul'yanovsk" },
            new() { CId = "RU", Name = "Ulan-Ude" },
            new() { CId = "RU", Name = "Ust'-Ordynskiy" },
            new() { CId = "RU", Name = "Vladikavkaz" },
            new() { CId = "RU", Name = "Vladimir" },
            new() { CId = "RU", Name = "Vladivostok" },
            new() { CId = "RU", Name = "Volgograd" },
            new() { CId = "RU", Name = "Vologda" },
            new() { CId = "RU", Name = "Voronezh" },
            new() { CId = "RU", Name = "Vyatka" },
            new() { CId = "RU", Name = "Yakutsk" },
            new() { CId = "RU", Name = "Yaroslavl" },
            new() { CId = "RU", Name = "Yekaterinburg" },
            new() { CId = "RU", Name = "Yoshkar-Ola" },
            new() { CId = "RW", Name = "Butare" },
            new() { CId = "RW", Name = "Byumba" },
            new() { CId = "RW", Name = "Cyangugu" },
            new() { CId = "RW", Name = "Gikongoro" },
            new() { CId = "RW", Name = "Gisenyi" },
            new() { CId = "RW", Name = "Gitarama" },
            new() { CId = "RW", Name = "Kibungo" },
            new() { CId = "RW", Name = "Kibuye" },
            new() { CId = "RW", Name = "Kigali Rurale" },
            new() { CId = "RW", Name = "Kigali-ville" },
            new() { CId = "RW", Name = "Ruhengeri" },
            new() { CId = "RW", Name = "Umutara" },
            new() { CId = "KN", Name = "Christ Church Nichola Town" },
            new() { CId = "KN", Name = "Saint Anne Sandy Point" },
            new() { CId = "KN", Name = "Saint George Basseterre" },
            new() { CId = "KN", Name = "Saint George Gingerland" },
            new() { CId = "KN", Name = "Saint James Windward" },
            new() { CId = "KN", Name = "Saint John Capesterre" },
            new() { CId = "KN", Name = "Saint John Figtree" },
            new() { CId = "KN", Name = "Saint Mary Cayon" },
            new() { CId = "KN", Name = "Saint Paul Capesterre" },
            new() { CId = "KN", Name = "Saint Paul Charlestown" },
            new() { CId = "KN", Name = "Saint Peter Basseterre" },
            new() { CId = "KN", Name = "Saint Thomas Lowland" },
            new() { CId = "KN", Name = "Saint Thomas Middle Island" },
            new() { CId = "KN", Name = "Trinity Palmetto Point" },
            new() { CId = "LC", Name = "Anse-la-Raye" },
            new() { CId = "LC", Name = "Castries" },
            new() { CId = "LC", Name = "Choiseul" },
            new() { CId = "LC", Name = "Dauphin" },
            new() { CId = "LC", Name = "Dennery" },
            new() { CId = "LC", Name = "Gros-Islet" },
            new() { CId = "LC", Name = "Laborie" },
            new() { CId = "LC", Name = "Micoud" },
            new() { CId = "LC", Name = "Praslin" },
            new() { CId = "LC", Name = "Soufriere" },
            new() { CId = "LC", Name = "Vieux-Fort" },
            new() { CId = "VC", Name = "Charlotte" },
            new() { CId = "VC", Name = "Grenadines" },
            new() { CId = "VC", Name = "Saint Andrew" },
            new() { CId = "VC", Name = "Saint David" },
            new() { CId = "VC", Name = "Saint George" },
            new() { CId = "VC", Name = "Saint Patrick" },
            new() { CId = "WS", Name = "A'ana" },
            new() { CId = "WS", Name = "Aiga-i-le-Tai" },
            new() { CId = "WS", Name = "Atua" },
            new() { CId = "WS", Name = "Fa'asaleleaga" },
            new() { CId = "WS", Name = "Gaga'emauga" },
            new() { CId = "WS", Name = "Gagaifomauga" },
            new() { CId = "WS", Name = "Palauli" },
            new() { CId = "WS", Name = "Satupa'itea" },
            new() { CId = "WS", Name = "Tuamasaga" },
            new() { CId = "WS", Name = "Va'a-o-Fonoti" },
            new() { CId = "WS", Name = "Vaisigano" },
            new() { CId = "SM", Name = "Acquaviva" },
            new() { CId = "SM", Name = "Borgo Maggiore" },
            new() { CId = "SM", Name = "Chiesanuova" },
            new() { CId = "SM", Name = "Domagnano" },
            new() { CId = "SM", Name = "Faetano" },
            new() { CId = "SM", Name = "Fiorentino" },
            new() { CId = "SM", Name = "Montegiardino" },
            new() { CId = "SM", Name = "Citta di San Marino" },
            new() { CId = "SM", Name = "Serravalle" },
            new() { CId = "ST", Name = "Sao Tome" },
            new() { CId = "ST", Name = "Principe" },
            new() { CId = "SA", Name = "Al Bahah" },
            new() { CId = "SA", Name = "Al Hudud ash Shamaliyah" },
            new() { CId = "SA", Name = "Al Jawf" },
            new() { CId = "SA", Name = "Al Madinah" },
            new() { CId = "SA", Name = "Al Qasim" },
            new() { CId = "SA", Name = "Ar Riyad" },
            new() { CId = "SA", Name = "Ash Sharqiyah (Eastern)" },
            new() { CId = "SA", Name = "'Asir" },
            new() { CId = "SA", Name = "Ha'il" },
            new() { CId = "SA", Name = "Jizan" },
            new() { CId = "SA", Name = "Makkah" },
            new() { CId = "SA", Name = "Najran" },
            new() { CId = "SA", Name = "Tabuk" },
            new() { CId = "SN", Name = "Dakar" },
            new() { CId = "SN", Name = "Diourbel" },
            new() { CId = "SN", Name = "Fatick" },
            new() { CId = "SN", Name = "Kaolack" },
            new() { CId = "SN", Name = "Kolda" },
            new() { CId = "SN", Name = "Louga" },
            new() { CId = "SN", Name = "Matam" },
            new() { CId = "SN", Name = "Saint-Louis" },
            new() { CId = "SN", Name = "Tambacounda" },
            new() { CId = "SN", Name = "Thies" },
            new() { CId = "SN", Name = "Ziguinchor" },
            new() { CId = "SC", Name = "Anse aux Pins" },
            new() { CId = "SC", Name = "Anse Boileau" },
            new() { CId = "SC", Name = "Anse Etoile" },
            new() { CId = "SC", Name = "Anse Louis" },
            new() { CId = "SC", Name = "Anse Royale" },
            new() { CId = "SC", Name = "Baie Lazare" },
            new() { CId = "SC", Name = "Baie Sainte Anne" },
            new() { CId = "SC", Name = "Beau Vallon" },
            new() { CId = "SC", Name = "Bel Air" },
            new() { CId = "SC", Name = "Bel Ombre" },
            new() { CId = "SC", Name = "Cascade" },
            new() { CId = "SC", Name = "Glacis" },
            new() { CId = "SC", Name = "Grand' Anse (on Mahe)" },
            new() { CId = "SC", Name = "Grand' Anse (on Praslin)" },
            new() { CId = "SC", Name = "La Digue" },
            new() { CId = "SC", Name = "La Riviere Anglaise" },
            new() { CId = "SC", Name = "Mont Buxton" },
            new() { CId = "SC", Name = "Mont Fleuri" },
            new() { CId = "SC", Name = "Plaisance" },
            new() { CId = "SC", Name = "Pointe La Rue" },
            new() { CId = "SC", Name = "Port Glaud" },
            new() { CId = "SC", Name = "Saint Louis" },
            new() { CId = "SC", Name = "Takamaka" },
            new() { CId = "SL", Name = "Eastern" },
            new() { CId = "SL", Name = "Northern" },
            new() { CId = "SL", Name = "Southern" },
            new() { CId = "SL", Name = "Western" },
            new() { CId = "SK", Name = "Banskobystrický" },
            new() { CId = "SK", Name = "Bratislavský" },
            new() { CId = "SK", Name = "Košický" },
            new() { CId = "SK", Name = "Nitriansky" },
            new() { CId = "SK", Name = "Prešovský" },
            new() { CId = "SK", Name = "Trenčiansky" },
            new() { CId = "SK", Name = "Trnavský" },
            new() { CId = "SK", Name = "Žilinský" },
            new() { CId = "SB", Name = "Central" },
            new() { CId = "SB", Name = "Choiseul" },
            new() { CId = "SB", Name = "Guadalcanal" },
            new() { CId = "SB", Name = "Honiara" },
            new() { CId = "SB", Name = "Isabel" },
            new() { CId = "SB", Name = "Makira" },
            new() { CId = "SB", Name = "Malaita" },
            new() { CId = "SB", Name = "Rennell and Bellona" },
            new() { CId = "SB", Name = "Temotu" },
            new() { CId = "SB", Name = "Western" },
            new() { CId = "SO", Name = "Awdal" },
            new() { CId = "SO", Name = "Bakool" },
            new() { CId = "SO", Name = "Banaadir" },
            new() { CId = "SO", Name = "Bari" },
            new() { CId = "SO", Name = "Bay" },
            new() { CId = "SO", Name = "Galguduud" },
            new() { CId = "SO", Name = "Gedo" },
            new() { CId = "SO", Name = "Hiiraan" },
            new() { CId = "SO", Name = "Jubbada Dhexe" },
            new() { CId = "SO", Name = "Jubbada Hoose" },
            new() { CId = "SO", Name = "Mudug" },
            new() { CId = "SO", Name = "Nugaal" },
            new() { CId = "SO", Name = "Sanaag" },
            new() { CId = "SO", Name = "Shabeellaha Dhexe" },
            new() { CId = "SO", Name = "Shabeellaha Hoose" },
            new() { CId = "SO", Name = "Sool" },
            new() { CId = "SO", Name = "Togdheer" },
            new() { CId = "SO", Name = "Woqooyi Galbeed" },
            new() { CId = "ZA", Name = "Eastern Cape" },
            new() { CId = "ZA", Name = "Free State" },
            new() { CId = "ZA", Name = "Gauteng" },
            new() { CId = "ZA", Name = "KwaZulu-Natal" },
            new() { CId = "ZA", Name = "Limpopo" },
            new() { CId = "ZA", Name = "Mpumalanga" },
            new() { CId = "ZA", Name = "North West" },
            new() { CId = "ZA", Name = "Northern Cape" },
            new() { CId = "ZA", Name = "Western Cape" },
            new() { CId = "ES", Name = "La Coru&ntilde;a" },
            new() { CId = "ES", Name = "&Aacute;lava" },
            new() { CId = "ES", Name = "Albacete" },
            new() { CId = "ES", Name = "Alicante" },
            new() { CId = "ES", Name = "Almeria" },
            new() { CId = "ES", Name = "Asturias" },
            new() { CId = "ES", Name = "&Aacute;vila" },
            new() { CId = "ES", Name = "Badajoz" },
            new() { CId = "ES", Name = "Baleares" },
            new() { CId = "ES", Name = "Barcelona" },
            new() { CId = "ES", Name = "Burgos" },
            new() { CId = "ES", Name = "C&aacute;ceres" },
            new() { CId = "ES", Name = "C&aacute;diz" },
            new() { CId = "ES", Name = "Cantabria" },
            new() { CId = "ES", Name = "Castell&oacute;n" },
            new() { CId = "ES", Name = "Ceuta" },
            new() { CId = "ES", Name = "Ciudad Real" },
            new() { CId = "ES", Name = "C&oacute;rdoba" },
            new() { CId = "ES", Name = "Cuenca" },
            new() { CId = "ES", Name = "Girona" },
            new() { CId = "ES", Name = "Granada" },
            new() { CId = "ES", Name = "Guadalajara" },
            new() { CId = "ES", Name = "Guip&uacute;zcoa" },
            new() { CId = "ES", Name = "Huelva" },
            new() { CId = "ES", Name = "Huesca" },
            new() { CId = "ES", Name = "Ja&eacute;n" },
            new() { CId = "ES", Name = "La Rioja" },
            new() { CId = "ES", Name = "Las Palmas" },
            new() { CId = "ES", Name = "Leon" },
            new() { CId = "ES", Name = "Lleida" },
            new() { CId = "ES", Name = "Lugo" },
            new() { CId = "ES", Name = "Madrid" },
            new() { CId = "ES", Name = "Malaga" },
            new() { CId = "ES", Name = "Melilla" },
            new() { CId = "ES", Name = "Murcia" },
            new() { CId = "ES", Name = "Navarra" },
            new() { CId = "ES", Name = "Ourense" },
            new() { CId = "ES", Name = "Palencia" },
            new() { CId = "ES", Name = "Pontevedra" },
            new() { CId = "ES", Name = "Salamanca" },
            new() { CId = "ES", Name = "Santa Cruz de Tenerife" },
            new() { CId = "ES", Name = "Segovia" },
            new() { CId = "ES", Name = "Sevilla" },
            new() { CId = "ES", Name = "Soria" },
            new() { CId = "ES", Name = "Tarragona" },
            new() { CId = "ES", Name = "Teruel" },
            new() { CId = "ES", Name = "Toledo" },
            new() { CId = "ES", Name = "Valencia" },
            new() { CId = "ES", Name = "Valladolid" },
            new() { CId = "ES", Name = "Vizcaya" },
            new() { CId = "ES", Name = "Zamora" },
            new() { CId = "ES", Name = "Zaragoza" },
            new() { CId = "LK", Name = "Central" },
            new() { CId = "LK", Name = "Eastern" },
            new() { CId = "LK", Name = "North Central" },
            new() { CId = "LK", Name = "Northern" },
            new() { CId = "LK", Name = "North Western" },
            new() { CId = "LK", Name = "Sabaragamuwa" },
            new() { CId = "LK", Name = "Southern" },
            new() { CId = "LK", Name = "Uva" },
            new() { CId = "LK", Name = "Western" },
            new() { CId = "SH", Name = "Saint Helena" },
            new() { CId = "SD", Name = "A'ali an Nil" },
            new() { CId = "SD", Name = "Al Bahr al Ahmar" },
            new() { CId = "SD", Name = "Al Buhayrat" },
            new() { CId = "SD", Name = "Al Jazirah" },
            new() { CId = "SD", Name = "Al Khartum" },
            new() { CId = "SD", Name = "Al Qadarif" },
            new() { CId = "SD", Name = "Al Wahdah" },
            new() { CId = "SD", Name = "An Nil al Abyad" },
            new() { CId = "SD", Name = "An Nil al Azraq" },
            new() { CId = "SD", Name = "Ash Shamaliyah" },
            new() { CId = "SD", Name = "Bahr al Jabal" },
            new() { CId = "SD", Name = "Gharb al Istiwa'iyah" },
            new() { CId = "SD", Name = "Gharb Bahr al Ghazal" },
            new() { CId = "SD", Name = "Gharb Darfur" },
            new() { CId = "SD", Name = "Gharb Kurdufan" },
            new() { CId = "SD", Name = "Janub Darfur" },
            new() { CId = "SD", Name = "Janub Kurdufan" },
            new() { CId = "SD", Name = "Junqali" },
            new() { CId = "SD", Name = "Kassala" },
            new() { CId = "SD", Name = "Nahr an Nil" },
            new() { CId = "SD", Name = "Shamal Bahr al Ghazal" },
            new() { CId = "SD", Name = "Shamal Darfur" },
            new() { CId = "SD", Name = "Shamal Kurdufan" },
            new() { CId = "SD", Name = "Sharq al Istiwa'iyah" },
            new() { CId = "SD", Name = "Sinnar" },
            new() { CId = "SD", Name = "Warab" },
            new() { CId = "SR", Name = "Brokopondo" },
            new() { CId = "SR", Name = "Commewijne" },
            new() { CId = "SR", Name = "Coronie" },
            new() { CId = "SR", Name = "Marowijne" },
            new() { CId = "SR", Name = "Nickerie" },
            new() { CId = "SR", Name = "Para" },
            new() { CId = "SR", Name = "Paramaribo" },
            new() { CId = "SR", Name = "Saramacca" },
            new() { CId = "SR", Name = "Sipaliwini" },
            new() { CId = "SR", Name = "Wanica" },
            new() { CId = "SZ", Name = "Hhohho" },
            new() { CId = "SZ", Name = "Lubombo" },
            new() { CId = "SZ", Name = "Manzini" },
            new() { CId = "SZ", Name = "Shishelweni" },
            new() { CId = "SE", Name = "Blekinge" },
            new() { CId = "SE", Name = "Dalarna" },
            new() { CId = "SE", Name = "Gävleborg" },
            new() { CId = "SE", Name = "Gotland" },
            new() { CId = "SE", Name = "Halland" },
            new() { CId = "SE", Name = "Jämtland" },
            new() { CId = "SE", Name = "Jönköping" },
            new() { CId = "SE", Name = "Kalmar" },
            new() { CId = "SE", Name = "Kronoberg" },
            new() { CId = "SE", Name = "Norrbotten" },
            new() { CId = "SE", Name = "Örebro" },
            new() { CId = "SE", Name = "Östergötland" },
            new() { CId = "SE", Name = "Sk&aring;ne" },
            new() { CId = "SE", Name = "Södermanland" },
            new() { CId = "SE", Name = "Stockholm" },
            new() { CId = "SE", Name = "Uppsala" },
            new() { CId = "SE", Name = "Värmland" },
            new() { CId = "SE", Name = "Västerbotten" },
            new() { CId = "SE", Name = "Västernorrland" },
            new() { CId = "SE", Name = "Västmanland" },
            new() { CId = "SE", Name = "Västra Götaland" },
            new() { CId = "CH", Name = "Aargau" },
            new() { CId = "CH", Name = "Appenzell Ausserrhoden" },
            new() { CId = "CH", Name = "Appenzell Innerrhoden" },
            new() { CId = "CH", Name = "Basel-Stadt" },
            new() { CId = "CH", Name = "Basel-Landschaft" },
            new() { CId = "CH", Name = "Bern" },
            new() { CId = "CH", Name = "Fribourg" },
            new() { CId = "CH", Name = "Gen&egrave;ve" },
            new() { CId = "CH", Name = "Glarus" },
            new() { CId = "CH", Name = "Graubünden" },
            new() { CId = "CH", Name = "Jura" },
            new() { CId = "CH", Name = "Luzern" },
            new() { CId = "CH", Name = "Neuch&acirc;tel" },
            new() { CId = "CH", Name = "Nidwald" },
            new() { CId = "CH", Name = "Obwald" },
            new() { CId = "CH", Name = "St. Gallen" },
            new() { CId = "CH", Name = "Schaffhausen" },
            new() { CId = "CH", Name = "Schwyz" },
            new() { CId = "CH", Name = "Solothurn" },
            new() { CId = "CH", Name = "Thurgau" },
            new() { CId = "CH", Name = "Ticino" },
            new() { CId = "CH", Name = "Uri" },
            new() { CId = "CH", Name = "Valais" },
            new() { CId = "CH", Name = "Vaud" },
            new() { CId = "CH", Name = "Zug" },
            new() { CId = "CH", Name = "Zürich" },
            new() { CId = "SY", Name = "Al Hasakah" },
            new() { CId = "SY", Name = "Al Ladhiqiyah" },
            new() { CId = "SY", Name = "Al Qunaytirah" },
            new() { CId = "SY", Name = "Ar Raqqah" },
            new() { CId = "SY", Name = "As Suwayda" },
            new() { CId = "SY", Name = "Dara" },
            new() { CId = "SY", Name = "Dayr az Zawr" },
            new() { CId = "SY", Name = "Dimashq" },
            new() { CId = "SY", Name = "Halab" },
            new() { CId = "SY", Name = "Hamah" },
            new() { CId = "SY", Name = "Hims" },
            new() { CId = "SY", Name = "Idlib" },
            new() { CId = "SY", Name = "Rif Dimashq" },
            new() { CId = "SY", Name = "Tartus" },
            new() { CId = "TJ", Name = "Gorno-Badakhstan" },
            new() { CId = "TJ", Name = "Khatlon" },
            new() { CId = "TJ", Name = "Sughd" },
            new() { CId = "TZ", Name = "Arusha" },
            new() { CId = "TZ", Name = "Dar es Salaam" },
            new() { CId = "TZ", Name = "Dodoma" },
            new() { CId = "TZ", Name = "Iringa" },
            new() { CId = "TZ", Name = "Kagera" },
            new() { CId = "TZ", Name = "Kigoma" },
            new() { CId = "TZ", Name = "Kilimanjaro" },
            new() { CId = "TZ", Name = "Lindi" },
            new() { CId = "TZ", Name = "Manyara" },
            new() { CId = "TZ", Name = "Mara" },
            new() { CId = "TZ", Name = "Mbeya" },
            new() { CId = "TZ", Name = "Morogoro" },
            new() { CId = "TZ", Name = "Mtwara" },
            new() { CId = "TZ", Name = "Mwanza" },
            new() { CId = "TZ", Name = "Pemba North" },
            new() { CId = "TZ", Name = "Pemba South" },
            new() { CId = "TZ", Name = "Pwani" },
            new() { CId = "TZ", Name = "Rukwa" },
            new() { CId = "TZ", Name = "Ruvuma" },
            new() { CId = "TZ", Name = "Shinyanga" },
            new() { CId = "TZ", Name = "Singida" },
            new() { CId = "TZ", Name = "Tabora" },
            new() { CId = "TZ", Name = "Tanga" },
            new() { CId = "TZ", Name = "Zanzibar Central/South" },
            new() { CId = "TZ", Name = "Zanzibar North" },
            new() { CId = "TZ", Name = "Zanzibar Urban/West" },
            new() { CId = "TH", Name = "Amnat Charoen" },
            new() { CId = "TH", Name = "Ang Thong" },
            new() { CId = "TH", Name = "Ayutthaya" },
            new() { CId = "TH", Name = "Bangkok" },
            new() { CId = "TH", Name = "Buriram" },
            new() { CId = "TH", Name = "Chachoengsao" },
            new() { CId = "TH", Name = "Chai Nat" },
            new() { CId = "TH", Name = "Chaiyaphum" },
            new() { CId = "TH", Name = "Chanthaburi" },
            new() { CId = "TH", Name = "Chiang Mai" },
            new() { CId = "TH", Name = "Chiang Rai" },
            new() { CId = "TH", Name = "Chon Buri" },
            new() { CId = "TH", Name = "Chumphon" },
            new() { CId = "TH", Name = "Kalasin" },
            new() { CId = "TH", Name = "Kamphaeng Phet" },
            new() { CId = "TH", Name = "Kanchanaburi" },
            new() { CId = "TH", Name = "Khon Kaen" },
            new() { CId = "TH", Name = "Krabi" },
            new() { CId = "TH", Name = "Lampang" },
            new() { CId = "TH", Name = "Lamphun" },
            new() { CId = "TH", Name = "Loei" },
            new() { CId = "TH", Name = "Lop Buri" },
            new() { CId = "TH", Name = "Mae Hong Son" },
            new() { CId = "TH", Name = "Maha Sarakham" },
            new() { CId = "TH", Name = "Mukdahan" },
            new() { CId = "TH", Name = "Nakhon Nayok" },
            new() { CId = "TH", Name = "Nakhon Pathom" },
            new() { CId = "TH", Name = "Nakhon Phanom" },
            new() { CId = "TH", Name = "Nakhon Ratchasima" },
            new() { CId = "TH", Name = "Nakhon Sawan" },
            new() { CId = "TH", Name = "Nakhon Si Thammarat" },
            new() { CId = "TH", Name = "Nan" },
            new() { CId = "TH", Name = "Narathiwat" },
            new() { CId = "TH", Name = "Nong Bua Lamphu" },
            new() { CId = "TH", Name = "Nong Khai" },
            new() { CId = "TH", Name = "Nonthaburi" },
            new() { CId = "TH", Name = "Pathum Thani" },
            new() { CId = "TH", Name = "Pattani" },
            new() { CId = "TH", Name = "Phangnga" },
            new() { CId = "TH", Name = "Phatthalung" },
            new() { CId = "TH", Name = "Phayao" },
            new() { CId = "TH", Name = "Phetchabun" },
            new() { CId = "TH", Name = "Phetchaburi" },
            new() { CId = "TH", Name = "Phichit" },
            new() { CId = "TH", Name = "Phitsanulok" },
            new() { CId = "TH", Name = "Phrae" },
            new() { CId = "TH", Name = "Phuket" },
            new() { CId = "TH", Name = "Prachin Buri" },
            new() { CId = "TH", Name = "Prachuap Khiri Khan" },
            new() { CId = "TH", Name = "Ranong" },
            new() { CId = "TH", Name = "Ratchaburi" },
            new() { CId = "TH", Name = "Rayong" },
            new() { CId = "TH", Name = "Roi Et" },
            new() { CId = "TH", Name = "Sa Kaeo" },
            new() { CId = "TH", Name = "Sakon Nakhon" },
            new() { CId = "TH", Name = "Samut Prakan" },
            new() { CId = "TH", Name = "Samut Sakhon" },
            new() { CId = "TH", Name = "Samut Songkhram" },
            new() { CId = "TH", Name = "Sara Buri" },
            new() { CId = "TH", Name = "Satun" },
            new() { CId = "TH", Name = "Sing Buri" },
            new() { CId = "TH", Name = "Sisaket" },
            new() { CId = "TH", Name = "Songkhla" },
            new() { CId = "TH", Name = "Sukhothai" },
            new() { CId = "TH", Name = "Suphan Buri" },
            new() { CId = "TH", Name = "Surat Thani" },
            new() { CId = "TH", Name = "Surin" },
            new() { CId = "TH", Name = "Tak" },
            new() { CId = "TH", Name = "Trang" },
            new() { CId = "TH", Name = "Trat" },
            new() { CId = "TH", Name = "Ubon Ratchathani" },
            new() { CId = "TH", Name = "Udon Thani" },
            new() { CId = "TH", Name = "Uthai Thani" },
            new() { CId = "TH", Name = "Uttaradit" },
            new() { CId = "TH", Name = "Yala" },
            new() { CId = "TH", Name = "Yasothon" },
            new() { CId = "TG", Name = "Kara" },
            new() { CId = "TG", Name = "Plateaux" },
            new() { CId = "TG", Name = "Savanes" },
            new() { CId = "TG", Name = "Centrale" },
            new() { CId = "TG", Name = "Maritime" },
            new() { CId = "TK", Name = "Atafu" },
            new() { CId = "TK", Name = "Fakaofo" },
            new() { CId = "TK", Name = "Nukunonu" },
            new() { CId = "TO", Name = "Ha'apai" },
            new() { CId = "TO", Name = "Tongatapu" },
            new() { CId = "TO", Name = "Vava'u" },
            new() { CId = "TT", Name = "Couva/Tabaquite/Talparo" },
            new() { CId = "TT", Name = "Diego Martin" },
            new() { CId = "TT", Name = "Mayaro/Rio Claro" },
            new() { CId = "TT", Name = "Penal/Debe" },
            new() { CId = "TT", Name = "Princes Town" },
            new() { CId = "TT", Name = "Sangre Grande" },
            new() { CId = "TT", Name = "San Juan/Laventille" },
            new() { CId = "TT", Name = "Siparia" },
            new() { CId = "TT", Name = "Tunapuna/Piarco" },
            new() { CId = "TT", Name = "Port of Spain" },
            new() { CId = "TT", Name = "San Fernando" },
            new() { CId = "TT", Name = "Arima" },
            new() { CId = "TT", Name = "Point Fortin" },
            new() { CId = "TT", Name = "Chaguanas" },
            new() { CId = "TT", Name = "Tobago" },
            new() { CId = "TN", Name = "Ariana" },
            new() { CId = "TN", Name = "Beja" },
            new() { CId = "TN", Name = "Ben Arous" },
            new() { CId = "TN", Name = "Bizerte" },
            new() { CId = "TN", Name = "Gabes" },
            new() { CId = "TN", Name = "Gafsa" },
            new() { CId = "TN", Name = "Jendouba" },
            new() { CId = "TN", Name = "Kairouan" },
            new() { CId = "TN", Name = "Kasserine" },
            new() { CId = "TN", Name = "Kebili" },
            new() { CId = "TN", Name = "Kef" },
            new() { CId = "TN", Name = "Mahdia" },
            new() { CId = "TN", Name = "Manouba" },
            new() { CId = "TN", Name = "Medenine" },
            new() { CId = "TN", Name = "Monastir" },
            new() { CId = "TN", Name = "Nabeul" },
            new() { CId = "TN", Name = "Sfax" },
            new() { CId = "TN", Name = "Sidi" },
            new() { CId = "TN", Name = "Siliana" },
            new() { CId = "TN", Name = "Sousse" },
            new() { CId = "TN", Name = "Tataouine" },
            new() { CId = "TN", Name = "Tozeur" },
            new() { CId = "TN", Name = "Tunis" },
            new() { CId = "TN", Name = "Zaghouan" },
            new() { CId = "TR", Name = "Adana" },
            new() { CId = "TR", Name = "Adıyaman" },
            new() { CId = "TR", Name = "Afyonkarahisar" },
            new() { CId = "TR", Name = "Ağrı" },
            new() { CId = "TR", Name = "Aksaray" },
            new() { CId = "TR", Name = "Amasya" },
            new() { CId = "TR", Name = "Ankara" },
            new() { CId = "TR", Name = "Antalya" },
            new() { CId = "TR", Name = "Ardahan" },
            new() { CId = "TR", Name = "Artvin" },
            new() { CId = "TR", Name = "Aydın" },
            new() { CId = "TR", Name = "Balıkesir" },
            new() { CId = "TR", Name = "Bartın" },
            new() { CId = "TR", Name = "Batman" },
            new() { CId = "TR", Name = "Bayburt" },
            new() { CId = "TR", Name = "Bilecik" },
            new() { CId = "TR", Name = "Bingöl" },
            new() { CId = "TR", Name = "Bitlis" },
            new() { CId = "TR", Name = "Bolu" },
            new() { CId = "TR", Name = "Burdur" },
            new() { CId = "TR", Name = "Bursa" },
            new() { CId = "TR", Name = "Çanakkale" },
            new() { CId = "TR", Name = "Çankırı" },
            new() { CId = "TR", Name = "Çorum" },
            new() { CId = "TR", Name = "Denizli" },
            new() { CId = "TR", Name = "Diyarbakır" },
            new() { CId = "TR", Name = "Düzce" },
            new() { CId = "TR", Name = "Edirne" },
            new() { CId = "TR", Name = "Elazığ" },
            new() { CId = "TR", Name = "Erzincan" },
            new() { CId = "TR", Name = "Erzurum" },
            new() { CId = "TR", Name = "Eskişehir" },
            new() { CId = "TR", Name = "Gaziantep" },
            new() { CId = "TR", Name = "Giresun" },
            new() { CId = "TR", Name = "Gümüşhane" },
            new() { CId = "TR", Name = "Hakkari" },
            new() { CId = "TR", Name = "Hatay" },
            new() { CId = "TR", Name = "Iğdır" },
            new() { CId = "TR", Name = "Isparta" },
            new() { CId = "TR", Name = "İstanbul" },
            new() { CId = "TR", Name = "İzmir" },
            new() { CId = "TR", Name = "Kahramanmaraş" },
            new() { CId = "TR", Name = "Karabük" },
            new() { CId = "TR", Name = "Karaman" },
            new() { CId = "TR", Name = "Kars" },
            new() { CId = "TR", Name = "Kastamonu" },
            new() { CId = "TR", Name = "Kayseri" },
            new() { CId = "TR", Name = "Kilis" },
            new() { CId = "TR", Name = "Kırıkkale" },
            new() { CId = "TR", Name = "Kırklareli" },
            new() { CId = "TR", Name = "Kırşehir" },
            new() { CId = "TR", Name = "Kocaeli" },
            new() { CId = "TR", Name = "Konya" },
            new() { CId = "TR", Name = "Kütahya" },
            new() { CId = "TR", Name = "Malatya" },
            new() { CId = "TR", Name = "Manisa" },
            new() { CId = "TR", Name = "Mardin" },
            new() { CId = "TR", Name = "Mersin" },
            new() { CId = "TR", Name = "Muğla" },
            new() { CId = "TR", Name = "Muş" },
            new() { CId = "TR", Name = "Nevşehir" },
            new() { CId = "TR", Name = "Niğde" },
            new() { CId = "TR", Name = "Ordu" },
            new() { CId = "TR", Name = "Osmaniye" },
            new() { CId = "TR", Name = "Rize" },
            new() { CId = "TR", Name = "Sakarya" },
            new() { CId = "TR", Name = "Samsun" },
            new() { CId = "TR", Name = "Şanlıurfa" },
            new() { CId = "TR", Name = "Siirt" },
            new() { CId = "TR", Name = "Sinop" },
            new() { CId = "TR", Name = "Şırnak" },
            new() { CId = "TR", Name = "Sivas" },
            new() { CId = "TR", Name = "Tekirdağ" },
            new() { CId = "TR", Name = "Tokat" },
            new() { CId = "TR", Name = "Trabzon" },
            new() { CId = "TR", Name = "Tunceli" },
            new() { CId = "TR", Name = "Uşak" },
            new() { CId = "TR", Name = "Van" },
            new() { CId = "TR", Name = "Yalova" },
            new() { CId = "TR", Name = "Yozgat" },
            new() { CId = "TR", Name = "Zonguldak" },
            new() { CId = "TM", Name = "Ahal Welayaty" },
            new() { CId = "TM", Name = "Balkan Welayaty" },
            new() { CId = "TM", Name = "Dashhowuz Welayaty" },
            new() { CId = "TM", Name = "Lebap Welayaty" },
            new() { CId = "TM", Name = "Mary Welayaty" },
            new() { CId = "TC", Name = "Ambergris Cays" },
            new() { CId = "TC", Name = "Dellis Cay" },
            new() { CId = "TC", Name = "French Cay" },
            new() { CId = "TC", Name = "Little Water Cay" },
            new() { CId = "TC", Name = "Parrot Cay" },
            new() { CId = "TC", Name = "Pine Cay" },
            new() { CId = "TC", Name = "Salt Cay" },
            new() { CId = "TC", Name = "Grand Turk" },
            new() { CId = "TC", Name = "South Caicos" },
            new() { CId = "TC", Name = "East Caicos" },
            new() { CId = "TC", Name = "Middle Caicos" },
            new() { CId = "TC", Name = "North Caicos" },
            new() { CId = "TC", Name = "Providenciales" },
            new() { CId = "TC", Name = "West Caicos" },
            new() { CId = "TV", Name = "Nanumanga" },
            new() { CId = "TV", Name = "Niulakita" },
            new() { CId = "TV", Name = "Niutao" },
            new() { CId = "TV", Name = "Funafuti" },
            new() { CId = "TV", Name = "Nanumea" },
            new() { CId = "TV", Name = "Nui" },
            new() { CId = "TV", Name = "Nukufetau" },
            new() { CId = "TV", Name = "Nukulaelae" },
            new() { CId = "TV", Name = "Vaitupu" },
            new() { CId = "UG", Name = "Kalangala" },
            new() { CId = "UG", Name = "Kampala" },
            new() { CId = "UG", Name = "Kayunga" },
            new() { CId = "UG", Name = "Kiboga" },
            new() { CId = "UG", Name = "Luwero" },
            new() { CId = "UG", Name = "Masaka" },
            new() { CId = "UG", Name = "Mpigi" },
            new() { CId = "UG", Name = "Mubende" },
            new() { CId = "UG", Name = "Mukono" },
            new() { CId = "UG", Name = "Nakasongola" },
            new() { CId = "UG", Name = "Rakai" },
            new() { CId = "UG", Name = "Sembabule" },
            new() { CId = "UG", Name = "Wakiso" },
            new() { CId = "UG", Name = "Bugiri" },
            new() { CId = "UG", Name = "Busia" },
            new() { CId = "UG", Name = "Iganga" },
            new() { CId = "UG", Name = "Jinja" },
            new() { CId = "UG", Name = "Kaberamaido" },
            new() { CId = "UG", Name = "Kamuli" },
            new() { CId = "UG", Name = "Kapchorwa" },
            new() { CId = "UG", Name = "Katakwi" },
            new() { CId = "UG", Name = "Kumi" },
            new() { CId = "UG", Name = "Mayuge" },
            new() { CId = "UG", Name = "Mbale" },
            new() { CId = "UG", Name = "Pallisa" },
            new() { CId = "UG", Name = "Sironko" },
            new() { CId = "UG", Name = "Soroti" },
            new() { CId = "UG", Name = "Tororo" },
            new() { CId = "UG", Name = "Adjumani" },
            new() { CId = "UG", Name = "Apac" },
            new() { CId = "UG", Name = "Arua" },
            new() { CId = "UG", Name = "Gulu" },
            new() { CId = "UG", Name = "Kitgum" },
            new() { CId = "UG", Name = "Kotido" },
            new() { CId = "UG", Name = "Lira" },
            new() { CId = "UG", Name = "Moroto" },
            new() { CId = "UG", Name = "Moyo" },
            new() { CId = "UG", Name = "Nakapiripirit" },
            new() { CId = "UG", Name = "Nebbi" },
            new() { CId = "UG", Name = "Pader" },
            new() { CId = "UG", Name = "Yumbe" },
            new() { CId = "UG", Name = "Bundibugyo" },
            new() { CId = "UG", Name = "Bushenyi" },
            new() { CId = "UG", Name = "Hoima" },
            new() { CId = "UG", Name = "Kabale" },
            new() { CId = "UG", Name = "Kabarole" },
            new() { CId = "UG", Name = "Kamwenge" },
            new() { CId = "UG", Name = "Kanungu" },
            new() { CId = "UG", Name = "Kasese" },
            new() { CId = "UG", Name = "Kibaale" },
            new() { CId = "UG", Name = "Kisoro" },
            new() { CId = "UG", Name = "Kyenjojo" },
            new() { CId = "UG", Name = "Masindi" },
            new() { CId = "UG", Name = "Mbarara" },
            new() { CId = "UG", Name = "Ntungamo" },
            new() { CId = "UG", Name = "Rukungiri" },
            new() { CId = "UA", Name = "Cherkas'ka Oblast'" },
            new() { CId = "UA", Name = "Chernihivs'ka Oblast'" },
            new() { CId = "UA", Name = "Chernivets'ka Oblast'" },
            new() { CId = "UA", Name = "Crimea" },
            new() { CId = "UA", Name = "Dnipropetrovs'ka Oblast'" },
            new() { CId = "UA", Name = "Donets'ka Oblast'" },
            new() { CId = "UA", Name = "Ivano-Frankivs'ka Oblast'" },
            new() { CId = "UA", Name = "Khersons'ka Oblast'" },
            new() { CId = "UA", Name = "Khmel'nyts'ka Oblast'" },
            new() { CId = "UA", Name = "Kirovohrads'ka Oblast'" },
            new() { CId = "UA", Name = "Kyiv" },
            new() { CId = "UA", Name = "Kyivs'ka Oblast'" },
            new() { CId = "UA", Name = "Luhans'ka Oblast'" },
            new() { CId = "UA", Name = "L'vivs'ka Oblast'" },
            new() { CId = "UA", Name = "Mykolayivs'ka Oblast'" },
            new() { CId = "UA", Name = "Odes'ka Oblast'" },
            new() { CId = "UA", Name = "Poltavs'ka Oblast'" },
            new() { CId = "UA", Name = "Rivnens'ka Oblast'" },
            new() { CId = "UA", Name = "Sevastopol'" },
            new() { CId = "UA", Name = "Sums'ka Oblast'" },
            new() { CId = "UA", Name = "Ternopil's'ka Oblast'" },
            new() { CId = "UA", Name = "Vinnyts'ka Oblast'" },
            new() { CId = "UA", Name = "Volyns'ka Oblast'" },
            new() { CId = "UA", Name = "Zakarpats'ka Oblast'" },
            new() { CId = "UA", Name = "Zaporiz'ka Oblast'" },
            new() { CId = "UA", Name = "Zhytomyrs'ka oblast'" },
            new() { CId = "AE", Name = "Abu Dhabi" },
            new() { CId = "AE", Name = "'Ajman" },
            new() { CId = "AE", Name = "Al Fujayrah" },
            new() { CId = "AE", Name = "Ash Shariqah" },
            new() { CId = "AE", Name = "Dubai" },
            new() { CId = "AE", Name = "R'as al Khaymah" },
            new() { CId = "AE", Name = "Umm al Qaywayn" },
            new() { CId = "GB", Name = "Aberdeen" },
            new() { CId = "GB", Name = "Aberdeenshire" },
            new() { CId = "GB", Name = "Anglesey" },
            new() { CId = "GB", Name = "Angus" },
            new() { CId = "GB", Name = "Argyll and Bute" },
            new() { CId = "GB", Name = "Bedfordshire" },
            new() { CId = "GB", Name = "Berkshire" },
            new() { CId = "GB", Name = "Blaenau Gwent" },
            new() { CId = "GB", Name = "Bridgend" },
            new() { CId = "GB", Name = "Bristol" },
            new() { CId = "GB", Name = "Buckinghamshire" },
            new() { CId = "GB", Name = "Caerphilly" },
            new() { CId = "GB", Name = "Cambridgeshire" },
            new() { CId = "GB", Name = "Cardiff" },
            new() { CId = "GB", Name = "Carmarthenshire" },
            new() { CId = "GB", Name = "Ceredigion" },
            new() { CId = "GB", Name = "Cheshire" },
            new() { CId = "GB", Name = "Clackmannanshire" },
            new() { CId = "GB", Name = "Conwy" },
            new() { CId = "GB", Name = "Cornwall" },
            new() { CId = "GB", Name = "Denbighshire" },
            new() { CId = "GB", Name = "Derbyshire" },
            new() { CId = "GB", Name = "Devon" },
            new() { CId = "GB", Name = "Dorset" },
            new() { CId = "GB", Name = "Dumfries and Galloway" },
            new() { CId = "GB", Name = "Dundee" },
            new() { CId = "GB", Name = "Durham" },
            new() { CId = "GB", Name = "East Ayrshire" },
            new() { CId = "GB", Name = "East Dunbartonshire" },
            new() { CId = "GB", Name = "East Lothian" },
            new() { CId = "GB", Name = "East Renfrewshire" },
            new() { CId = "GB", Name = "East Riding of Yorkshire" },
            new() { CId = "GB", Name = "East Sussex" },
            new() { CId = "GB", Name = "Edinburgh" },
            new() { CId = "GB", Name = "Essex" },
            new() { CId = "GB", Name = "Falkirk" },
            new() { CId = "GB", Name = "Fife" },
            new() { CId = "GB", Name = "Flintshire" },
            new() { CId = "GB", Name = "Glasgow" },
            new() { CId = "GB", Name = "Gloucestershire" },
            new() { CId = "GB", Name = "Greater London" },
            new() { CId = "GB", Name = "Greater Manchester" },
            new() { CId = "GB", Name = "Gwynedd" },
            new() { CId = "GB", Name = "Hampshire" },
            new() { CId = "GB", Name = "Herefordshire" },
            new() { CId = "GB", Name = "Hertfordshire" },
            new() { CId = "GB", Name = "Highlands" },
            new() { CId = "GB", Name = "Inverclyde" },
            new() { CId = "GB", Name = "Isle of Wight" },
            new() { CId = "GB", Name = "Kent" },
            new() { CId = "GB", Name = "Lancashire" },
            new() { CId = "GB", Name = "Leicestershire" },
            new() { CId = "GB", Name = "Lincolnshire" },
            new() { CId = "GB", Name = "Merseyside" },
            new() { CId = "GB", Name = "Merthyr Tydfil" },
            new() { CId = "GB", Name = "Midlothian" },
            new() { CId = "GB", Name = "Monmouthshire" },
            new() { CId = "GB", Name = "Moray" },
            new() { CId = "GB", Name = "Neath Port Talbot" },
            new() { CId = "GB", Name = "Newport" },
            new() { CId = "GB", Name = "Norfolk" },
            new() { CId = "GB", Name = "North Ayrshire" },
            new() { CId = "GB", Name = "North Lanarkshire" },
            new() { CId = "GB", Name = "North Yorkshire" },
            new() { CId = "GB", Name = "Northamptonshire" },
            new() { CId = "GB", Name = "Northumberland" },
            new() { CId = "GB", Name = "Nottinghamshire" },
            new() { CId = "GB", Name = "Orkney Islands" },
            new() { CId = "GB", Name = "Oxfordshire" },
            new() { CId = "GB", Name = "Pembrokeshire" },
            new() { CId = "GB", Name = "Perth and Kinross" },
            new() { CId = "GB", Name = "Powys" },
            new() { CId = "GB", Name = "Renfrewshire" },
            new() { CId = "GB", Name = "Rhondda Cynon Taff" },
            new() { CId = "GB", Name = "Rutland" },
            new() { CId = "GB", Name = "Scottish Borders" },
            new() { CId = "GB", Name = "Shetland Islands" },
            new() { CId = "GB", Name = "Shropshire" },
            new() { CId = "GB", Name = "Somerset" },
            new() { CId = "GB", Name = "South Ayrshire" },
            new() { CId = "GB", Name = "South Lanarkshire" },
            new() { CId = "GB", Name = "South Yorkshire" },
            new() { CId = "GB", Name = "Staffordshire" },
            new() { CId = "GB", Name = "Stirling" },
            new() { CId = "GB", Name = "Suffolk" },
            new() { CId = "GB", Name = "Surrey" },
            new() { CId = "GB", Name = "Swansea" },
            new() { CId = "GB", Name = "Torfaen" },
            new() { CId = "GB", Name = "Tyne and Wear" },
            new() { CId = "GB", Name = "Vale of Glamorgan" },
            new() { CId = "GB", Name = "Warwickshire" },
            new() { CId = "GB", Name = "West Dunbartonshire" },
            new() { CId = "GB", Name = "West Lothian" },
            new() { CId = "GB", Name = "West Midlands" },
            new() { CId = "GB", Name = "West Sussex" },
            new() { CId = "GB", Name = "West Yorkshire" },
            new() { CId = "GB", Name = "Western Isles" },
            new() { CId = "GB", Name = "Wiltshire" },
            new() { CId = "GB", Name = "Worcestershire" },
            new() { CId = "GB", Name = "Wrexham" },
            new() { CId = "US", Name = "Alabama" },
            new() { CId = "US", Name = "Alaska" },
            new() { CId = "US", Name = "American Samoa" },
            new() { CId = "US", Name = "Arizona" },
            new() { CId = "US", Name = "Arkansas" },
            new() { CId = "US", Name = "Armed Forces Africa" },
            new() { CId = "US", Name = "Armed Forces Americas" },
            new() { CId = "US", Name = "Armed Forces Canada" },
            new() { CId = "US", Name = "Armed Forces Europe" },
            new() { CId = "US", Name = "Armed Forces Middle East" },
            new() { CId = "US", Name = "Armed Forces Pacific" },
            new() { CId = "US", Name = "California" },
            new() { CId = "US", Name = "Colorado" },
            new() { CId = "US", Name = "Connecticut" },
            new() { CId = "US", Name = "Delaware" },
            new() { CId = "US", Name = "District of Columbia" },
            new() { CId = "US", Name = "Federated States Of Micronesia" },
            new() { CId = "US", Name = "Florida" },
            new() { CId = "US", Name = "Georgia" },
            new() { CId = "US", Name = "Guam" },
            new() { CId = "US", Name = "Hawaii" },
            new() { CId = "US", Name = "Idaho" },
            new() { CId = "US", Name = "Illinois" },
            new() { CId = "US", Name = "Indiana" },
            new() { CId = "US", Name = "Iowa" },
            new() { CId = "US", Name = "Kansas" },
            new() { CId = "US", Name = "Kentucky" },
            new() { CId = "US", Name = "Louisiana" },
            new() { CId = "US", Name = "Maine" },
            new() { CId = "US", Name = "Marshall Islands" },
            new() { CId = "US", Name = "Maryland" },
            new() { CId = "US", Name = "Massachusetts" },
            new() { CId = "US", Name = "Michigan" },
            new() { CId = "US", Name = "Minnesota" },
            new() { CId = "US", Name = "Mississippi" },
            new() { CId = "US", Name = "Missouri" },
            new() { CId = "US", Name = "Montana" },
            new() { CId = "US", Name = "Nebraska" },
            new() { CId = "US", Name = "Nevada" },
            new() { CId = "US", Name = "New Hampshire" },
            new() { CId = "US", Name = "New Jersey" },
            new() { CId = "US", Name = "New Mexico" },
            new() { CId = "US", Name = "New York" },
            new() { CId = "US", Name = "North Carolina" },
            new() { CId = "US", Name = "North Dakota" },
            new() { CId = "US", Name = "Northern Mariana Islands" },
            new() { CId = "US", Name = "Ohio" },
            new() { CId = "US", Name = "Oklahoma" },
            new() { CId = "US", Name = "Oregon" },
            new() { CId = "US", Name = "Palau" },
            new() { CId = "US", Name = "Pennsylvania" },
            new() { CId = "US", Name = "Puerto Rico" },
            new() { CId = "US", Name = "Rhode Island" },
            new() { CId = "US", Name = "South Carolina" },
            new() { CId = "US", Name = "South Dakota" },
            new() { CId = "US", Name = "Tennessee" },
            new() { CId = "US", Name = "Texas" },
            new() { CId = "US", Name = "Utah" },
            new() { CId = "US", Name = "Vermont" },
            new() { CId = "US", Name = "Virgin Islands" },
            new() { CId = "US", Name = "Virginia" },
            new() { CId = "US", Name = "Washington" },
            new() { CId = "US", Name = "West Virginia" },
            new() { CId = "US", Name = "Wisconsin" },
            new() { CId = "US", Name = "Wyoming" },
            new() { CId = "UM", Name = "Baker Island" },
            new() { CId = "UM", Name = "Howland Island" },
            new() { CId = "UM", Name = "Jarvis Island" },
            new() { CId = "UM", Name = "Johnston Atoll" },
            new() { CId = "UM", Name = "Kingman Reef" },
            new() { CId = "UM", Name = "Midway Atoll" },
            new() { CId = "UM", Name = "Navassa Island" },
            new() { CId = "UM", Name = "Palmyra Atoll" },
            new() { CId = "UM", Name = "Wake Island" },
            new() { CId = "UY", Name = "Artigas" },
            new() { CId = "UY", Name = "Canelones" },
            new() { CId = "UY", Name = "Cerro Largo" },
            new() { CId = "UY", Name = "Colonia" },
            new() { CId = "UY", Name = "Durazno" },
            new() { CId = "UY", Name = "Flores" },
            new() { CId = "UY", Name = "Florida" },
            new() { CId = "UY", Name = "Lavalleja" },
            new() { CId = "UY", Name = "Maldonado" },
            new() { CId = "UY", Name = "Montevideo" },
            new() { CId = "UY", Name = "Paysandu" },
            new() { CId = "UY", Name = "Rio Negro" },
            new() { CId = "UY", Name = "Rivera" },
            new() { CId = "UY", Name = "Rocha" },
            new() { CId = "UY", Name = "Salto" },
            new() { CId = "UY", Name = "San Jose" },
            new() { CId = "UY", Name = "Soriano" },
            new() { CId = "UY", Name = "Tacuarembo" },
            new() { CId = "UY", Name = "Treinta y Tres" },
            new() { CId = "UZ", Name = "Andijon" },
            new() { CId = "UZ", Name = "Buxoro" },
            new() { CId = "UZ", Name = "Farg'ona" },
            new() { CId = "UZ", Name = "Jizzax" },
            new() { CId = "UZ", Name = "Namangan" },
            new() { CId = "UZ", Name = "Navoiy" },
            new() { CId = "UZ", Name = "Qashqadaryo" },
            new() { CId = "UZ", Name = "Qoraqalpog'iston Republikasi" },
            new() { CId = "UZ", Name = "Samarqand" },
            new() { CId = "UZ", Name = "Sirdaryo" },
            new() { CId = "UZ", Name = "Surxondaryo" },
            new() { CId = "UZ", Name = "Toshkent City" },
            new() { CId = "UZ", Name = "Toshkent Region" },
            new() { CId = "UZ", Name = "Xorazm" },
            new() { CId = "VU", Name = "Malampa" },
            new() { CId = "VU", Name = "Penama" },
            new() { CId = "VU", Name = "Sanma" },
            new() { CId = "VU", Name = "Shefa" },
            new() { CId = "VU", Name = "Tafea" },
            new() { CId = "VU", Name = "Torba" },
            new() { CId = "VE", Name = "Amazonas" },
            new() { CId = "VE", Name = "Anzoategui" },
            new() { CId = "VE", Name = "Apure" },
            new() { CId = "VE", Name = "Aragua" },
            new() { CId = "VE", Name = "Barinas" },
            new() { CId = "VE", Name = "Bolivar" },
            new() { CId = "VE", Name = "Carabobo" },
            new() { CId = "VE", Name = "Cojedes" },
            new() { CId = "VE", Name = "Delta Amacuro" },
            new() { CId = "VE", Name = "Dependencias Federales" },
            new() { CId = "VE", Name = "Distrito Federal" },
            new() { CId = "VE", Name = "Falcon" },
            new() { CId = "VE", Name = "Guarico" },
            new() { CId = "VE", Name = "Lara" },
            new() { CId = "VE", Name = "Merida" },
            new() { CId = "VE", Name = "Miranda" },
            new() { CId = "VE", Name = "Monagas" },
            new() { CId = "VE", Name = "Nueva Esparta" },
            new() { CId = "VE", Name = "Portuguesa" },
            new() { CId = "VE", Name = "Sucre" },
            new() { CId = "VE", Name = "Tachira" },
            new() { CId = "VE", Name = "Trujillo" },
            new() { CId = "VE", Name = "Vargas" },
            new() { CId = "VE", Name = "Yaracuy" },
            new() { CId = "VE", Name = "Zulia" },
            new() { CId = "VN", Name = "An Giang" },
            new() { CId = "VN", Name = "Bac Giang" },
            new() { CId = "VN", Name = "Bac Kan" },
            new() { CId = "VN", Name = "Bac Lieu" },
            new() { CId = "VN", Name = "Bac Ninh" },
            new() { CId = "VN", Name = "Ba Ria-Vung Tau" },
            new() { CId = "VN", Name = "Ben Tre" },
            new() { CId = "VN", Name = "Binh Dinh" },
            new() { CId = "VN", Name = "Binh Duong" },
            new() { CId = "VN", Name = "Binh Phuoc" },
            new() { CId = "VN", Name = "Binh Thuan" },
            new() { CId = "VN", Name = "Ca Mau" },
            new() { CId = "VN", Name = "Can Tho" },
            new() { CId = "VN", Name = "Cao Bang" },
            new() { CId = "VN", Name = "Dak Lak" },
            new() { CId = "VN", Name = "Dak Nong" },
            new() { CId = "VN", Name = "Da Nang" },
            new() { CId = "VN", Name = "Dien Bien" },
            new() { CId = "VN", Name = "Dong Nai" },
            new() { CId = "VN", Name = "Dong Thap" },
            new() { CId = "VN", Name = "Gia Lai" },
            new() { CId = "VN", Name = "Ha Giang" },
            new() { CId = "VN", Name = "Hai Duong" },
            new() { CId = "VN", Name = "Hai Phong" },
            new() { CId = "VN", Name = "Ha Nam" },
            new() { CId = "VN", Name = "Ha Noi" },
            new() { CId = "VN", Name = "Ha Tay" },
            new() { CId = "VN", Name = "Ha Tinh" },
            new() { CId = "VN", Name = "Hoa Binh" },
            new() { CId = "VN", Name = "Ho Chi Minh City" },
            new() { CId = "VN", Name = "Hau Giang" },
            new() { CId = "VN", Name = "Hung Yen" },
            new() { CId = "VI", Name = "Saint Croix" },
            new() { CId = "VI", Name = "Saint John" },
            new() { CId = "VI", Name = "Saint Thomas" },
            new() { CId = "WF", Name = "Alo" },
            new() { CId = "WF", Name = "Sigave" },
            new() { CId = "WF", Name = "Wallis" },
            new() { CId = "YE", Name = "Abyan" },
            new() { CId = "YE", Name = "Adan" },
            new() { CId = "YE", Name = "Amran" },
            new() { CId = "YE", Name = "Al Bayda" },
            new() { CId = "YE", Name = "Ad Dali" },
            new() { CId = "YE", Name = "Dhamar" },
            new() { CId = "YE", Name = "Hadramawt" },
            new() { CId = "YE", Name = "Hajjah" },
            new() { CId = "YE", Name = "Al Hudaydah" },
            new() { CId = "YE", Name = "Ibb" },
            new() { CId = "YE", Name = "Al Jawf" },
            new() { CId = "YE", Name = "Lahij" },
            new() { CId = "YE", Name = "Ma'rib" },
            new() { CId = "YE", Name = "Al Mahrah" },
            new() { CId = "YE", Name = "Al Mahwit" },
            new() { CId = "YE", Name = "Sa'dah" },
            new() { CId = "YE", Name = "San'a" },
            new() { CId = "YE", Name = "Shabwah" },
            new() { CId = "YE", Name = "Ta'izz" },
            new() { CId = "CD", Name = "Bas-Congo" },
            new() { CId = "CD", Name = "Bandundu" },
            new() { CId = "CD", Name = "Equateur" },
            new() { CId = "CD", Name = "Katanga" },
            new() { CId = "CD", Name = "Kasai-Oriental" },
            new() { CId = "CD", Name = "Kinshasa" },
            new() { CId = "CD", Name = "Kasai-Occidental" },
            new() { CId = "CD", Name = "Maniema" },
            new() { CId = "CD", Name = "Nord-Kivu" },
            new() { CId = "CD", Name = "Orientale" },
            new() { CId = "CD", Name = "Sud-Kivu" },
            new() { CId = "ZM", Name = "Central" },
            new() { CId = "ZM", Name = "Copperbelt" },
            new() { CId = "ZM", Name = "Eastern" },
            new() { CId = "ZM", Name = "Luapula" },
            new() { CId = "ZM", Name = "Lusaka" },
            new() { CId = "ZM", Name = "Northern" },
            new() { CId = "ZM", Name = "North-Western" },
            new() { CId = "ZM", Name = "Southern" },
            new() { CId = "ZM", Name = "Western" },
            new() { CId = "ZW", Name = "Bulawayo" },
            new() { CId = "ZW", Name = "Harare" },
            new() { CId = "ZW", Name = "Manicaland" },
            new() { CId = "ZW", Name = "Mashonaland Central" },
            new() { CId = "ZW", Name = "Mashonaland East" },
            new() { CId = "ZW", Name = "Mashonaland West" },
            new() { CId = "ZW", Name = "Masvingo" },
            new() { CId = "ZW", Name = "Matabeleland North" },
            new() { CId = "ZW", Name = "Matabeleland South" },
            new() { CId = "ZW", Name = "Midlands" },
            new() { CId = "IT", Name = "Agrigento" },
            new() { CId = "IT", Name = "Alessandria" },
            new() { CId = "IT", Name = "Ancona" },
            new() { CId = "IT", Name = "Aosta" },
            new() { CId = "IT", Name = "Arezzo" },
            new() { CId = "IT", Name = "Ascoli Piceno" },
            new() { CId = "IT", Name = "Asti" },
            new() { CId = "IT", Name = "Avellino" },
            new() { CId = "IT", Name = "Bari" },
            new() { CId = "IT", Name = "Belluno" },
            new() { CId = "IT", Name = "Benevento" },
            new() { CId = "IT", Name = "Bergamo" },
            new() { CId = "IT", Name = "Biella" },
            new() { CId = "IT", Name = "Bologna" },
            new() { CId = "IT", Name = "Bolzano" },
            new() { CId = "IT", Name = "Brescia" },
            new() { CId = "IT", Name = "Brindisi" },
            new() { CId = "IT", Name = "Cagliari" },
            new() { CId = "IT", Name = "Caltanissetta" },
            new() { CId = "IT", Name = "Campobasso" },
            new() { CId = "IT", Name = "Caserta" },
            new() { CId = "IT", Name = "Catania" },
            new() { CId = "IT", Name = "Catanzaro" },
            new() { CId = "IT", Name = "Chieti" },
            new() { CId = "IT", Name = "Como" },
            new() { CId = "IT", Name = "Cosenza" },
            new() { CId = "IT", Name = "Cremona" },
            new() { CId = "IT", Name = "Crotone" },
            new() { CId = "IT", Name = "Cuneo" },
            new() { CId = "IT", Name = "Enna" },
            new() { CId = "IT", Name = "Ferrara" },
            new() { CId = "IT", Name = "Firenze" },
            new() { CId = "IT", Name = "Foggia" },
            new() { CId = "IT", Name = "Forli-Cesena" },
            new() { CId = "IT", Name = "Frosinone" },
            new() { CId = "IT", Name = "Genova" },
            new() { CId = "IT", Name = "Gorizia" },
            new() { CId = "IT", Name = "Grosseto" },
            new() { CId = "IT", Name = "Imperia" },
            new() { CId = "IT", Name = "Isernia" },
            new() { CId = "IT", Name = "L&#39;Aquila" },
            new() { CId = "IT", Name = "La Spezia" },
            new() { CId = "IT", Name = "Latina" },
            new() { CId = "IT", Name = "Lecce" },
            new() { CId = "IT", Name = "Lecco" },
            new() { CId = "IT", Name = "Livorno" },
            new() { CId = "IT", Name = "Lodi" },
            new() { CId = "IT", Name = "Lucca" },
            new() { CId = "IT", Name = "Macerata" },
            new() { CId = "IT", Name = "Mantova" },
            new() { CId = "IT", Name = "Massa-Carrara" },
            new() { CId = "IT", Name = "Matera" },
            new() { CId = "IT", Name = "Messina" },
            new() { CId = "IT", Name = "Milano" },
            new() { CId = "IT", Name = "Modena" },
            new() { CId = "IT", Name = "Napoli" },
            new() { CId = "IT", Name = "Novara" },
            new() { CId = "IT", Name = "Nuoro" },
            new() { CId = "IT", Name = "Oristano" },
            new() { CId = "IT", Name = "Padova" },
            new() { CId = "IT", Name = "Palermo" },
            new() { CId = "IT", Name = "Parma" },
            new() { CId = "IT", Name = "Pavia" },
            new() { CId = "IT", Name = "Perugia" },
            new() { CId = "IT", Name = "Pesaro e Urbino" },
            new() { CId = "IT", Name = "Pescara" },
            new() { CId = "IT", Name = "Piacenza" },
            new() { CId = "IT", Name = "Pisa" },
            new() { CId = "IT", Name = "Pistoia" },
            new() { CId = "IT", Name = "Pordenone" },
            new() { CId = "IT", Name = "Potenza" },
            new() { CId = "IT", Name = "Prato" },
            new() { CId = "IT", Name = "Ragusa" },
            new() { CId = "IT", Name = "Ravenna" },
            new() { CId = "IT", Name = "Reggio Calabria" },
            new() { CId = "IT", Name = "Reggio Emilia" },
            new() { CId = "IT", Name = "Rieti" },
            new() { CId = "IT", Name = "Rimini" },
            new() { CId = "IT", Name = "Roma" },
            new() { CId = "IT", Name = "Rovigo" },
            new() { CId = "IT", Name = "Salerno" },
            new() { CId = "IT", Name = "Sassari" },
            new() { CId = "IT", Name = "Savona" },
            new() { CId = "IT", Name = "Siena" },
            new() { CId = "IT", Name = "Siracusa" },
            new() { CId = "IT", Name = "Sondrio" },
            new() { CId = "IT", Name = "Taranto" },
            new() { CId = "IT", Name = "Teramo" },
            new() { CId = "IT", Name = "Terni" },
            new() { CId = "IT", Name = "Torino" },
            new() { CId = "IT", Name = "Trapani" },
            new() { CId = "IT", Name = "Trento" },
            new() { CId = "IT", Name = "Treviso" },
            new() { CId = "IT", Name = "Trieste" },
            new() { CId = "IT", Name = "Udine" },
            new() { CId = "IT", Name = "Varese" },
            new() { CId = "IT", Name = "Venezia" },
            new() { CId = "IT", Name = "Verbano-Cusio-Ossola" },
            new() { CId = "IT", Name = "Vercelli" },
            new() { CId = "IT", Name = "Verona" },
            new() { CId = "IT", Name = "Vibo Valentia" },
            new() { CId = "IT", Name = "Vicenza" },
            new() { CId = "IT", Name = "Viterbo" },
            new() { CId = "GB", Name = "County Antrim" },
            new() { CId = "GB", Name = "County Armagh" },
            new() { CId = "GB", Name = "County Down" },
            new() { CId = "GB", Name = "County Fermanagh" },
            new() { CId = "GB", Name = "County Londonderry" },
            new() { CId = "GB", Name = "County Tyrone" },
            new() { CId = "GB", Name = "Cumbria" },
            new() { CId = "SI", Name = "Pomurska" },
            new() { CId = "SI", Name = "Podravska" },
            new() { CId = "SI", Name = "Koroška" },
            new() { CId = "SI", Name = "Savinjska" },
            new() { CId = "SI", Name = "Zasavska" },
            new() { CId = "SI", Name = "Spodnjeposavska" },
            new() { CId = "SI", Name = "Jugovzhodna Slovenija" },
            new() { CId = "SI", Name = "Osrednjeslovenska" },
            new() { CId = "SI", Name = "Gorenjska" },
            new() { CId = "SI", Name = "Notranjsko-kraška" },
            new() { CId = "SI", Name = "Goriška" },
            new() { CId = "SI", Name = "Obalno-kraška" },
            new() { CId = "BG", Name = "Ruse" },
            new() { CId = "IR", Name = "Alborz" },
            new() { CId = "BE", Name = "Brussels-Capital Region" },
            new() { CId = "MX", Name = "Aguascalientes" },
            new() { CId = "ME", Name = "Andrijevica" },
            new() { CId = "ME", Name = "Bar" },
            new() { CId = "ME", Name = "Berane" },
            new() { CId = "ME", Name = "Bijelo Polje" },
            new() { CId = "ME", Name = "Budva" },
            new() { CId = "ME", Name = "Cetinje" },
            new() { CId = "ME", Name = "Danilovgrad" },
            new() { CId = "ME", Name = "Herceg-Novi" },
            new() { CId = "ME", Name = "Kolašin" },
            new() { CId = "ME", Name = "Kotor" },
            new() { CId = "ME", Name = "Mojkovac" },
            new() { CId = "ME", Name = "Nikšić" },
            new() { CId = "ME", Name = "Plav" },
            new() { CId = "ME", Name = "Pljevlja" },
            new() { CId = "ME", Name = "Plužine" },
            new() { CId = "ME", Name = "Podgorica" },
            new() { CId = "ME", Name = "Rožaje" },
            new() { CId = "ME", Name = "Šavnik" },
            new() { CId = "ME", Name = "Tivat" },
            new() { CId = "ME", Name = "Ulcinj" },
            new() { CId = "ME", Name = "Žabljak" },
            new() { CId = "RS", Name = "Belgrade" },
            new() { CId = "RS", Name = "North Bačka" },
            new() { CId = "RS", Name = "Central Banat" },
            new() { CId = "RS", Name = "North Banat" },
            new() { CId = "RS", Name = "South Banat" },
            new() { CId = "RS", Name = "West Bačka" },
            new() { CId = "RS", Name = "South Bačka" },
            new() { CId = "RS", Name = "Srem" },
            new() { CId = "RS", Name = "Mačva" },
            new() { CId = "RS", Name = "Kolubara" },
            new() { CId = "RS", Name = "Podunavlje" },
            new() { CId = "RS", Name = "Braničevo" },
            new() { CId = "RS", Name = "Šumadija" },
            new() { CId = "RS", Name = "Pomoravlje" },
            new() { CId = "RS", Name = "Bor" },
            new() { CId = "RS", Name = "Zaječar" },
            new() { CId = "RS", Name = "Zlatibor" },
            new() { CId = "RS", Name = "Moravica" },
            new() { CId = "RS", Name = "Raška" },
            new() { CId = "RS", Name = "Rasina" },
            new() { CId = "RS", Name = "Nišava" },
            new() { CId = "RS", Name = "Toplica" },
            new() { CId = "RS", Name = "Pirot" },
            new() { CId = "RS", Name = "Jablanica" },
            new() { CId = "RS", Name = "Pčinja" },
            new() { CId = "BQ", Name = "Bonaire" },
            new() { CId = "BQ", Name = "Saba" },
            new() { CId = "BQ", Name = "Sint Eustatius" },
            new() { CId = "SS", Name = "Central Equatoria" },
            new() { CId = "SS", Name = "Eastern Equatoria" },
            new() { CId = "SS", Name = "Jonglei" },
            new() { CId = "SS", Name = "Lakes" },
            new() { CId = "SS", Name = "Northern Bahr el-Ghazal" },
            new() { CId = "SS", Name = "Unity" },
            new() { CId = "SS", Name = "Upper Nile" },
            new() { CId = "SS", Name = "Warrap" },
            new() { CId = "SS", Name = "Western Bahr el-Ghazal" },
            new() { CId = "SS", Name = "Western Equatoria" },
            new() { CId = "MY", Name = "Putrajaya" },
            new() { CId = "LV", Name = "Ainaži, Salacgrīvas novads" },
            new() { CId = "LV", Name = "Aizkraukle, Aizkraukles novads" },
            new() { CId = "LV", Name = "Aizkraukles novads" },
            new() { CId = "LV", Name = "Aizpute, Aizputes novads" },
            new() { CId = "LV", Name = "Aizputes novads" },
            new() { CId = "LV", Name = "Aknīste, Aknīstes novads" },
            new() { CId = "LV", Name = "Aknīstes novads" },
            new() { CId = "LV", Name = "Aloja, Alojas novads" },
            new() { CId = "LV", Name = "Alojas novads" },
            new() { CId = "LV", Name = "Alsungas novads" },
            new() { CId = "LV", Name = "Alūksne, Alūksnes novads" },
            new() { CId = "LV", Name = "Alūksnes novads" },
            new() { CId = "LV", Name = "Amatas novads" },
            new() { CId = "LV", Name = "Ape, Apes novads" },
            new() { CId = "LV", Name = "Apes novads" },
            new() { CId = "LV", Name = "Auce, Auces novads" },
            new() { CId = "LV", Name = "Auces novads" },
            new() { CId = "LV", Name = "Ādažu novads" },
            new() { CId = "LV", Name = "Babītes novads" },
            new() { CId = "LV", Name = "Baldone, Baldones novads" },
            new() { CId = "LV", Name = "Baldones novads" },
            new() { CId = "LV", Name = "Baloži, Ķekavas novads" },
            new() { CId = "LV", Name = "Baltinavas novads" },
            new() { CId = "LV", Name = "Balvi, Balvu novads" },
            new() { CId = "LV", Name = "Balvu novads" },
            new() { CId = "LV", Name = "Bauska, Bauskas novads" },
            new() { CId = "LV", Name = "Bauskas novads" },
            new() { CId = "LV", Name = "Beverīnas novads" },
            new() { CId = "LV", Name = "Brocēni, Brocēnu novads" },
            new() { CId = "LV", Name = "Brocēnu novads" },
            new() { CId = "LV", Name = "Burtnieku novads" },
            new() { CId = "LV", Name = "Carnikavas novads" },
            new() { CId = "LV", Name = "Cesvaine, Cesvaines novads" },
            new() { CId = "LV", Name = "Cesvaines novads" },
            new() { CId = "LV", Name = "Cēsis, Cēsu novads" },
            new() { CId = "LV", Name = "Cēsu novads" },
            new() { CId = "LV", Name = "Ciblas novads" },
            new() { CId = "LV", Name = "Dagda, Dagdas novads" },
            new() { CId = "LV", Name = "Dagdas novads" },
            new() { CId = "LV", Name = "Daugavpils" },
            new() { CId = "LV", Name = "Daugavpils novads" },
            new() { CId = "LV", Name = "Dobele, Dobeles novads" },
            new() { CId = "LV", Name = "Dobeles novads" },
            new() { CId = "LV", Name = "Dundagas novads" },
            new() { CId = "LV", Name = "Durbe, Durbes novads" },
            new() { CId = "LV", Name = "Durbes novads" },
            new() { CId = "LV", Name = "Engures novads" },
            new() { CId = "LV", Name = "Ērgļu novads" },
            new() { CId = "LV", Name = "Garkalnes novads" },
            new() { CId = "LV", Name = "Grobiņa, Grobiņas novads" },
            new() { CId = "LV", Name = "Grobiņas novads" },
            new() { CId = "LV", Name = "Gulbene, Gulbenes novads" },
            new() { CId = "LV", Name = "Gulbenes novads" },
            new() { CId = "LV", Name = "Iecavas novads" },
            new() { CId = "LV", Name = "Ikšķile, Ikšķiles novads" },
            new() { CId = "LV", Name = "Ikšķiles novads" },
            new() { CId = "LV", Name = "Ilūkste, Ilūkstes novads" },
            new() { CId = "LV", Name = "Ilūkstes novads" },
            new() { CId = "LV", Name = "Inčukalna novads" },
            new() { CId = "LV", Name = "Jaunjelgava, Jaunjelgavas novads" },
            new() { CId = "LV", Name = "Jaunjelgavas novads" },
            new() { CId = "LV", Name = "Jaunpiebalgas novads" },
            new() { CId = "LV", Name = "Jaunpils novads" },
            new() { CId = "LV", Name = "Jelgava" },
            new() { CId = "LV", Name = "Jelgavas novads" },
            new() { CId = "LV", Name = "Jēkabpils" },
            new() { CId = "LV", Name = "Jēkabpils novads" },
            new() { CId = "LV", Name = "Jūrmala" },
            new() { CId = "LV", Name = "Kalnciems, Jelgavas novads" },
            new() { CId = "LV", Name = "Kandava, Kandavas novads" },
            new() { CId = "LV", Name = "Kandavas novads" },
            new() { CId = "LV", Name = "Kārsava, Kārsavas novads" },
            new() { CId = "LV", Name = "Kārsavas novads" },
            new() { CId = "LV", Name = "Kocēnu novads ,bij. Valmieras)" },
            new() { CId = "LV", Name = "Kokneses novads" },
            new() { CId = "LV", Name = "Krāslava, Krāslavas novads" },
            new() { CId = "LV", Name = "Krāslavas novads" },
            new() { CId = "LV", Name = "Krimuldas novads" },
            new() { CId = "LV", Name = "Krustpils novads" },
            new() { CId = "LV", Name = "Kuldīga, Kuldīgas novads" },
            new() { CId = "LV", Name = "Kuldīgas novads" },
            new() { CId = "LV", Name = "Ķeguma novads" },
            new() { CId = "LV", Name = "Ķegums, Ķeguma novads" },
            new() { CId = "LV", Name = "Ķekavas novads" },
            new() { CId = "LV", Name = "Lielvārde, Lielvārdes novads" },
            new() { CId = "LV", Name = "Lielvārdes novads" },
            new() { CId = "LV", Name = "Liepāja" },
            new() { CId = "LV", Name = "Limbaži, Limbažu novads" },
            new() { CId = "LV", Name = "Limbažu novads" },
            new() { CId = "LV", Name = "Līgatne, Līgatnes novads" },
            new() { CId = "LV", Name = "Līgatnes novads" },
            new() { CId = "LV", Name = "Līvāni, Līvānu novads" },
            new() { CId = "LV", Name = "Līvānu novads" },
            new() { CId = "LV", Name = "Lubāna, Lubānas novads" },
            new() { CId = "LV", Name = "Lubānas novads" },
            new() { CId = "LV", Name = "Ludza, Ludzas novads" },
            new() { CId = "LV", Name = "Ludzas novads" },
            new() { CId = "LV", Name = "Madona, Madonas novads" },
            new() { CId = "LV", Name = "Madonas novads" },
            new() { CId = "LV", Name = "Mazsalaca, Mazsalacas novads" },
            new() { CId = "LV", Name = "Mazsalacas novads" },
            new() { CId = "LV", Name = "Mālpils novads" },
            new() { CId = "LV", Name = "Mārupes novads" },
            new() { CId = "LV", Name = "Mērsraga novads" },
            new() { CId = "LV", Name = "Naukšēnu novads" },
            new() { CId = "LV", Name = "Neretas novads" },
            new() { CId = "LV", Name = "Nīcas novads" },
            new() { CId = "LV", Name = "Ogre, Ogres novads" },
            new() { CId = "LV", Name = "Ogres novads" },
            new() { CId = "LV", Name = "Olaine, Olaines novads" },
            new() { CId = "LV", Name = "Olaines novads" },
            new() { CId = "LV", Name = "Ozolnieku novads" },
            new() { CId = "LV", Name = "Pārgaujas novads" },
            new() { CId = "LV", Name = "Pāvilosta, Pāvilostas novads" },
            new() { CId = "LV", Name = "Pāvilostas novads" },
            new() { CId = "LV", Name = "Piltene, Ventspils novads" },
            new() { CId = "LV", Name = "Pļaviņas, Pļaviņu novads" },
            new() { CId = "LV", Name = "Pļaviņu novads" },
            new() { CId = "LV", Name = "Preiļi, Preiļu novads" },
            new() { CId = "LV", Name = "Preiļu novads" },
            new() { CId = "LV", Name = "Priekule, Priekules novads" },
            new() { CId = "LV", Name = "Priekules novads" },
            new() { CId = "LV", Name = "Priekuļu novads" },
            new() { CId = "LV", Name = "Raunas novads" },
            new() { CId = "LV", Name = "Rēzekne" },
            new() { CId = "LV", Name = "Rēzeknes novads" },
            new() { CId = "LV", Name = "Riebiņu novads" },
            new() { CId = "LV", Name = "Rīga" },
            new() { CId = "LV", Name = "Rojas novads" },
            new() { CId = "LV", Name = "Ropažu novads" },
            new() { CId = "LV", Name = "Rucavas novads" },
            new() { CId = "LV", Name = "Rugāju novads" },
            new() { CId = "LV", Name = "Rundāles novads" },
            new() { CId = "LV", Name = "Rūjiena, Rūjienas novads" },
            new() { CId = "LV", Name = "Rūjienas novads" },
            new() { CId = "LV", Name = "Sabile, Talsu novads" },
            new() { CId = "LV", Name = "Salacgrīva, Salacgrīvas novads" },
            new() { CId = "LV", Name = "Salacgrīvas novads" },
            new() { CId = "LV", Name = "Salas novads" },
            new() { CId = "LV", Name = "Salaspils novads" },
            new() { CId = "LV", Name = "Salaspils, Salaspils novads" },
            new() { CId = "LV", Name = "Saldus novads" },
            new() { CId = "LV", Name = "Saldus, Saldus novads" },
            new() { CId = "LV", Name = "Saulkrasti, Saulkrastu novads" },
            new() { CId = "LV", Name = "Saulkrastu novads" },
            new() { CId = "LV", Name = "Seda, Strenču novads" },
            new() { CId = "LV", Name = "Sējas novads" },
            new() { CId = "LV", Name = "Sigulda, Siguldas novads" },
            new() { CId = "LV", Name = "Siguldas novads" },
            new() { CId = "LV", Name = "Skrīveru novads" },
            new() { CId = "LV", Name = "Skrunda, Skrundas novads" },
            new() { CId = "LV", Name = "Skrundas novads" },
            new() { CId = "LV", Name = "Smiltene, Smiltenes novads" },
            new() { CId = "LV", Name = "Smiltenes novads" },
            new() { CId = "LV", Name = "Staicele, Alojas novads" },
            new() { CId = "LV", Name = "Stende, Talsu novads" },
            new() { CId = "LV", Name = "Stopiņu novads" },
            new() { CId = "LV", Name = "Strenči, Strenču novads" },
            new() { CId = "LV", Name = "Strenču novads" },
            new() { CId = "LV", Name = "Subate, Ilūkstes novads" },
            new() { CId = "LV", Name = "Talsi, Talsu novads" },
            new() { CId = "LV", Name = "Talsu novads" },
            new() { CId = "LV", Name = "Tērvetes novads" },
            new() { CId = "LV", Name = "Tukuma novads" },
            new() { CId = "LV", Name = "Tukums, Tukuma novads" },
            new() { CId = "LV", Name = "Vaiņodes novads" },
            new() { CId = "LV", Name = "Valdemārpils, Talsu novads" },
            new() { CId = "LV", Name = "Valka, Valkas novads" },
            new() { CId = "LV", Name = "Valkas novads" },
            new() { CId = "LV", Name = "Valmiera" },
            new() { CId = "LV", Name = "Vangaži, Inčukalna novads" },
            new() { CId = "LV", Name = "Varakļāni, Varakļānu novads" },
            new() { CId = "LV", Name = "Varakļānu novads" },
            new() { CId = "LV", Name = "Vārkavas novads" },
            new() { CId = "LV", Name = "Vecpiebalgas novads" },
            new() { CId = "LV", Name = "Vecumnieku novads" },
            new() { CId = "LV", Name = "Ventspils" },
            new() { CId = "LV", Name = "Ventspils novads" },
            new() { CId = "LV", Name = "Viesīte, Viesītes novads" },
            new() { CId = "LV", Name = "Viesītes novads" },
            new() { CId = "LV", Name = "Viļaka, Viļakas novads" },
            new() { CId = "LV", Name = "Viļakas novads" },
            new() { CId = "LV", Name = "Viļāni, Viļānu novads" },
            new() { CId = "LV", Name = "Viļānu novads" },
            new() { CId = "LV", Name = "Zilupe, Zilupes novads" },
            new() { CId = "LV", Name = "Zilupes novads" },
            new() { CId = "CL", Name = "Arica y Parinacota" },
            new() { CId = "CL", Name = "Los Rios" },
            new() { CId = "UA", Name = "Kharkivs'ka Oblast'" },
            new() { CId = "LB", Name = "Beirut" },
            new() { CId = "LB", Name = "Bekaa" },
            new() { CId = "LB", Name = "Mount Lebanon" },
            new() { CId = "LB", Name = "Nabatieh" },
            new() { CId = "LB", Name = "North" },
            new() { CId = "LB", Name = "South" },
            new() { CId = "IN", Name = "Telangana" },
            new() { CId = "ID", Name = "Papua Barat" },
            new() { CId = "ID", Name = "Sulawesi Barat" },
            new() { CId = "ID", Name = "Kepulauan Riau" },
            new() { CId = "IT", Name = "Barletta-Andria-Trani" },
            new() { CId = "IT", Name = "Fermo" },
            new() { CId = "IT", Name = "Monza Brianza" },
            new() { CId = "AS", Name = "Los Angeles" },
            new() { CId = "SG", Name = "Central Region" },
            new() { CId = "SG", Name = "East Region" },
            new() { CId = "SG", Name = "West Region" },
            new() { CId = "SG", Name = "South Region" },
            new() { CId = "SG", Name = "North Region" },
        };
        XTrace.WriteLine("Inserting {0} records...", list.Count);
        foreach (var item in list)
        {
            item.Level = 0;
            item.ParentCode = "0";
            item.AreaCode = (item.CId + item.Name).MD5();
            //item.Insert();
        }
        list.Insert();

        await Task.CompletedTask.ConfigureAwait(false);
    }
}
