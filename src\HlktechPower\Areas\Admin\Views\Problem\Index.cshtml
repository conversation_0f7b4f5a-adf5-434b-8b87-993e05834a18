﻿@using HlktechPower.Entity
<style asp-location="true">
    .opt_for {
        color: #aaa !important;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("问题管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
                @* <li><a href="@Url.Action("Add")"><span>@T("添加")</span></a></li> *@
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("关键字")</dt>
                <dd><input type="text" value="@Model.name" name="name" class="txt"></dd>
                <dt>@T("问题类型")</dt>
                <dd>
                    <select name="problemType">
                        <option value="-1">@T("请选择")</option>
                        <!option value="0" @(Model.problemType == 0 ? "selected" : "")>@T("网站问题")</!option>
                        <!option value="1" @(Model.problemType == 1 ? "selected" : "")>@T("产品问题")</!option>
                        <!option value="2" @(Model.problemType == 2 ? "selected" : "")>@T("技术问题")</!option>
                        <!option value="3" @(Model.problemType == 3 ? "selected" : "")>@T("其他问题")</!option>
                    </select>
                </dd>
                <dt>@T("回复状态")</dt>
                <dd>
                    <select name="status">
                        <option value="-1">@T("请选择")</option>
                        <!option value="0" @(Model.status == 0 ? "selected" : "")>@T("未回复")</!option>
                        <!option value="1" @(Model.status == 1 ? "selected" : "")>@T("已回复")</!option>
                    </select>
                </dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")">
            </div>
        </div>
    </form>
    <table class="ds-default-table">
        <thead>
            <tr>
                <th class="w24"></th>
                <th>@T("编号")</th>
                <th>@T("问题内容")</th>
                <th></th>
                <th></th>
                <th></th>
                <th>@T("问题类型")</th>
                <th>@T("回复状态")</th>
                <th>@T("显示状态")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (Problem item in Model.list)
            {
                <tr id="ds_row_11" style="background: rgb(255, 255, 255);">
                    <td><input type="checkbox" class="checkitem" name="nav_id[]" value="@item.Id"></td>
                    <td>@item.Id</td>
                    <td>@item.Content</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    @if (item.ProblemType == 0)
                    {
                        <td>@T("网站问题")</td>
                    }
                    else if (item.ProblemType == 1)
                    {
                        <td>@T("产品问题")</td>
                    }
                    else if (item.ProblemType == 2)
                    {
                        <td>@T("技术问题")</td>
                    }
                    else
                    {
                        <td>@T("其他问题")</td>
                    }

                    <td>@(item.Answer.IsNullOrWhiteSpace() ? "未回复" : "已回复")</td>
                    <td>@(item.Show ? "显示" : "隐藏")</td>
                    <td>
                        @if (item.Show)
                        {
                            <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("UpdateShow", new { Id = item.Id })','@T("您确定要隐藏吗")?',@item.Id)" class="dsui-btn-edit"><i class="iconfont"></i>@T("隐藏")</a>
                        }
                        else
                        {
                            <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("UpdateShow", new { Id = item.Id })','@T("您确定要显示吗")?',@item.Id)" class="dsui-btn-edit"><i class="iconfont"></i>@T("显示")</a>
                        }
                        <a href="@Url.Action("Update",new { Id=item.Id})" class="dsui-btn-edit"><i class="iconfont"></i>@T("回复")</a>
                        <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("Delete", new { Ids = item.Id })','@T("您确定要删除吗")?',@item.Id)" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>
                    </td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>
<script asp-location="Footer">
    function submit_delete(ids_str) {
        _uri = "@Url.Action("Delete")?Ids=" + ids_str;
        dsLayerConfirm(_uri, '您确定要删除吗?');
    }
</script>
