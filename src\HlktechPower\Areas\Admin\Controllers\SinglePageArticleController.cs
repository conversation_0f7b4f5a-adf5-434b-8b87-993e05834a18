﻿

using DH;
using DH.Core.Domain.Localization;
using DH.Entity;
using HlktechPower.Entity;
using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Helpers;
using Pek.IO;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Webs;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;


namespace HlktechPower.Areas.Admin.Controllers;

/// <summary>单页文章管理</summary>
[DisplayName("单页文章管理")]
[Description("用于单页文章分类的管理")]
[AdminArea]
[DHMenu(95, ParentMenuName = "Sites", ParentMenuDisplayName = "网站", ParentMenuUrl = "~/{area}/Sites", ParentMenuOrder = 85, ParentVisible = true, CurrentMenuUrl = "~/{area}/SinglePageArticle", CurrentMenuName = "SinglePageArticle", CurrentIcon = "&#xe652;", LastUpdate = "20250610")]
public class SinglePageArticleController : PekCubeAdminControllerX
{
    /// <summary>
    /// 单页文章列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("单页文章列表")]
    public IActionResult Index(string name, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = SingleArticle._.Id,
            Desc = true,
        };
        var list = SingleArticle.Searchs(name.SafeString().Trim(), pages);
        viewModel.list = list;
        viewModel.page = page;
        viewModel.name = name;
        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name } });
        return View(viewModel);
    }

    /// <summary>
    /// 添加单页文章管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("添加单页文章管理")]
    public IActionResult AddArticle()
    {
        dynamic viewModel = new ExpandoObject();
        ViewBag.FileList = UploadInfo.FindAllByItemIdAndFileType(0, 1);
        var Sort = SingleArticle.FindMax("Sort");
        ViewBag.Sort = Sort + 1;
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View(viewModel);
    }

    /// <summary>
    /// 新增提交
    /// </summary>
    /// <param name="article_title">文章标题</param>
    /// <param name="default_user_portrait">文章主图</param>
    /// <param name="article_url">链接</param>
    /// <param name="article_show">文章是否显示</param>
    /// <param name="article_sort">文章排序</param>
    /// <param name="article_content">文章内容</param>
    /// <param name="article_mobileContent">移动端文章内容</param>
    /// <param name="article_code">调用别名</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("新增提交")]
    [HttpPost]
    public IActionResult CreatArticle(IFormFile default_user_portrait, string article_title, string article_url, int article_show, int article_sort, string article_content, string article_mobileContent, String article_code)
    {
        if (article_title.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("标题名称不能为空") });
        }
        var ex = SingleArticle.FindByName(article_title.SafeString().Trim());
        if (ex != null)
        {
            return Prompt(new PromptModel { Message = GetResource("名称已存在") });
        }
        using (var tran1 = SingleArticle.Meta.CreateTrans())
        {

            var Model = new SingleArticle();
            Model.Name = article_title.SafeString().Trim();
            Model.Url = article_url.SafeString().Trim();
            Model.Show = article_show == 1 ? true : false;
            Model.Sort = article_sort;
            Model.Code = article_code;
            Model.Content = article_content;
            Model.MobileContent = article_mobileContent;
            Model.Insert();
            if (default_user_portrait != null)
            {
                var bytes = default_user_portrait.OpenReadStream().ReadBytes(default_user_portrait.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }

                var filename = $"{"SingleArticle" + Model.Id}{Path.GetExtension(default_user_portrait.FileName)}";
                var filepath = FileUtil.JoinPath(DHSetting.Current.UploadPath, $"SingleArticle/{filename}");
                var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
                saveFileName.EnsureDirectory();
                default_user_portrait.SaveAs(saveFileName);
                Model.Pic = filepath.Replace("\\", "/");
                Model.Update();
            }


            var file_id = GetRequest("file_id[]");
            if (file_id.IsNotNullAndWhiteSpace())
            {
                var list = UploadInfo.FindByIds(file_id.Trim(','));
                //修改没有标识的图片
                foreach (var item in list)
                {
                    item.ItemId = Model.Id;
                }
                list.Save();
            }

            var localizationSettings = LocalizationSettings.Current;

            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var filea = Request.Form.Files;
                var list = filea.Count();

                var lanlist = SingleArticleLan.FindAllBySId(Model.Id);
                foreach (var item in Languagelist)
                {
                    var mobileLan = lanlist.Find(x => x.LId == item.Id);
                    if (mobileLan == null)
                    {
                        mobileLan = new SingleArticleLan();
                    }
                    var Name = $"[{item.Id}].default_user_portrait";
                    mobileLan.Name = GetRequest($"[{item.Id}].article_title").SafeString().Trim();
                    mobileLan.Content = GetRequest($"article_content_{item.Id}").SafeString().Trim();
                    mobileLan.MobileContent = GetRequest($"article_mobileContent_{item.Id}").SafeString().Trim();
                    mobileLan.SId = Model.Id;
                    mobileLan.LId = item.Id;
                    mobileLan.Save();

                    var file = filea.Where(x => x.Name == Name.SafeString().Trim()).FirstOrDefault();
                    if (file != null)
                    {
                        var bytes = file.OpenReadStream().ReadBytes(file.Length);
                        if (!bytes.IsImageFile())
                        {
                            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                        }
                        var filename = $"{"SingleArticleLan" + mobileLan.Id}{Path.GetExtension(file.FileName)}";
                        var filepath = FileUtil.JoinPath(DHSetting.Current.UploadPath, $"SingleArticleLan/{filename}");
                        var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
                        saveFileName.EnsureDirectory();
                        file.SaveAs(saveFileName);
                        mobileLan.Pic = filepath.Replace("\\", "/");
                        mobileLan.Update();
                    }
                }
            }
            tran1.Commit();
        }
        SingleArticle.Meta.Cache.Clear("");//清除缓存
        SingleArticleLan.Meta.Cache.Clear("");//清除缓存
        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 单页文章管理修改页面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("单页文章管理修改页面")]
    public IActionResult EditArticle(Int32 Id)
    {
        var Model = SingleArticle.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除"));

        }
        ViewBag.FileList = UploadInfo.FindAllByItemIdAndFileType(Id, 2);
        dynamic viewModel = new ExpandoObject();
        viewModel.Model = Model;
        ViewBag.Images = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), Model.Pic.IsNotNullAndWhiteSpace() ? Model.Pic : "");
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        return View(viewModel);
    }

    /// <summary>
    /// 文章管理修改接口
    /// </summary>
    /// <param name="Id">文章编号</param>
    /// <param name="article_title">文章标题</param>
    /// <param name="default_user_portrait">文章主图</param>
    /// <param name="article_url">链接</param>
    /// <param name="article_show">文章是否显示</param>
    /// <param name="article_sort">文章排序</param>
    /// <param name="article_content">文章内容</param>
    /// <param name="article_code">调用别名</param>
    /// <param name="article_mobileContent">移动端文章内容</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("单页文章管理修改接口")]
    public IActionResult EditArticle(Int32 Id, IFormFile default_user_portrait, string article_title, string article_url, int article_show, int article_sort, string article_content, string article_mobileContent, String article_code)
    {
        if (article_title.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("标题名称不能为空") });
        }
        var ex = SingleArticle.FindByName(article_title.SafeString().Trim());
        if (ex != null && ex.Id != Id)
        {
            return Prompt(new PromptModel { Message = GetResource("名称已存在") });
        }

        using (var tran1 = SingleArticle.Meta.CreateTrans())
        {
            var Model = SingleArticle.FindById(Id);
            if (Model == null)
            {
                return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除") });
            }
            Model.Name = article_title.SafeString().Trim();
            Model.Url = article_url;
            Model.Show = article_show == 1 ? true : false;
            Model.Sort = article_sort;
            Model.Content = article_content;
            Model.MobileContent = article_mobileContent;
            Model.Code = article_code;

            if (default_user_portrait != null)
            {
                var bytes = default_user_portrait.OpenReadStream().ReadBytes(default_user_portrait.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }

                var filename = $"{"SingleArticle" + Model.Id}{Path.GetExtension(default_user_portrait.FileName)}";
                var filepath = FileUtil.JoinPath(DHSetting.Current.UploadPath, $"SingleArticle/{filename}");
                var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
                saveFileName.EnsureDirectory();
                default_user_portrait.SaveAs(saveFileName);
                Model.Pic = filepath.Replace("\\", "/");
            }
            Model.Update();
            var file_id = GetRequest("file_id[]");
            if (file_id.IsNotNullAndWhiteSpace())
            {
                var list = UploadInfo.FindByIds(file_id.Trim(','));
                //修改没有标识的图片
                foreach (var item in list)
                {
                    item.ItemId = Model.Id;
                }
                list.Save();
            }

            var localizationSettings = LocalizationSettings.Current;

            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var filea = Request.Form.Files;
                var list = filea.Count();

                var lanlist = SingleArticleLan.FindAllBySId(Model.Id);
                foreach (var item in Languagelist)
                {
                    var aaaa = lanlist.Find(x => x.LId == item.Id);
                    if (aaaa == null)
                    {
                        aaaa = new SingleArticleLan();
                    }
                    var Name = $"[{item.Id}].default_user_portrait";
                    aaaa.Name = GetRequest($"[{item.Id}].article_title").SafeString().Trim();
                    aaaa.Content = GetRequest($"article_content_{item.Id}").SafeString().Trim();
                    aaaa.MobileContent = GetRequest($"article_mobileContent_{item.Id}").SafeString().Trim();
                    aaaa.SId = Model.Id;
                    aaaa.LId = item.Id;
                    aaaa.Save();

                    var file = filea.Where(x => x.Name == Name.SafeString().Trim()).FirstOrDefault();
                    if (file != null)
                    {
                        var bytes = file.OpenReadStream().ReadBytes(file.Length);
                        if (!bytes.IsImageFile())
                        {
                            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                        }
                        var filename = $"{"SingleArticleLan" + aaaa.Id}{Path.GetExtension(file.FileName)}";
                        var filepath = FileUtil.JoinPath(DHSetting.Current.UploadPath, $"SingleArticleLan/{filename}");
                        var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
                        saveFileName.EnsureDirectory();
                        file.SaveAs(saveFileName);
                        aaaa.Pic = filepath.Replace("\\", "/");
                        aaaa.Update();
                    }
                }
            }
            tran1.Commit();
        }

        SingleArticle.Meta.Cache.Clear("");//清除缓存
        SingleArticleLan.Meta.Cache.Clear("");//清除缓存
        return Prompt(new PromptModel { Message = GetResource("修改成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 批量删除数据
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("单页文章管理删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();
        SingleArticle.DelByIds(Ids.Trim(','));
        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 图片上传
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片上传")]
    [HttpPost]
    public IActionResult UploadImg(Int32 Id, IFormFile fileupload)
    {
        var bytes = fileupload.OpenReadStream().ReadBytes(fileupload.Length);
        if (!bytes.IsImageFile())
        {
            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
        }
        var fileModel = new UploadInfo();
        fileModel.FileSize = fileupload.Length;
        fileModel.FileType = 1;
        fileModel.ItemId = Id;

        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(fileupload.FileName)}";
        var filepath = FileUtil.JoinPath(DHSetting.Current.UploadPath, $"SingleArticle/{filename}");
        var saveFileName = DHSetting.Current.WebRootPath.GetFullPath().CombinePath(filepath);
        saveFileName.EnsureDirectory();
        fileupload.SaveAs(saveFileName);

        fileModel.FileName = filename;
        fileModel.FileUrl = filepath.Replace("\\", "/");
        fileModel.Insert();

        return Json(new { file_id = fileModel.Id, file_name = filename, file_path = Pek.Helpers.DHWeb.GetSiteUrl() + "/" + filepath.Replace("\\", "/") });
    }

    /// <summary>
    /// 图片删除
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片删除")]
    public IActionResult DeleteImg(Int32 Id)
    {
        var model = UploadInfo.FindById(Id);

        if (model != null)
        {
            DHSetting.Current.WebRootPath.GetFullPath().CombinePath(model.FileUrl).AsFile().Delete();
            model.Delete();
        }

        return Ok("true");
    }

    /// <summary>
    /// 根据名称查询
    /// </summary>
    /// <param name="title"></param>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("根据名称查询")]
    public IActionResult FinByName(string title, int Id)
    {
        var Model = SingleArticle.FindByName(title.SafeString().Trim());
        if (Id != 0)
        {
            if (Model != null && Model.Id != Id)
            {
                return Json(false);
            }
        }
        else
        {
            if (Model != null)
            {
                return Json(false);
            }
        }

        return Json(true);
    }

    /// <summary>
    /// 根据调用别名查询
    /// </summary>
    /// <param name="code">调用别名</param>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("根据调用别名查询")]
    public IActionResult GetByCode(string code, int Id)
    {
        var Model = SingleArticle.FindByCode(code.SafeString().Trim());
        if (Id != 0)
        {
            if (Model != null && Model.Id != Id)
            {
                return Json(false);
            }
        }
        else
        {
            if (Model != null)
            {
                return Json(false);
            }
        }

        return Json(true);
    }
}
