﻿using DH.Entity;

using Pek.Events;
using Pek.NCube.Events.EventModel;

namespace HlktechPower.Events;

/// <summary>
/// 初始化国家数据事件消费者
/// </summary>
public class InitCountryDataEventConsumer : IConsumer<InitCountryDataEvent>
{
    public Int32 Sort { get; set; } = 0;

    public async Task HandleEventAsync(InitCountryDataEvent eventMessage)
    {
        // 写初始化国家数据的逻辑
        var entity = new Country()
        {
            Name = "Afghanistan",
            TwoLetterIsoCode = "AF",
            ThreeLetterIsoCode = "AFG",
            NumericIsoCode = 4,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        var entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "阿富汗"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Aland Islands",
            TwoLetterIsoCode = "AX",
            ThreeLetterIsoCode = "ALA",
            NumericIsoCode = 248,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "奥兰"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Albania",
            TwoLetterIsoCode = "AL",
            ThreeLetterIsoCode = "ALB",
            NumericIsoCode = 8,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "阿尔巴尼亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Algeria",
            TwoLetterIsoCode = "DZ",
            ThreeLetterIsoCode = "DZA",
            NumericIsoCode = 12,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "阿尔及利亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "American Samoa",
            TwoLetterIsoCode = "AS",
            ThreeLetterIsoCode = "ASM",
            NumericIsoCode = 16,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "美属萨摩亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Andorra",
            TwoLetterIsoCode = "AD",
            ThreeLetterIsoCode = "AND",
            NumericIsoCode = 20,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "安道尔"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Angola",
            TwoLetterIsoCode = "AO",
            ThreeLetterIsoCode = "AGO",
            NumericIsoCode = 24,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "安哥拉"
        };
        entityLan.Insert();

        //补充
        entity = new Country()
        {
            Name = "Anguilla",
            TwoLetterIsoCode = "AI",
            ThreeLetterIsoCode = "AIA",
            NumericIsoCode = 660,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "安圭拉"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Antarctica",
            TwoLetterIsoCode = "AQ",
            ThreeLetterIsoCode = "ATA",
            NumericIsoCode = 10,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南极洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "南极洲"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Antigua and Barbuda",
            TwoLetterIsoCode = "AG",
            ThreeLetterIsoCode = "ATG",
            NumericIsoCode = 28,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "安提瓜和巴布达"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Argentina",
            TwoLetterIsoCode = "AR",
            ThreeLetterIsoCode = "ARG",
            NumericIsoCode = 32,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "阿根廷"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Armenia",
            TwoLetterIsoCode = "AM",
            ThreeLetterIsoCode = "ARM",
            NumericIsoCode = 51,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "亚美尼亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Aruba",
            TwoLetterIsoCode = "AW",
            ThreeLetterIsoCode = "ABW",
            NumericIsoCode = 533,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "阿鲁巴"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Australia",
            TwoLetterIsoCode = "AU",
            ThreeLetterIsoCode = "AUS",
            NumericIsoCode = 36,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "澳大利亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Austria",
            TwoLetterIsoCode = "AT",
            ThreeLetterIsoCode = "AUT",
            NumericIsoCode = 40,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "奥地利"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Azerbaijan",
            TwoLetterIsoCode = "AZ",
            ThreeLetterIsoCode = "AZE",
            NumericIsoCode = 31,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "阿塞拜疆"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Bahamas",
            TwoLetterIsoCode = "BS",
            ThreeLetterIsoCode = "BHS",
            NumericIsoCode = 44,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "巴哈马"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Bahrain",
            TwoLetterIsoCode = "BH",
            ThreeLetterIsoCode = "BHR",
            NumericIsoCode = 48,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "巴林"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Bangladesh",
            TwoLetterIsoCode = "BD",
            ThreeLetterIsoCode = "BGD",
            NumericIsoCode = 50,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "孟加拉国"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Barbados",
            TwoLetterIsoCode = "BB",
            ThreeLetterIsoCode = "BRB",
            NumericIsoCode = 52,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "巴巴多斯"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Belarus",
            TwoLetterIsoCode = "BY",
            ThreeLetterIsoCode = "BLR",
            NumericIsoCode = 112,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "白俄罗斯"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Belgium",
            TwoLetterIsoCode = "BE",
            ThreeLetterIsoCode = "BEL",
            NumericIsoCode = 56,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "比利时"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Belize",
            TwoLetterIsoCode = "BZ",
            ThreeLetterIsoCode = "BLZ",
            NumericIsoCode = 84,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "伯利兹"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Benin",
            TwoLetterIsoCode = "BJ",
            ThreeLetterIsoCode = "BEN",
            NumericIsoCode = 204,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "贝宁"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Bermuda",
            TwoLetterIsoCode = "BM",
            ThreeLetterIsoCode = "BMU",
            NumericIsoCode = 60,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "百慕大"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Bhutan",
            TwoLetterIsoCode = "BT",
            ThreeLetterIsoCode = "BTN",
            NumericIsoCode = 64,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "不丹"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Bolivia (Plurinational State of)",
            TwoLetterIsoCode = "BO",
            ThreeLetterIsoCode = "BOL",
            NumericIsoCode = 68,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "玻利维亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Bonaire, Sint Eustatius and Saba",
            TwoLetterIsoCode = "BQ",
            ThreeLetterIsoCode = "BES",
            NumericIsoCode = 535,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "荷兰加勒比区"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Bosnia and Herzegovina",
            TwoLetterIsoCode = "BA",
            ThreeLetterIsoCode = "BIH",
            NumericIsoCode = 70,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "波黑"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Botswana",
            TwoLetterIsoCode = "BW",
            ThreeLetterIsoCode = "BWA",
            NumericIsoCode = 72,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "博茨瓦纳"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Bouvet Island",
            TwoLetterIsoCode = "BV",
            ThreeLetterIsoCode = "BVT",
            NumericIsoCode = 74,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南极洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "布韦岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Brazil",
            TwoLetterIsoCode = "BR",
            ThreeLetterIsoCode = "BRA",
            NumericIsoCode = 76,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "巴西"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "British Indian Ocean Territory",
            TwoLetterIsoCode = "IO",
            ThreeLetterIsoCode = "IOT",
            NumericIsoCode = 86,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "英属印度洋领地"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Brunei Darussalam",
            TwoLetterIsoCode = "BN",
            ThreeLetterIsoCode = "BRN",
            NumericIsoCode = 96,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "文莱"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Bulgaria",
            TwoLetterIsoCode = "BG",
            ThreeLetterIsoCode = "BGR",
            NumericIsoCode = 100,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "保加利亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Burkina Faso",
            TwoLetterIsoCode = "BF",
            ThreeLetterIsoCode = "BFA",
            NumericIsoCode = 854,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "布基纳法索"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Burundi",
            TwoLetterIsoCode = "BI",
            ThreeLetterIsoCode = "BDI",
            NumericIsoCode = 108,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "布隆迪"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Cabo Verde",
            TwoLetterIsoCode = "CV",
            ThreeLetterIsoCode = "CPV",
            NumericIsoCode = 132,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "佛得角"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Cambodia",
            TwoLetterIsoCode = "KH",
            ThreeLetterIsoCode = "KHM",
            NumericIsoCode = 116,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "柬埔寨"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Cameroon",
            TwoLetterIsoCode = "CM",
            ThreeLetterIsoCode = "CMR",
            NumericIsoCode = 120,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "喀麦隆"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Canada",
            TwoLetterIsoCode = "CA",
            ThreeLetterIsoCode = "CAN",
            NumericIsoCode = 124,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "加拿大"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Cayman Islands",
            TwoLetterIsoCode = "KY",
            ThreeLetterIsoCode = "CYM",
            NumericIsoCode = 136,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "开曼群岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Central African Republic",
            TwoLetterIsoCode = "CF",
            ThreeLetterIsoCode = "CAF",
            NumericIsoCode = 140,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "中非"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Chad",
            TwoLetterIsoCode = "TD",
            ThreeLetterIsoCode = "TCD",
            NumericIsoCode = 148,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "乍得"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Chile",
            TwoLetterIsoCode = "CL",
            ThreeLetterIsoCode = "CHL",
            NumericIsoCode = 152,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "智利"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "China",
            TwoLetterIsoCode = "CN",
            ThreeLetterIsoCode = "CHN",
            NumericIsoCode = 156,
            DisplayOrder = 1,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true,
            IsDefault = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "中国"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Christmas Island",
            TwoLetterIsoCode = "CX",
            ThreeLetterIsoCode = "CXR",
            NumericIsoCode = 162,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "圣诞岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Cocos (Keeling) Islands",
            TwoLetterIsoCode = "CC",
            ThreeLetterIsoCode = "CCK",
            NumericIsoCode = 166,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "科科斯（基林）群岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Colombia",
            TwoLetterIsoCode = "CO",
            ThreeLetterIsoCode = "COL",
            NumericIsoCode = 170,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "哥伦比亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Comoros",
            TwoLetterIsoCode = "KM",
            ThreeLetterIsoCode = "COM",
            NumericIsoCode = 174,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "科摩罗"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Congo",
            TwoLetterIsoCode = "CG",
            ThreeLetterIsoCode = "COG",
            NumericIsoCode = 178,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "刚果共和国"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Congo (Democratic Republic of the)",
            TwoLetterIsoCode = "CD",
            ThreeLetterIsoCode = "COD",
            NumericIsoCode = 180,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "刚果民主共和国"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Cook Islands",
            TwoLetterIsoCode = "CK",
            ThreeLetterIsoCode = "COK",
            NumericIsoCode = 184,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "库克群岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Costa Rica",
            TwoLetterIsoCode = "CR",
            ThreeLetterIsoCode = "CRI",
            NumericIsoCode = 188,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "哥斯达黎加"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Cote D'ivoire",
            TwoLetterIsoCode = "CI",
            ThreeLetterIsoCode = "CIV",
            NumericIsoCode = 384,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "科特迪瓦"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Croatia",
            TwoLetterIsoCode = "HR",
            ThreeLetterIsoCode = "HRV",
            NumericIsoCode = 191,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "克罗地亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Cuba",
            TwoLetterIsoCode = "CU",
            ThreeLetterIsoCode = "CUB",
            NumericIsoCode = 192,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "古巴"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Curacao",
            TwoLetterIsoCode = "CW",
            ThreeLetterIsoCode = "CUW",
            NumericIsoCode = 531,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "库拉索"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Cyprus",
            TwoLetterIsoCode = "CY",
            ThreeLetterIsoCode = "CYP",
            NumericIsoCode = 196,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "塞浦路斯"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Czechia",
            TwoLetterIsoCode = "CZ",
            ThreeLetterIsoCode = "CZE",
            NumericIsoCode = 203,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "捷克"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Denmark",
            TwoLetterIsoCode = "DK",
            ThreeLetterIsoCode = "DNK",
            NumericIsoCode = 208,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "丹麦"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Djibouti",
            TwoLetterIsoCode = "DJ",
            ThreeLetterIsoCode = "DJI",
            NumericIsoCode = 262,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "吉布提"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Dominica",
            TwoLetterIsoCode = "DM",
            ThreeLetterIsoCode = "DMA",
            NumericIsoCode = 212,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "多米尼克"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Dominican Republic",
            TwoLetterIsoCode = "DO",
            ThreeLetterIsoCode = "DOM",
            NumericIsoCode = 214,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "多米尼加"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Ecuador",
            TwoLetterIsoCode = "EC",
            ThreeLetterIsoCode = "ECU",
            NumericIsoCode = 218,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "厄瓜多尔"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Egypt",
            TwoLetterIsoCode = "EG",
            ThreeLetterIsoCode = "EGY",
            NumericIsoCode = 818,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "埃及"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "El Salvador",
            TwoLetterIsoCode = "SV",
            ThreeLetterIsoCode = "SLV",
            NumericIsoCode = 222,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "萨尔瓦多"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Equatorial Guinea",
            TwoLetterIsoCode = "GQ",
            ThreeLetterIsoCode = "GNQ",
            NumericIsoCode = 226,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "赤道几内亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Eritrea",
            TwoLetterIsoCode = "ER",
            ThreeLetterIsoCode = "ERI",
            NumericIsoCode = 232,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "厄立特里亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Estonia",
            TwoLetterIsoCode = "EE",
            ThreeLetterIsoCode = "EST",
            NumericIsoCode = 233,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "爱沙尼亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Eswatini",
            TwoLetterIsoCode = "SZ",
            ThreeLetterIsoCode = "SWZ",
            NumericIsoCode = 748,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "斯威士兰"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Ethiopia",
            TwoLetterIsoCode = "ET",
            ThreeLetterIsoCode = "ETH",
            NumericIsoCode = 231,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "埃塞俄比亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Falkland Islands (Malvinas)",
            TwoLetterIsoCode = "FK",
            ThreeLetterIsoCode = "FLK",
            NumericIsoCode = 238,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "福克兰群岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Faroe Islands",
            TwoLetterIsoCode = "FO",
            ThreeLetterIsoCode = "FRO",
            NumericIsoCode = 234,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "法罗群岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Fiji",
            TwoLetterIsoCode = "FJ",
            ThreeLetterIsoCode = "FJI",
            NumericIsoCode = 242,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "斐济"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Finland",
            TwoLetterIsoCode = "FI",
            ThreeLetterIsoCode = "FIN",
            NumericIsoCode = 246,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "芬兰"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "France",
            TwoLetterIsoCode = "FR",
            ThreeLetterIsoCode = "FRA",
            NumericIsoCode = 250,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "法国"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "French Guiana",
            TwoLetterIsoCode = "GF",
            ThreeLetterIsoCode = "GUF",
            NumericIsoCode = 254,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "法属圭亚那"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "French Polynesia",
            TwoLetterIsoCode = "PF",
            ThreeLetterIsoCode = "PYF",
            NumericIsoCode = 258,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "法属波利尼西亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "French Southern Territories",
            TwoLetterIsoCode = "TF",
            ThreeLetterIsoCode = "ATF",
            NumericIsoCode = 260,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南极洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "法属南部和南极领地"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Gabon",
            TwoLetterIsoCode = "GA",
            ThreeLetterIsoCode = "GAB",
            NumericIsoCode = 266,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "加蓬"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Gambia",
            TwoLetterIsoCode = "GM",
            ThreeLetterIsoCode = "GMB",
            NumericIsoCode = 270,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "冈比亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Georgia",
            TwoLetterIsoCode = "GE",
            ThreeLetterIsoCode = "GEO",
            NumericIsoCode = 268,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "格鲁吉亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Germany",
            TwoLetterIsoCode = "DE",
            ThreeLetterIsoCode = "DEU",
            NumericIsoCode = 276,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "德国"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Ghana",
            TwoLetterIsoCode = "GH",
            ThreeLetterIsoCode = "GHA",
            NumericIsoCode = 288,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "加纳"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Gibraltar",
            TwoLetterIsoCode = "GI",
            ThreeLetterIsoCode = "GIB",
            NumericIsoCode = 292,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "直布罗陀"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Greece",
            TwoLetterIsoCode = "GR",
            ThreeLetterIsoCode = "GRC",
            NumericIsoCode = 300,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "希腊"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Greenland",
            TwoLetterIsoCode = "GL",
            ThreeLetterIsoCode = "GRL",
            NumericIsoCode = 304,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "格陵兰"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Grenada",
            TwoLetterIsoCode = "GD",
            ThreeLetterIsoCode = "GRD",
            NumericIsoCode = 308,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "格林纳达"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Guadeloupe",
            TwoLetterIsoCode = "GP",
            ThreeLetterIsoCode = "GLP",
            NumericIsoCode = 312,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "瓜德罗普"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Guam",
            TwoLetterIsoCode = "GU",
            ThreeLetterIsoCode = "GUM",
            NumericIsoCode = 316,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "关岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Guatemala",
            TwoLetterIsoCode = "GT",
            ThreeLetterIsoCode = "GTM",
            NumericIsoCode = 320,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "危地马拉"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Guernsey",
            TwoLetterIsoCode = "GG",
            ThreeLetterIsoCode = "GGY",
            NumericIsoCode = 831,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "根西"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Guinea",
            TwoLetterIsoCode = "GN",
            ThreeLetterIsoCode = "GIN",
            NumericIsoCode = 324,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "几内亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Guinea-Bissau",
            TwoLetterIsoCode = "GW",
            ThreeLetterIsoCode = "GNB",
            NumericIsoCode = 624,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "几内亚比绍"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Guyana",
            TwoLetterIsoCode = "GY",
            ThreeLetterIsoCode = "GUY",
            NumericIsoCode = 328,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "圭亚那"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Haiti",
            TwoLetterIsoCode = "HT",
            ThreeLetterIsoCode = "HTI",
            NumericIsoCode = 332,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "海地"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Heard Island and McDonald Islands",
            TwoLetterIsoCode = "HM",
            ThreeLetterIsoCode = "HMD",
            NumericIsoCode = 334,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "赫德岛和麦克唐纳群岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Holy See",
            TwoLetterIsoCode = "VA",
            ThreeLetterIsoCode = "VAT",
            NumericIsoCode = 336,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "梵蒂冈"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Honduras",
            TwoLetterIsoCode = "HN",
            ThreeLetterIsoCode = "HND",
            NumericIsoCode = 340,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "洪都拉斯"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Hong Kong",
            TwoLetterIsoCode = "HK",
            ThreeLetterIsoCode = "HKG",
            NumericIsoCode = 344,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "香港"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Hungary",
            TwoLetterIsoCode = "HU",
            ThreeLetterIsoCode = "HUN",
            NumericIsoCode = 348,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "匈牙利"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Iceland",
            TwoLetterIsoCode = "IS",
            ThreeLetterIsoCode = "ISL",
            NumericIsoCode = 352,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "冰岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "India",
            TwoLetterIsoCode = "IN",
            ThreeLetterIsoCode = "IND",
            NumericIsoCode = 356,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "印度"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Indonesia",
            TwoLetterIsoCode = "ID",
            ThreeLetterIsoCode = "IDN",
            NumericIsoCode = 360,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "印度尼西亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Iran (Islamic Republic of)",
            TwoLetterIsoCode = "IR",
            ThreeLetterIsoCode = "IRN",
            NumericIsoCode = 364,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "伊朗"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Iraq",
            TwoLetterIsoCode = "IQ",
            ThreeLetterIsoCode = "IRQ",
            NumericIsoCode = 368,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "伊拉克"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Ireland",
            TwoLetterIsoCode = "IE",
            ThreeLetterIsoCode = "IRL",
            NumericIsoCode = 372,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "爱尔兰"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Isle of Man",
            TwoLetterIsoCode = "IM",
            ThreeLetterIsoCode = "IMN",
            NumericIsoCode = 833,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "马恩岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Israel",
            TwoLetterIsoCode = "IL",
            ThreeLetterIsoCode = "ISR",
            NumericIsoCode = 376,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "以色列"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Italy",
            TwoLetterIsoCode = "IT",
            ThreeLetterIsoCode = "ITA",
            NumericIsoCode = 380,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "意大利"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Jamaica",
            TwoLetterIsoCode = "JM",
            ThreeLetterIsoCode = "JAM",
            NumericIsoCode = 388,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "牙买加"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Japan",
            TwoLetterIsoCode = "JP",
            ThreeLetterIsoCode = "JPN",
            NumericIsoCode = 392,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "日本"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Jersey",
            TwoLetterIsoCode = "JE",
            ThreeLetterIsoCode = "JEY",
            NumericIsoCode = 832,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "泽西"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Jordan",
            TwoLetterIsoCode = "JO",
            ThreeLetterIsoCode = "JOR",
            NumericIsoCode = 400,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "约旦"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Kazakhstan",
            TwoLetterIsoCode = "KZ",
            ThreeLetterIsoCode = "KAZ",
            NumericIsoCode = 398,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "哈萨克斯坦"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Kenya",
            TwoLetterIsoCode = "KE",
            ThreeLetterIsoCode = "KEN",
            NumericIsoCode = 404,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "肯尼亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Kiribati",
            TwoLetterIsoCode = "KI",
            ThreeLetterIsoCode = "KIR",
            NumericIsoCode = 296,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "基里巴斯"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Korea (Democratic People's Republic of)",
            TwoLetterIsoCode = "KP",
            ThreeLetterIsoCode = "PRK",
            NumericIsoCode = 408,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "朝鲜"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Korea (Republic of)",
            TwoLetterIsoCode = "KR",
            ThreeLetterIsoCode = "KOR",
            NumericIsoCode = 410,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "韩国"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Kuwait",
            TwoLetterIsoCode = "KW",
            ThreeLetterIsoCode = "KWT",
            NumericIsoCode = 414,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "科威特"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Kyrgyzstan",
            TwoLetterIsoCode = "KG",
            ThreeLetterIsoCode = "KGZ",
            NumericIsoCode = 417,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "吉尔吉斯斯坦"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Lao People's Democratic Republic",
            TwoLetterIsoCode = "LA",
            ThreeLetterIsoCode = "LAO",
            NumericIsoCode = 418,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "老挝"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Latvia",
            TwoLetterIsoCode = "LV",
            ThreeLetterIsoCode = "LVA",
            NumericIsoCode = 428,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "拉脱维亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Lebanon",
            TwoLetterIsoCode = "LB",
            ThreeLetterIsoCode = "LBN",
            NumericIsoCode = 422,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "黎巴嫩"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Lesotho",
            TwoLetterIsoCode = "LS",
            ThreeLetterIsoCode = "LSO",
            NumericIsoCode = 426,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "莱索托"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Liberia",
            TwoLetterIsoCode = "LR",
            ThreeLetterIsoCode = "LBR",
            NumericIsoCode = 430,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "利比里亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Libya",
            TwoLetterIsoCode = "LY",
            ThreeLetterIsoCode = "LBY",
            NumericIsoCode = 434,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "利比亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Liechtenstein",
            TwoLetterIsoCode = "LI",
            ThreeLetterIsoCode = "LIE",
            NumericIsoCode = 438,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "列支敦士登"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Lithuania",
            TwoLetterIsoCode = "LT",
            ThreeLetterIsoCode = "LTU",
            NumericIsoCode = 440,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "立陶宛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Luxembourg",
            TwoLetterIsoCode = "LU",
            ThreeLetterIsoCode = "LUX",
            NumericIsoCode = 442,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "卢森堡"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Macao",
            TwoLetterIsoCode = "MO",
            ThreeLetterIsoCode = "MAC",
            NumericIsoCode = 446,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "澳门"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Madagascar",
            TwoLetterIsoCode = "MG",
            ThreeLetterIsoCode = "MDG",
            NumericIsoCode = 450,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "马达加斯加"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Malawi",
            TwoLetterIsoCode = "MW",
            ThreeLetterIsoCode = "MWI",
            NumericIsoCode = 454,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "马拉维"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Malaysia",
            TwoLetterIsoCode = "MY",
            ThreeLetterIsoCode = "MYS",
            NumericIsoCode = 458,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "马来西亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Maldives",
            TwoLetterIsoCode = "MV",
            ThreeLetterIsoCode = "MDV",
            NumericIsoCode = 462,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "马尔代夫"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Mali",
            TwoLetterIsoCode = "ML",
            ThreeLetterIsoCode = "MLI",
            NumericIsoCode = 466,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "马里"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Malta",
            TwoLetterIsoCode = "MT",
            ThreeLetterIsoCode = "MLT",
            NumericIsoCode = 470,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "马耳他"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Marshall Islands",
            TwoLetterIsoCode = "MH",
            ThreeLetterIsoCode = "MHL",
            NumericIsoCode = 584,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "马绍尔群岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Martinique",
            TwoLetterIsoCode = "MQ",
            ThreeLetterIsoCode = "MTQ",
            NumericIsoCode = 474,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "马提尼克"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Mauritania",
            TwoLetterIsoCode = "MR",
            ThreeLetterIsoCode = "MRT",
            NumericIsoCode = 478,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "毛里塔尼亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Mauritius",
            TwoLetterIsoCode = "MU",
            ThreeLetterIsoCode = "MUS",
            NumericIsoCode = 480,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "毛里求斯"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Mayotte",
            TwoLetterIsoCode = "YT",
            ThreeLetterIsoCode = "MYT",
            NumericIsoCode = 175,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "马约特"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Mexico",
            TwoLetterIsoCode = "MX",
            ThreeLetterIsoCode = "MEX",
            NumericIsoCode = 484,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "墨西哥"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Micronesia (Federated States of)",
            TwoLetterIsoCode = "FM",
            ThreeLetterIsoCode = "FSM",
            NumericIsoCode = 583,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "密克罗尼西亚联邦"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Moldova (Republic of)",
            TwoLetterIsoCode = "MD",
            ThreeLetterIsoCode = "MDA",
            NumericIsoCode = 498,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "摩尔多瓦"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Monaco",
            TwoLetterIsoCode = "MC",
            ThreeLetterIsoCode = "MCO",
            NumericIsoCode = 492,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "摩纳哥"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Mongolia",
            TwoLetterIsoCode = "MN",
            ThreeLetterIsoCode = "MNG",
            NumericIsoCode = 496,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "蒙古"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Montenegro",
            TwoLetterIsoCode = "ME",
            ThreeLetterIsoCode = "MNE",
            NumericIsoCode = 499,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "黑山"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Montserrat",
            TwoLetterIsoCode = "MS",
            ThreeLetterIsoCode = "MSR",
            NumericIsoCode = 500,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "蒙特塞拉特"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Morocco",
            TwoLetterIsoCode = "MA",
            ThreeLetterIsoCode = "MAR",
            NumericIsoCode = 504,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "摩洛哥"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Mozambique",
            TwoLetterIsoCode = "MZ",
            ThreeLetterIsoCode = "MOZ",
            NumericIsoCode = 508,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "莫桑比克"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Myanmar",
            TwoLetterIsoCode = "MM",
            ThreeLetterIsoCode = "MMR",
            NumericIsoCode = 104,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "缅甸"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Namibia",
            TwoLetterIsoCode = "NA",
            ThreeLetterIsoCode = "NAM",
            NumericIsoCode = 516,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "纳米比亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Nauru",
            TwoLetterIsoCode = "NR",
            ThreeLetterIsoCode = "NRU",
            NumericIsoCode = 520,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "瑙鲁"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Nepal",
            TwoLetterIsoCode = "NP",
            ThreeLetterIsoCode = "NPL",
            NumericIsoCode = 524,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "尼泊尔"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Netherlands",
            TwoLetterIsoCode = "NL",
            ThreeLetterIsoCode = "NLD",
            NumericIsoCode = 528,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "荷兰"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "New Caledonia",
            TwoLetterIsoCode = "NC",
            ThreeLetterIsoCode = "NCL",
            NumericIsoCode = 540,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "新喀里多尼亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "New Zealand",
            TwoLetterIsoCode = "NZ",
            ThreeLetterIsoCode = "NZL",
            NumericIsoCode = 554,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "新西兰"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Nicaragua",
            TwoLetterIsoCode = "NI",
            ThreeLetterIsoCode = "NIC",
            NumericIsoCode = 558,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "尼加拉瓜"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Niger",
            TwoLetterIsoCode = "NE",
            ThreeLetterIsoCode = "NER",
            NumericIsoCode = 562,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "尼日尔"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Nigeria",
            TwoLetterIsoCode = "NG",
            ThreeLetterIsoCode = "NGA",
            NumericIsoCode = 566,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "尼日利亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Niue",
            TwoLetterIsoCode = "NU",
            ThreeLetterIsoCode = "NIU",
            NumericIsoCode = 570,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "纽埃"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Norfolk Island",
            TwoLetterIsoCode = "NF",
            ThreeLetterIsoCode = "NFK",
            NumericIsoCode = 574,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "诺福克岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "North Macedonia",
            TwoLetterIsoCode = "MK",
            ThreeLetterIsoCode = "MKD",
            NumericIsoCode = 807,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "北马其顿"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Northern Mariana Islands",
            TwoLetterIsoCode = "MP",
            ThreeLetterIsoCode = "MNP",
            NumericIsoCode = 580,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "北马里亚纳群岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Norway",
            TwoLetterIsoCode = "NO",
            ThreeLetterIsoCode = "NOR",
            NumericIsoCode = 578,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "挪威"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Oman",
            TwoLetterIsoCode = "OM",
            ThreeLetterIsoCode = "OMN",
            NumericIsoCode = 512,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "阿曼"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Pakistan",
            TwoLetterIsoCode = "PK",
            ThreeLetterIsoCode = "PAK",
            NumericIsoCode = 586,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "巴基斯坦"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Palau",
            TwoLetterIsoCode = "PW",
            ThreeLetterIsoCode = "PLW",
            NumericIsoCode = 585,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "帕劳"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Palestine, State of",
            TwoLetterIsoCode = "PS",
            ThreeLetterIsoCode = "PSE",
            NumericIsoCode = 275,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "巴勒斯坦"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Panama",
            TwoLetterIsoCode = "PA",
            ThreeLetterIsoCode = "PAN",
            NumericIsoCode = 591,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "巴拿马"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Papua New Guinea",
            TwoLetterIsoCode = "PG",
            ThreeLetterIsoCode = "PNG",
            NumericIsoCode = 598,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "巴布亚新几内亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Paraguay",
            TwoLetterIsoCode = "PY",
            ThreeLetterIsoCode = "PRY",
            NumericIsoCode = 600,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "巴拉圭"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Peru",
            TwoLetterIsoCode = "PE",
            ThreeLetterIsoCode = "PER",
            NumericIsoCode = 604,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "秘鲁"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Philippines",
            TwoLetterIsoCode = "PH",
            ThreeLetterIsoCode = "PHL",
            NumericIsoCode = 608,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "菲律宾"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Pitcairn",
            TwoLetterIsoCode = "PN",
            ThreeLetterIsoCode = "PCN",
            NumericIsoCode = 612,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "皮特凯恩群岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Poland",
            TwoLetterIsoCode = "PL",
            ThreeLetterIsoCode = "POL",
            NumericIsoCode = 616,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "波兰"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Portugal",
            TwoLetterIsoCode = "PT",
            ThreeLetterIsoCode = "PRT",
            NumericIsoCode = 620,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "葡萄牙"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Puerto Rico",
            TwoLetterIsoCode = "PR",
            ThreeLetterIsoCode = "PRI",
            NumericIsoCode = 630,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "波多黎各"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Qatar",
            TwoLetterIsoCode = "QA",
            ThreeLetterIsoCode = "QAT",
            NumericIsoCode = 634,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "卡塔尔"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Reunion",
            TwoLetterIsoCode = "RE",
            ThreeLetterIsoCode = "REU",
            NumericIsoCode = 638,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "留尼汪"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Romania",
            TwoLetterIsoCode = "RO",
            ThreeLetterIsoCode = "ROU",
            NumericIsoCode = 642,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "罗马尼亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Russian Federation",
            TwoLetterIsoCode = "RU",
            ThreeLetterIsoCode = "RUS",
            NumericIsoCode = 643,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "俄罗斯"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Rwanda",
            TwoLetterIsoCode = "RW",
            ThreeLetterIsoCode = "RWA",
            NumericIsoCode = 646,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "卢旺达"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Saint Barthelemy",
            TwoLetterIsoCode = "BL",
            ThreeLetterIsoCode = "BLM",
            NumericIsoCode = 652,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "圣巴泰勒米"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Saint Helena, Ascension and Tristan da Cunha",
            TwoLetterIsoCode = "SH",
            ThreeLetterIsoCode = "SHN",
            NumericIsoCode = 654,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "圣赫勒拿、阿森松和特里斯坦-达库尼亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Saint Kitts and Nevis",
            TwoLetterIsoCode = "KN",
            ThreeLetterIsoCode = "KNA",
            NumericIsoCode = 659,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "圣基茨和尼维斯"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Saint Lucia",
            TwoLetterIsoCode = "LC",
            ThreeLetterIsoCode = "LCA",
            NumericIsoCode = 662,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "圣卢西亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Saint Martin (French part)",
            TwoLetterIsoCode = "MF",
            ThreeLetterIsoCode = "MAF",
            NumericIsoCode = 663,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "法属圣马丁"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Saint Pierre and Miquelon",
            TwoLetterIsoCode = "PM",
            ThreeLetterIsoCode = "SPM",
            NumericIsoCode = 666,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "圣皮埃尔和密克隆"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Saint Vincent and the Grenadines",
            TwoLetterIsoCode = "VC",
            ThreeLetterIsoCode = "VCT",
            NumericIsoCode = 670,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "圣文森特和格林纳丁斯"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Samoa",
            TwoLetterIsoCode = "WS",
            ThreeLetterIsoCode = "WSM",
            NumericIsoCode = 882,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "萨摩亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "San Marino",
            TwoLetterIsoCode = "SM",
            ThreeLetterIsoCode = "SMR",
            NumericIsoCode = 674,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "圣马力诺"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Sao Tome and Principe",
            TwoLetterIsoCode = "ST",
            ThreeLetterIsoCode = "STP",
            NumericIsoCode = 678,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "圣多美和普林西比"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Saudi Arabia",
            TwoLetterIsoCode = "SA",
            ThreeLetterIsoCode = "SAU",
            NumericIsoCode = 682,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "沙特阿拉伯"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Senegal",
            TwoLetterIsoCode = "SN",
            ThreeLetterIsoCode = "SEN",
            NumericIsoCode = 686,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "塞内加尔"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Serbia",
            TwoLetterIsoCode = "RS",
            ThreeLetterIsoCode = "SRB",
            NumericIsoCode = 688,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "塞尔维亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Seychelles",
            TwoLetterIsoCode = "SC",
            ThreeLetterIsoCode = "SYC",
            NumericIsoCode = 690,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "塞舌尔"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Sierra Leone",
            TwoLetterIsoCode = "SL",
            ThreeLetterIsoCode = "SLE",
            NumericIsoCode = 694,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "塞拉利昂"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Singapore",
            TwoLetterIsoCode = "SG",
            ThreeLetterIsoCode = "SGP",
            NumericIsoCode = 702,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "新加坡"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Sint Maarten (Dutch part)",
            TwoLetterIsoCode = "SX",
            ThreeLetterIsoCode = "SXM",
            NumericIsoCode = 534,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "荷属圣马丁"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Slovakia",
            TwoLetterIsoCode = "SK",
            ThreeLetterIsoCode = "SVK",
            NumericIsoCode = 703,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "斯洛伐克"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Slovenia",
            TwoLetterIsoCode = "SI",
            ThreeLetterIsoCode = "SVN",
            NumericIsoCode = 705,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "斯洛文尼亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Solomon Islands",
            TwoLetterIsoCode = "SB",
            ThreeLetterIsoCode = "SLB",
            NumericIsoCode = 90,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "所罗门群岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Somalia",
            TwoLetterIsoCode = "SO",
            ThreeLetterIsoCode = "SOM",
            NumericIsoCode = 706,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "索马里"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "South Africa",
            TwoLetterIsoCode = "ZA",
            ThreeLetterIsoCode = "ZAF",
            NumericIsoCode = 710,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "南非"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "South Georgia and the South Sandwich Islands",
            TwoLetterIsoCode = "GS",
            ThreeLetterIsoCode = "SGS",
            NumericIsoCode = 239,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "南乔治亚和南桑威奇群岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "South Sudan",
            TwoLetterIsoCode = "SS",
            ThreeLetterIsoCode = "SSD",
            NumericIsoCode = 728,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "南苏丹"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Spain",
            TwoLetterIsoCode = "ES",
            ThreeLetterIsoCode = "ESP",
            NumericIsoCode = 724,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "西班牙"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Sri Lanka",
            TwoLetterIsoCode = "LK",
            ThreeLetterIsoCode = "LKA",
            NumericIsoCode = 144,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "斯里兰卡"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Sudan",
            TwoLetterIsoCode = "SD",
            ThreeLetterIsoCode = "SDN",
            NumericIsoCode = 729,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "苏丹"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Suriname",
            TwoLetterIsoCode = "SR",
            ThreeLetterIsoCode = "SUR",
            NumericIsoCode = 740,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "苏里南"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Svalbard and Jan Mayen",
            TwoLetterIsoCode = "SJ",
            ThreeLetterIsoCode = "SJM",
            NumericIsoCode = 744,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "斯瓦尔巴和扬马延"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Sweden",
            TwoLetterIsoCode = "SE",
            ThreeLetterIsoCode = "SWE",
            NumericIsoCode = 752,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "瑞典"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Switzerland",
            TwoLetterIsoCode = "CH",
            ThreeLetterIsoCode = "CHE",
            NumericIsoCode = 756,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "瑞士"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Syrian Arab Republic",
            TwoLetterIsoCode = "SY",
            ThreeLetterIsoCode = "SYR",
            NumericIsoCode = 760,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "叙利亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Taiwan, Province of China",
            TwoLetterIsoCode = "TW",
            ThreeLetterIsoCode = "TWN",
            NumericIsoCode = 158,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "中国台湾省"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Tajikistan",
            TwoLetterIsoCode = "TJ",
            ThreeLetterIsoCode = "TJK",
            NumericIsoCode = 762,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "塔吉克斯坦"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Tanzania, United Republic of",
            TwoLetterIsoCode = "TZ",
            ThreeLetterIsoCode = "TZA",
            NumericIsoCode = 834,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "坦桑尼亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Thailand",
            TwoLetterIsoCode = "TH",
            ThreeLetterIsoCode = "THA",
            NumericIsoCode = 764,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "泰国"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Timor-Leste",
            TwoLetterIsoCode = "TL",
            ThreeLetterIsoCode = "TLS",
            NumericIsoCode = 626,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "东帝汶"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Togo",
            TwoLetterIsoCode = "TG",
            ThreeLetterIsoCode = "TGO",
            NumericIsoCode = 768,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "多哥"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Tokelau",
            TwoLetterIsoCode = "TK",
            ThreeLetterIsoCode = "TKL",
            NumericIsoCode = 772,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "托克劳"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Tonga",
            TwoLetterIsoCode = "TO",
            ThreeLetterIsoCode = "TON",
            NumericIsoCode = 776,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "汤加"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Trinidad and Tobago",
            TwoLetterIsoCode = "TT",
            ThreeLetterIsoCode = "TTO",
            NumericIsoCode = 780,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "特立尼达和多巴哥"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Tunisia",
            TwoLetterIsoCode = "TN",
            ThreeLetterIsoCode = "TUN",
            NumericIsoCode = 788,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "突尼斯"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Turkey",
            TwoLetterIsoCode = "TR",
            ThreeLetterIsoCode = "TUR",
            NumericIsoCode = 792,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "土耳其"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Turkmenistan",
            TwoLetterIsoCode = "TM",
            ThreeLetterIsoCode = "TKM",
            NumericIsoCode = 795,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "土库曼斯坦"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Turks and Caicos Islands",
            TwoLetterIsoCode = "TC",
            ThreeLetterIsoCode = "TCA",
            NumericIsoCode = 796,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "特克斯和凯科斯群岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Tuvalu",
            TwoLetterIsoCode = "TV",
            ThreeLetterIsoCode = "TUV",
            NumericIsoCode = 798,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "图瓦卢"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Uganda",
            TwoLetterIsoCode = "UG",
            ThreeLetterIsoCode = "UGA",
            NumericIsoCode = 800,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "乌干达"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Ukraine",
            TwoLetterIsoCode = "UA",
            ThreeLetterIsoCode = "UKR",
            NumericIsoCode = 804,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "乌克兰"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "United Arab Emirates",
            TwoLetterIsoCode = "AE",
            ThreeLetterIsoCode = "ARE",
            NumericIsoCode = 784,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "阿联酋"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "United Kingdom",
            TwoLetterIsoCode = "GB",
            ThreeLetterIsoCode = "GBR",
            NumericIsoCode = 826,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "英国"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "United States of America",
            TwoLetterIsoCode = "US",
            ThreeLetterIsoCode = "USA",
            NumericIsoCode = 840,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "美国"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "United States Minor Outlying Islands",
            TwoLetterIsoCode = "UM",
            ThreeLetterIsoCode = "UMI",
            NumericIsoCode = 581,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "美国本土外小岛屿"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Uruguay",
            TwoLetterIsoCode = "UY",
            ThreeLetterIsoCode = "URY",
            NumericIsoCode = 858,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "乌拉圭"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Uzbekistan",
            TwoLetterIsoCode = "UZ",
            ThreeLetterIsoCode = "UZB",
            NumericIsoCode = 860,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "乌兹别克斯坦"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Vanuatu",
            TwoLetterIsoCode = "VU",
            ThreeLetterIsoCode = "VUT",
            NumericIsoCode = 548,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "瓦努阿图"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Venezuela (Bolivarian Republic of)",
            TwoLetterIsoCode = "VE",
            ThreeLetterIsoCode = "VEN",
            NumericIsoCode = 862,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("南美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "委内瑞拉"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Viet Nam",
            TwoLetterIsoCode = "VN",
            ThreeLetterIsoCode = "VNM",
            NumericIsoCode = 704,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "越南"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Virgin Islands (British)",
            TwoLetterIsoCode = "VG",
            ThreeLetterIsoCode = "VGB",
            NumericIsoCode = 92,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "英属维尔京群岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Virgin Islands (U.S.)",
            TwoLetterIsoCode = "VI",
            ThreeLetterIsoCode = "VIR",
            NumericIsoCode = 850,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("北美洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "美属维尔京群岛"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Wallis and Futuna",
            TwoLetterIsoCode = "WF",
            ThreeLetterIsoCode = "WLF",
            NumericIsoCode = 876,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("大洋洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "瓦利斯和富图纳"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Western Sahara",
            TwoLetterIsoCode = "EH",
            ThreeLetterIsoCode = "ESH",
            NumericIsoCode = 732,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "西撒哈拉"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Yemen",
            TwoLetterIsoCode = "YE",
            ThreeLetterIsoCode = "YEM",
            NumericIsoCode = 887,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("亚洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "也门"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Zambia",
            TwoLetterIsoCode = "ZM",
            ThreeLetterIsoCode = "ZMB",
            NumericIsoCode = 894,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "赞比亚"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Zimbabwe",
            TwoLetterIsoCode = "ZW",
            ThreeLetterIsoCode = "ZWE",
            NumericIsoCode = 716,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("非洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "津巴布韦"
        };
        entityLan.Insert();

        entity = new Country()
        {
            Name = "Kosovo",
            TwoLetterIsoCode = "XK",
            ThreeLetterIsoCode = "XKX",
            NumericIsoCode = 0,
            DisplayOrder = 0,
            ContinentCode = Continent.FindByName("欧洲")?.TwoLetterIsoCode,
            IsEnabled = true
        };
        entity.Insert();
        entityLan = new CountryLan
        {
            CId = entity.Id,
            LId = 1,
            Name = "科索沃"
        };
        entityLan.Insert();

        var countries = Country.FindAll();
        foreach (var country in countries)
        {
            var countryLan = new CountryLan { Name = country.Name, CId = country.Id, LId = 4 };
            countryLan.Insert();
        }

        await Task.CompletedTask.ConfigureAwait(false);
    }
}
