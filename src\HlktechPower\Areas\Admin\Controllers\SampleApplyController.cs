﻿using DH.Entity;
using HlktechPower.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife.Data;
using Pek.DsMallUI;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace HlktechPower.Areas.Admin.Controllers
{
    /// <summary>
    /// 样品申请
    /// </summary>
    [DisplayName("样品申请")]
    [Description("用于样品申请记录的管理")]
    [AdminArea]
    [DHMenu(45, ParentMenuName = "Product", ParentMenuDisplayName = "产品", ParentMenuUrl = "~/{area}/Product", ParentMenuOrder = 90, ParentVisible = true, CurrentMenuUrl = "~/{area}/SampleApply", CurrentMenuName = "SampleApply", CurrentIcon = "&#xe652;", LastUpdate = "20250604")]
    public class SampleApplyController : PekCubeAdminControllerX
    {
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("列表")]
        public IActionResult Index(Int32 page)
        {
            dynamic viewModel = new ExpandoObject();

            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = 10,
                RetrieveTotalCount = true,
                Sort = SampleApply._.CreateTime,
                Desc = true,
            };

            viewModel.list = SampleApply.FindAll().Select(e => new SampleApply
            {
                Id = e.Id,
                listSampleApplyEx = SampleApplyEx.FindAllBySId(e.Id),
            });

            return View(viewModel);
        }

        public IActionResult Details(Int64 Id)
        {
            var model = SampleApply.FindById(Id);
            if (model == null) return Content(GetResource("记录不存在"));
            if(model.Country != null)
            {
                var lan = CountryLan.FindByCIdAndLId(model.Country.Id, WorkingLanguage.Id);
                if (lan != null && !lan.Name.IsNullOrWhiteSpace()) model.Country.Name = lan.Name;
            }
            return View(model);
        }
    }
}
