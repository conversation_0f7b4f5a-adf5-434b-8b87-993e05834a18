﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechPower.Entity;

/// <summary>样品申请拓展表</summary>
public partial class SampleApplyExModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>样品申请编号</summary>
    public Int64 SId { get; set; }

    /// <summary>产品分类编号1</summary>
    public Int64 PId1 { get; set; }

    /// <summary>产品分类编号2</summary>
    public Int64 PId2 { get; set; }

    /// <summary>产品分类编号3</summary>
    public Int64 PId3 { get; set; }

    /// <summary>数量</summary>
    public Int32 Number { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISampleApplyEx model)
    {
        Id = model.Id;
        SId = model.SId;
        PId1 = model.PId1;
        PId2 = model.PId2;
        PId3 = model.PId3;
        Number = model.Number;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
