﻿using HlktechPower.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife;
using Pek.NCube;
using System.Dynamic;

namespace HlktechPower.Controllers
{
    /// <summary>
    /// 关于我们
    /// </summary>
    public class AboutUsController : PekBaseControllerX
    {
        /// <summary>
        /// 企业简介
        /// </summary>
        /// <returns></returns>
        public IActionResult Index()
        {
            var model = SingleArticle.FindByCode("CompanyProfile");
            if (model == null)
                return Content(GetResource("文章内容不存在"));

            var modelLan = SingleArticleLan.FindBySIdAndLId(model.Id, WorkingLanguage.Id);

            if (!string.IsNullOrEmpty(modelLan?.Content))
                model.Content = modelLan.Content;

            return View(model);
        }

        /// <summary>
        /// 荣誉资质
        /// </summary>
        /// <returns></returns>
        public IActionResult HonorsAndQualifications()
        {
            var model = SingleArticle.FindByCode("HonorsAndQualifications");
            if (model == null)
                return Content(GetResource("文章内容不存在"));

            var modelLan = SingleArticleLan.FindBySIdAndLId(model.Id, WorkingLanguage.Id);

            if (!string.IsNullOrEmpty(modelLan?.Content))
                model.Content = modelLan.Content;

            return View(model);
        }

        /// <summary>
        /// 企业历程
        /// </summary>
        /// <returns></returns>
        public IActionResult EnterpriseHistory()
        {
            var model = SingleArticle.FindByCode("EnterpriseHistory");
            if (model == null)
                return Content(GetResource("文章内容不存在"));

            var modelLan = SingleArticleLan.FindBySIdAndLId(model.Id, WorkingLanguage.Id);

            if (!string.IsNullOrEmpty(modelLan?.Content))
                model.Content = modelLan.Content;

            return View(model);
        }

        /// <summary>
        /// 企业文化
        /// </summary>
        /// <returns></returns>
        public IActionResult CorporateCulture()
        {
            var model = SingleArticle.FindByCode("CorporateCulture");
            if (model == null)
                return Content(GetResource("文章内容不存在"));
            var modelLan = SingleArticleLan.FindBySIdAndLId(model.Id, WorkingLanguage.Id);
            if (!string.IsNullOrEmpty(modelLan?.Content))
                model.Content = modelLan.Content;
            return View(model);
        }
    }
}
