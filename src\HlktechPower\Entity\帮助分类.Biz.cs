﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace HlktechPower.Entity;

public partial class HelpsCategory : DHEntityBase<HelpsCategory>
{
    #region 对象操作
    static HelpsCategory()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(ParentId));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

        // 检查唯一索引
        // CheckExist(method == DataMethod.Insert, nameof(Name), nameof(Level));

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化HelpsCategory[帮助分类]数据……");

    //    var entity = new HelpsCategory();
    //    entity.Name = "abc";
    //    entity.ParentId = 0;
    //    entity.ParentIdList = "abc";
    //    entity.Level = 0;
    //    entity.DisplayOrder = 0;
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化HelpsCategory[帮助分类]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>
    ///是否存在子集
    /// </summary>
    [XmlIgnore, ScriptIgnore]
    public Boolean Subset { get; set; } = false;
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="name">分类名称</param>
    /// <param name="parentId">所属父级Id</param>
    /// <param name="level">当前层级</param>
    /// <param name="start">更新时间开始</param>
    /// <param name="end">更新时间结束</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<HelpsCategory> Search(String? name, Int32 parentId, Int32 level, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (!name.IsNullOrEmpty()) exp &= _.Name == name;
        if (parentId >= 0) exp &= _.ParentId == parentId;
        if (level >= 0) exp &= _.Level == level;
        exp &= _.UpdateTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= SearchWhereByKeys(key);

        return FindAll(exp, page);
    }

    /// <summary>根据当前层级查找</summary>
    /// <param name="level">当前层级</param>
    /// <returns>实体列表</returns>
    public static IList<HelpsCategory> FindAllByLevel(Int32 level)
    {
        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Level == level);

        return FindAll(_.Level == level);
    }

    /// <summary>根据名称查找</summary>
    /// <param name="name">设备DeviceName</param>
    /// <returns>实体对象</returns>
    public static HelpsCategory? FindByName(String name)
    {
        if (name.IsNullOrWhiteSpace()) return null;

        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Name == name);

        return Find(_.Name == name);
    }

    /// <summary>
    /// 根据父级Id和语言Id查找所有子分类
    /// </summary>
    /// <param name="parentId"></param>
    /// <param name="LId"></param>
    /// <returns></returns>
    public static List<HelpsCategory> FindAllByParentIdAndLan(Int32 parentId,Int32 LId)
    {
        var list = FindAllByParentId(parentId).Select(e => new HelpsCategory
        {
            Id = e.Id,
            ParentId = e.ParentId,
            Name = HelpsCategoryLan.FindByHelpsCategory(e,LId).Name,
        }).ToList();

        return list;
    }

    /// <summary>
    /// 根据列表Ids获取列表
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    public static IList<HelpsCategory> FindByIds(String ids)
    {
        if (ids.IsNullOrWhiteSpace()) return [];

        ids = ids.Trim(',');

        if (Meta.Session.Count < 1000)
        {
            return Meta.Cache.FindAll(x => ids.SplitAsInt(",").Contains(x.Id));
        }

        return FindAll(_.Id.In(ids.Split(',')));
    }

    // Select Count(Id) as Id,Category From DH_HelpsCategory Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<HelpsCategory> _CategoryCache = new(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IHelpsCategory ToModel()
    {
        var model = new HelpsCategory();
        model.Copy(this);

        return model;
    }

    #endregion
}
