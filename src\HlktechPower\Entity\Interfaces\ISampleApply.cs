﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechPower.Entity;

/// <summary>样品申请表</summary>
public partial interface ISampleApply
{
    #region 属性
    /// <summary>编号</summary>
    Int64 Id { get; set; }

    /// <summary>项目名称</summary>
    String? ProjectName { get; set; }

    /// <summary>预计月/年用量(个)</summary>
    Int32 Dosage { get; set; }

    /// <summary>邮箱</summary>
    String? Mail { get; set; }

    /// <summary>联系电话</summary>
    String? Phone { get; set; }

    /// <summary>项目联系人</summary>
    String? ContactPerson { get; set; }

    /// <summary>公司名称</summary>
    String? CompanyName { get; set; }

    /// <summary>国家Id</summary>
    Int32 CountryId { get; set; }

    /// <summary>省份Id</summary>
    Int32 ProvinceId { get; set; }

    /// <summary>市级Id</summary>
    Int32 CityId { get; set; }

    /// <summary>详细地址</summary>
    String? Address { get; set; }

    /// <summary>申请主题</summary>
    String? Theme { get; set; }

    /// <summary>需求描述</summary>
    String? Description { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
