﻿using HlktechPower.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife.Data;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using Pek.NCube;
using System.Dynamic;

namespace HlktechPower.Controllers
{
    /// <summary>
    /// 智能选型
    /// </summary>
    public class IntelligentSelectionController : PekBaseControllerX
    {
        public IActionResult Index(Int32 page = 1, Int32 limit = 10)
        {
            dynamic viewModel = new ExpandoObject();

            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = ProductClass._.Sort
            };
            List<object> list = new();
            list.FirstOrDefault();
            viewModel.listProductClass = ProductClass.FindAllByParentId(0).Select(e => new ProductClass { Id = e.Id, Name = ProductClassLan.FindByProductClass(e, WorkingLanguage.Id).Name });
            viewModel.list = ProductClass.Search("", -1, true, DateTime.MinValue, DateTime.MinValue, "", pages);
            viewModel.total = pages.TotalCount;
            viewModel.page = page;
            viewModel.limit = limit;
            return View(viewModel);
        }
    }
}
