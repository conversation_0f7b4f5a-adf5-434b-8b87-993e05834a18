﻿<head>
    <link rel="stylesheet" href="~/css/mySidebar.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="~/css/pageCss/contactInfo.css">
    <script src="~/components/mySidebar.js" defer></script>
</head>

<body style="background-color:white">
    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="../index/index.html">@T("首页")</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="./providerDetail.html">@T("联系信息")</div>
        </div>
        <div class="myContent">
            <!-- 侧边栏 -->
            <div class="mySidebar"></div>
            @Html.Raw(Model.Content)
            @* <div class="myContent-right">
                <div class="search">
                    <div class="h3">联系信息</div>
                </div>
                <div class="layui-tab layui-tab-brief">
                    <ul class="layui-tab-title">
                        <li class="layui-this">公司信息</li>
                        <li>优秀代理</li>
                    </ul>
                    <div class="layui-tab-content">
                        <div class="layui-tab-item layui-show">
                            <div class="companyInfoBox">
                                <ul class="companyInfo">
                                    <li class="bold">深圳市海凌科电子有限公司<span class="hq">(总部)</span></li>
                                    <li class="bold">公司信息：</li>
                                    <li>
                                        地址: : 广东省深圳市龙华区民治街道民乐社区星河WORLDE栋大厦
                                        17层1705、1706、1709A
                                    </li>
                                    <li>
                                        电话: : 0755-23152658；83575155；0755-82557290；
                                        *********1
                                    </li>
                                    <li>传真: 0755-83575189</li>
                                    <li>网址: www.hlktech.com</li>
                                    <li>邮箱: <EMAIL></li>
                                    <li>业务联系QQ : 2851395231 / 2851395234 / 2851395239</li>
                                </ul>
                                <ul class="companyInfo">
                                    <li class="bold">&nbsp;</li>
                                    <li class="bold">账号信息：</li>
                                    <li>户名: 深圳市海凌科电子有限公司</li>
                                    <li>开户行: 中国光大银行深圳东海支行</li>
                                    <li>账号: 3901 0188 0000 86065</li>
                                    <li class="bold">找到我们:</li>
                                    <li>导航: 星河WORLD(二期)</li>
                                    <li>地铁: 雅宝站</li>
                                </ul>
                            </div>
                            <div class="companyInfoBox">
                                <ul class="companyInfo">
                                    <li class="bold">武汉市海凌科电子有限公司<span class="hq">(分部)</span></li>
                                    <li class="bold">公司信息：</li>
                                    <li>地址: : 武汉市武昌区中南国际城C座2单元701-702室</li>
                                    <li>电话:-027-87222329；*********</li>
                                    <li>微信: 17671230390</li>
                                    <li>网址: http://www.gicisky.net</li>
                                    <li>邮箱: <EMAIL></li>
                                    <li>业务联系QQ: 3539478547</li>
                                </ul>
                                <ul class="companyInfo">
                                    <li class="bold">&nbsp;</li>
                                    <li class="bold">账号信息：</li>
                                    <li>户名:武汉市海凌科电子有限公司</li>
                                    <li>开户行:中国农业银行股份有限公司武汉洪山支行</li>
                                    <li>账号:1703 8201040020323</li>
                                    <li class="bold">找到我们:</li>
                                    <li>导航: 中南国际城</li>
                                    <li>地铁:2号线/中南路 4号线/梅苑小区</li>
                                </ul>
                            </div>
                            <div class="companyInfoBox">
                                <ul class="companyInfo">
                                    <li class="bold">益坤泰实业(东莞)有限公司<span class="hq">(分部)</span></li>
                                    <li class="bold">公司信息：</li>
                                    <li>地址: 广东省东莞市东城街道狮长路3号2栋4楼</li>
                                    <li>电话:18898736510</li>
                                </ul>
                            </div>
                            <!-- 地图 -->
                            <div class="map-nav">
                                <div class="btn-group">
                                    <button type="button" class="btn" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                        <b>深圳市海凌科电子有限公司</b><span class="caret"></span>
                                        <img src="../../images/power/place.png" alt="Alternate Text" />
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a href="javascript:;" data-type="1">深圳市海凌科电子有限公司</a></li>
                                        <li><a href="javascript:;" data-type="2">武汉市海凌科电子有限公司</a></li>
                                        <li><a href="javascript:;" data-type="3">益坤泰实业(东莞)有限公司</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div id="allmap"></div>
                        </div>
                        <div class="layui-tab-item">
                            <div class="companyInfoBox">
                                <ul class="companyInfo">
                                    <li class="bold">广东优信电子科技有限公司</li>
                                    <li class="bold">公司信息：</li>
                                    <li>联系人: 彭玲</li>
                                    <li>地址: 深圳市宝安区新安街道海乐社区41区宝乐二街16号3号厂房601</li>
                                    <li>电话: 0755-27892806</li>
                                    <li>网址: www.hlktech.com</li>
                                    <li>邮箱: <EMAIL></li>
                                </ul>
                                <ul class="companyInfo">
                                    <li class="bold">深圳市优铄信电子有限公司</li>
                                    <li class="bold">公司信息：</li>
                                    <li>联系人: 赵先生</li>
                                    <li>开户行: 中国光大银行深圳东海支行</li>
                                    <li>地址: 广东省深圳市福田区华强北街道振华路118号华丽大院西座302</li>
                                    <li>电话:13590188765</li>
                                </ul>
                            </div>
                            <div class="companyInfoBox">
                                <ul class="companyInfo">
                                    <li class="bold">深圳市智联兴微电子有限公司</li>
                                    <li class="bold">公司信息：</li>
                                    <li>联系人: 朱先生</li>
                                    <li>地址: 深圳市福田区华强北街道华航社区中航路4号都会100大厦金都10E</li>
                                    <li>电话:15302749365</li>
                                    <li>邮箱: <EMAIL></li>
                                </ul>
                                <ul class="companyInfo">
                                    <li class="bold">深圳市恒轩微科技有限公司</li>
                                    <li class="bold">公司信息：</li>
                                    <li>联系人: 杨先生115919937953</li>
                                    <li>地址: 深圳市福田区华航社区都会100大厦B座12P</li>
                                    <li>电话: 0755-82720102</li>
                                    <li>邮箱: <EMAIL></li>
                                </ul>
                            </div>
                            <div class="companyInfoBox">
                                <ul class="companyInfo">
                                    <li class="bold">深圳市中银海电子科技有限公司</li>
                                    <li class="bold">公司信息：</li>
                                    <li>联系人: 廖小姐</li>
                                    <li>地址: 广东省深圳市龙华新区龙观大道鸿宇大厦1308</li>
                                    <li>电话:15818539121</li>
                                    <li>邮箱: <EMAIL></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div> *@
        </div>
    </div>
</body>
<script type="text/javascript" src="https://api.tianditu.gov.cn/api?v=4.0&tk=4466b7f03b3ccc26a491b9f08d5ee6ef"></script>
<script>
    const data = [
        {
            text: '@T("联系我们")',
            link: '#',
        },
        {
            text: '@T("联系信息")',
            link: '@Url.Action("Index")',
        },
        {
            text: '@T("建议反馈")',
            link: '@Url.Action("SuggestionAndFeedback")',
        },
    ]
    var map = new T.Map('allmap');
    var zoom = 15;
    map.centerAndZoom(new T.LngLat(114.054824, 22.610507), zoom);
    // 创建自定义图标
    var icon = new T.Icon({
        iconUrl: "http://api.tianditu.gov.cn/img/map/markerA.png",
        iconSize: new T.Point(19, 27),
        iconAnchor: new T.Point(10, 25)
    });
    // 创建标注并设置自定义图标和标题
    var marker = new T.Marker(new T.LngLat(114.053054, 22.607550), { icon: icon });
    // 将标注添加到地图上
    map.addOverLay(marker);
    var latlng = new T.LngLat(114.053054, 22.607550);
    var label = new T.Label({
        text: "深圳市海凌科电子有限公司<br>地址：广东省深圳市龙华区民治街道<br>民乐社区星河WORLD E栋大厦17层1705、1706、1709A",
        position: latlng,
        offset: new T.Point(-9, 0)
    });
    //创建地图文本对象
    map.addOverLay(label);
    var marker = new T.Marker(new T.LngLat(114.324350, 30.536450)); // 创建点
    map.addOverLay(marker);//增加点
    var latlng = new T.LngLat(114.324350, 30.536450);
    var label = new T.Label({
        text: "武汉极思灵创科技有限公司<br>地址：武汉市武昌区中南国际城C座2单元701-702室",
        position: latlng,
        offset: new T.Point(-9, 0)
    });
    //创建地图文本对象
    map.addOverLay(label);
    var marker = new T.Marker(new T.LngLat(113.806557, 23.068383));
    map.addOverLay(marker);//增加点
    var latlng = new T.LngLat(113.806557, 23.068383);
    var label = new T.Label({
        text: "益坤泰实业有限公司<br>地址：广东省东莞市东城街道狮长路3号2栋4楼",
        position: latlng,
        offset: new T.Point(-9, 0)
    });
    //创建地图文本对象
    map.addOverLay(label);
    $(".map-nav .dropdown-menu li").click(function () {
        $(".map-nav .btn-group button b").html($(this).find("a").html());

        var type = $(this).find("a").attr("data-type");
        if (type == "1") {
            map.panTo(new T.LngLat(114.053054, 22.607550));
        }
        if (type == "2") {
            map.panTo(new T.LngLat(114.324350, 30.536450));
        }
        if (type == "3") {
            map.panTo(new T.LngLat(113.806557, 23.068383));
        }
    });
</script>