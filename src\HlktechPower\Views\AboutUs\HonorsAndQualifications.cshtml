﻿<head>
    <!-- 基础css -->
    <link rel="stylesheet" href="~/css/mySidebar.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="~/css/public.css">
    <link rel="stylesheet" href="~/css/pageCss/honor.css">
    <!-- 所有页面组成部分 -->
    <script src="~/components/mySidebar.js" defer></script>
</head>

<body style="background-color:white">
    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="/">@T("首页")</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="#">@T("荣誉资质")</div>
        </div>
        <div class="myContent">
            <!-- 侧边栏 -->
            <div class="mySidebar"></div>
            @Html.Raw(Model.Content)
            @* <div class="introBox">
                <h2>荣誉资质</h2>
                <div class="ompany-intro">
                    <p>
                        在市场竞争日益激烈的今天，拥有创新生产力才是制胜的关键法宝。公司拥有强大的研发团队和技术力量,产品已获得国家多项专利技术，为中国的智
                        能家居行业建立了新的里程碑。
                    </p>
                    <img src="../../images/power/renzheng.png" alt="">
                    <ul class="certificate">
                        <li>
                            <img src="../../images/power/gaoxinqiye.png" alt="">
                            <span> 高新技术企业</span>
                        </li>
                        <li>
                            <img src="../../images/power/zhongxiaoxing.png" alt="">
                            <span> 创新型中小企业</span>
                        </li>
                        <li>
                            <img src="../../images/power/zhuanjingtexin.png" alt="">
                            <span> 专精特新中小企业</span>
                        </li>
                    </ul>
                    <img src="../../images/power/zizhi.png" alt="">
                </div>*@
        </div>
    </div>
</body>
<script>
    const data = [
        { text: '@T("关于我们")', link: '#' },  // 标题
        { text: '@T("企业简介")', link: '@Url.Action("Index")' },
        { text: '@T("荣誉资质")', link: '@Url.Action("HonorsAndQualifications")' },
        { text: '@T("企业历程")', link: '@Url.Action("EnterpriseHistory")' },
        { text: '@T("企业文化")', link: '@Url.Action("CorporateCulture")' },
    ];
</script>
