.companyIng {
  display: flex;
  flex-direction: column;
  gap: 1.4rem; 
}
body{
    background-color:white;
}
.company-item {
  display: flex;
  border-bottom: 1px solid var(--line);
  padding-bottom: 1.4vw; 
}


.item-image {
  flex-shrink: 0; /* Prevent image from shrinking */
  margin-right: 1.4vw; 
}

.item-image img {
  width: 262px;
  height: auto;
  display: block;
}

.item-content {
  flex-grow: 1; /* Allow content to take up remaining space */
}

.item-title {
  font-size: 1.2em;
  margin-bottom: .4rem;
  color: var(--text-color);
}

.item-description {
  font-size: 0.9em;
  color:var(--text-color3); 
  margin-bottom: .7vw;
}

.item-date {
  font-size: 0.8em; 
  color: var(--text-color3); 
  margin-bottom: .7vw;
}

.item-button {
  display: inline-block;
  padding: 8px 15px;
  border: 1px solid var(--line);
  background-color: var(--white);
  color:  var(--text-color);
  text-decoration: none;
  cursor: pointer;
  font-size: 0.9em;
}


#demo-laypage-all{
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}