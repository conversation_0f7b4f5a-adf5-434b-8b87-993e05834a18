﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechPower.Entity;

/// <summary>应用视频翻译表</summary>
public partial interface IAppVideoLan
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>应用视频Id</summary>
    Int32 AId { get; set; }

    /// <summary>所属语言Id</summary>
    Int32 LId { get; set; }

    /// <summary>标题</summary>
    String? Name { get; set; }

    /// <summary>链接</summary>
    String? Url { get; set; }
    #endregion
}
