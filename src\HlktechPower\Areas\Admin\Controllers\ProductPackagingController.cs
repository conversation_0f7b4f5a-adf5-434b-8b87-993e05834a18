﻿using HlktechPower.Dto;
using HlktechPower.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NewLife.Data;
using Org.BouncyCastle.Crypto;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.Collections.Generic;
using System.ComponentModel;
using System.Dynamic;
using System.Xml.Linq;
using XCode.Membership;

namespace HlktechPower.Areas.Admin.Controllers
{
    /// <summary>
    /// 产品封装
    /// </summary>
    [DisplayName("产品封装")]
    [Description("用于产品封装的管理")]
    [AdminArea]
    [DHMenu(95, ParentMenuName = "Product", ParentMenuDisplayName = "产品", ParentMenuUrl = "~/{area}/Product", ParentMenuOrder = 90, ParentVisible = true, CurrentMenuUrl = "~/{area}/ProductPackaging", CurrentMenuName = "ProductPackaging", CurrentIcon = "&#xe652;", LastUpdate = "20250527")]
    public class ProductPackagingController : PekCubeAdminControllerX
    {
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult Index(String name,Int64 productClassId,Int32 page = 1)
        {
            dynamic viewModel = new ExpandoObject();
            var pages = new PageParameter()
            {
                PageIndex = page,
                PageSize = 10,
                RetrieveTotalCount = true,
                Sort = ProductPackaging._.Id,
                Desc = true
            };
            viewModel.list = ProductPackaging.Search(productClassId, name, null, DateTime.MinValue, DateTime.MinValue, "", pages).Select(e => new ProductDto
            {
                Id = e.Id,
                Name = ProductPackagingLan.FindByProductPackaging(e,WorkingLanguage.Id).Name ?? "",
                Enable = e.Enable,
                ProductClassId = e.ProductClassId,
                ProductClassName = e.ProductClassName ?? "",
                CreateTime = e.CreateTime,
            });
            viewModel.Name = name;
            viewModel.ProductClassId = productClassId;
            viewModel.ProductClassList = ProductClass.FindAllByParentId(0).Where(e => e.Enable).Select(e => new ProductClass
            {
                Id = e.Id,
                Name = ProductClassLan.FindByProductClass(e, WorkingLanguage.Id).Name,
            });
            viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { });
            return View(viewModel);
        }

        [DisplayName("新增")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add()
        {
            ViewBag.ProductClassList = ProductClass.FindAllByParentId(0).Where(e=>e.Enable).Select(e=>new ProductClass
            {
                Id = e.Id,
                Name = ProductClassLan.FindByProductClass(e,WorkingLanguage.Id).Name,
            });
            ViewBag.LanguageList = DH.Entity.Language.FindByStatus().OrderBy(e => e.DisplayOrder);
            return View();
        }

        [HttpPost]
        [DisplayName("新增")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add([FromForm]String Name, [FromForm]Int64 ProductClassId)
        {
            if (Name.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("封装形式不能为空") });
            }
            if (ProductClassId <= 0)
            {
                return Prompt(new PromptModel { Message = GetResource("产品分类不能为空") });
            }
            var model = new ProductPackaging()
            {
                Name = Name,
                ProductClassId = ProductClassId,
                Enable = true
            };
            model.Insert();
            var languagelist = DH.Entity.Language.FindByStatus().OrderBy(e => e.DisplayOrder); // 获取全部有效语言
            foreach (var language in languagelist)
            {
                var lan = new ProductPackagingLan
                {
                    Name = (GetRequest($"[{language.Id}].Name")).SafeString().Trim(),
                    LId = language.Id,
                    PId = model.Id,
                };
                lan.Insert();
            }
            return Prompt(new PromptModel { Message = GetResource("新增成功"), IsOk = true, BackUrl = Url.Action("Index") });
        }

        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Update(Int32 Id)
        {
            var model = ProductPackaging.FindById(Id);
            if(model == null)
            {
                return Prompt(new PromptModel { Message = GetResource("数据不存在") });
            }
            ViewBag.ProductClassList = ProductClass.FindAllByParentId(0).Where(e => e.Enable).Select(e => new ProductClass
            {
                Id = e.Id,
                Name = ProductClassLan.FindByProductClass(e, WorkingLanguage.Id).Name,
            });
            ViewBag.LanguageList = DH.Entity.Language.FindByStatus().OrderBy(e => e.DisplayOrder);
            return View(model);
        }

        [HttpPost]
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Update([FromForm] String Name, [FromForm] Int64 ProductClassId, [FromForm]Int32 Id)
        {
            if (Name.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("封装形式不能为空") });
            }
            if (ProductClassId <= 0)
            {
                return Prompt(new PromptModel { Message = GetResource("产品分类不能为空") });
            }
            var model = ProductPackaging.FindById(Id);
            if(model == null)
            {
                return Prompt(new PromptModel { Message = GetResource("数据不存在") });
            }
            model.Name = Name;
            model.ProductClassId = ProductClassId;
            model.Update();
            var languagelist = DH.Entity.Language.FindByStatus().OrderBy(e => e.DisplayOrder); // 获取全部有效语言
            foreach (var language in languagelist)
            {
                var lan = ProductPackagingLan.FindByPIdAndLId(model.Id, language.Id);
                if(lan != null)
                {
                    lan.Name = (GetRequest($"[{language.Id}].Name")).SafeString().Trim();
                    lan.Update();
                }
                else
                {
                    lan = new ProductPackagingLan
                    {
                        Name = (GetRequest($"[{language.Id}].Name")).SafeString().Trim(),
                        LId = language.Id,
                        PId = model.Id,
                    };
                    lan.Insert();
                }
            }
            return Prompt(new PromptModel { Message = GetResource("编辑成功"), IsOk = true, BackUrl = Url.Action("Index") });
        }

        //编辑启用状态
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult UpdateEnable(Int32 Id)
        {
            var res = new DResult();
            var model = ProductPackaging.FindById(Id);
            if (model == null)
            {
                return Prompt(new PromptModel { Message = GetResource("数据不存在") });
            }
            model.Enable = !model.Enable;
            model.Update();
            res.success = true;
            res.msg = GetResource("操作成功");
            return Json(res);
        }

        [DisplayName("删除")]
        [EntityAuthorize(PermissionFlags.Delete)]
        public IActionResult Delete(Int32 Id)
        {
            var res = new DResult();
            ProductPackaging.Delete(ProductPackaging._.Id == Id);
            ProductPackagingLan.Delete(ProductPackagingLan._.PId == Id);
            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);
        }

    }
}
