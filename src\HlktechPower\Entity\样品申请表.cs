﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechPower.Entity;

/// <summary>样品申请表</summary>
[Serializable]
[DataObject]
[Description("样品申请表")]
[BindIndex("IX_DH_SampleApply_ProjectName", false, "ProjectName")]
[BindIndex("IX_DH_SampleApply_CompanyName", false, "CompanyName")]
[BindIndex("IX_DH_SampleApply_ContactPerson", false, "ContactPerson")]
[BindIndex("IX_DH_SampleApply_CountryId", false, "CountryId")]
[BindTable("DH_SampleApply", Description = "样品申请表", ConnName = "Pek", DbType = DatabaseType.None)]
public partial class SampleApply : ISampleApply, IEntity<ISampleApply>
{
    #region 属性
    private Int64 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, false, false, 0)]
    [BindColumn("Id", "编号", "", DataScale = "time")]
    public Int64 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _ProjectName;
    /// <summary>项目名称</summary>
    [DisplayName("项目名称")]
    [Description("项目名称")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("ProjectName", "项目名称", "")]
    public String? ProjectName { get => _ProjectName; set { if (OnPropertyChanging("ProjectName", value)) { _ProjectName = value; OnPropertyChanged("ProjectName"); } } }

    private Int32 _Dosage;
    /// <summary>预计月/年用量(个)</summary>
    [DisplayName("预计月_年用量(个)")]
    [Description("预计月/年用量(个)")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Dosage", "预计月/年用量(个)", "")]
    public Int32 Dosage { get => _Dosage; set { if (OnPropertyChanging("Dosage", value)) { _Dosage = value; OnPropertyChanged("Dosage"); } } }

    private String? _Mail;
    /// <summary>邮箱</summary>
    [DisplayName("邮箱")]
    [Description("邮箱")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("Mail", "邮箱", "")]
    public String? Mail { get => _Mail; set { if (OnPropertyChanging("Mail", value)) { _Mail = value; OnPropertyChanged("Mail"); } } }

    private String? _Phone;
    /// <summary>联系电话</summary>
    [DisplayName("联系电话")]
    [Description("联系电话")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("Phone", "联系电话", "")]
    public String? Phone { get => _Phone; set { if (OnPropertyChanging("Phone", value)) { _Phone = value; OnPropertyChanged("Phone"); } } }

    private String? _ContactPerson;
    /// <summary>项目联系人</summary>
    [DisplayName("项目联系人")]
    [Description("项目联系人")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("ContactPerson", "项目联系人", "")]
    public String? ContactPerson { get => _ContactPerson; set { if (OnPropertyChanging("ContactPerson", value)) { _ContactPerson = value; OnPropertyChanged("ContactPerson"); } } }

    private String? _CompanyName;
    /// <summary>公司名称</summary>
    [DisplayName("公司名称")]
    [Description("公司名称")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("CompanyName", "公司名称", "")]
    public String? CompanyName { get => _CompanyName; set { if (OnPropertyChanging("CompanyName", value)) { _CompanyName = value; OnPropertyChanged("CompanyName"); } } }

    private Int32 _CountryId;
    /// <summary>国家Id</summary>
    [DisplayName("国家Id")]
    [Description("国家Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CountryId", "国家Id", "")]
    public Int32 CountryId { get => _CountryId; set { if (OnPropertyChanging("CountryId", value)) { _CountryId = value; OnPropertyChanged("CountryId"); } } }

    private Int32 _ProvinceId;
    /// <summary>省份Id</summary>
    [DisplayName("省份Id")]
    [Description("省份Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ProvinceId", "省份Id", "")]
    public Int32 ProvinceId { get => _ProvinceId; set { if (OnPropertyChanging("ProvinceId", value)) { _ProvinceId = value; OnPropertyChanged("ProvinceId"); } } }

    private Int32 _CityId;
    /// <summary>市级Id</summary>
    [DisplayName("市级Id")]
    [Description("市级Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CityId", "市级Id", "")]
    public Int32 CityId { get => _CityId; set { if (OnPropertyChanging("CityId", value)) { _CityId = value; OnPropertyChanged("CityId"); } } }

    private String? _Address;
    /// <summary>详细地址</summary>
    [DisplayName("详细地址")]
    [Description("详细地址")]
    [DataObjectField(false, false, true, 500)]
    [BindColumn("Address", "详细地址", "")]
    public String? Address { get => _Address; set { if (OnPropertyChanging("Address", value)) { _Address = value; OnPropertyChanged("Address"); } } }

    private String? _Theme;
    /// <summary>申请主题</summary>
    [DisplayName("申请主题")]
    [Description("申请主题")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("Theme", "申请主题", "")]
    public String? Theme { get => _Theme; set { if (OnPropertyChanging("Theme", value)) { _Theme = value; OnPropertyChanged("Theme"); } } }

    private String? _Description;
    /// <summary>需求描述</summary>
    [DisplayName("需求描述")]
    [Description("需求描述")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("Description", "需求描述", "text")]
    public String? Description { get => _Description; set { if (OnPropertyChanging("Description", value)) { _Description = value; OnPropertyChanged("Description"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISampleApply model)
    {
        Id = model.Id;
        ProjectName = model.ProjectName;
        Dosage = model.Dosage;
        Mail = model.Mail;
        Phone = model.Phone;
        ContactPerson = model.ContactPerson;
        CompanyName = model.CompanyName;
        CountryId = model.CountryId;
        ProvinceId = model.ProvinceId;
        CityId = model.CityId;
        Address = model.Address;
        Theme = model.Theme;
        Description = model.Description;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "ProjectName" => _ProjectName,
            "Dosage" => _Dosage,
            "Mail" => _Mail,
            "Phone" => _Phone,
            "ContactPerson" => _ContactPerson,
            "CompanyName" => _CompanyName,
            "CountryId" => _CountryId,
            "ProvinceId" => _ProvinceId,
            "CityId" => _CityId,
            "Address" => _Address,
            "Theme" => _Theme,
            "Description" => _Description,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToLong(); break;
                case "ProjectName": _ProjectName = Convert.ToString(value); break;
                case "Dosage": _Dosage = value.ToInt(); break;
                case "Mail": _Mail = Convert.ToString(value); break;
                case "Phone": _Phone = Convert.ToString(value); break;
                case "ContactPerson": _ContactPerson = Convert.ToString(value); break;
                case "CompanyName": _CompanyName = Convert.ToString(value); break;
                case "CountryId": _CountryId = value.ToInt(); break;
                case "ProvinceId": _ProvinceId = value.ToInt(); break;
                case "CityId": _CityId = value.ToInt(); break;
                case "Address": _Address = Convert.ToString(value); break;
                case "Theme": _Theme = Convert.ToString(value); break;
                case "Description": _Description = Convert.ToString(value); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static SampleApply? FindById(Int64 id)
    {
        if (id < 0) return null;

        return Find(_.Id == id);
    }

    /// <summary>根据项目名称查找</summary>
    /// <param name="projectName">项目名称</param>
    /// <returns>实体列表</returns>
    public static IList<SampleApply> FindAllByProjectName(String? projectName)
    {
        if (projectName == null) return [];

        return FindAll(_.ProjectName == projectName);
    }

    /// <summary>根据公司名称查找</summary>
    /// <param name="companyName">公司名称</param>
    /// <returns>实体列表</returns>
    public static IList<SampleApply> FindAllByCompanyName(String? companyName)
    {
        if (companyName == null) return [];

        return FindAll(_.CompanyName == companyName);
    }

    /// <summary>根据项目联系人查找</summary>
    /// <param name="contactPerson">项目联系人</param>
    /// <returns>实体列表</returns>
    public static IList<SampleApply> FindAllByContactPerson(String? contactPerson)
    {
        if (contactPerson == null) return [];

        return FindAll(_.ContactPerson == contactPerson);
    }

    /// <summary>根据国家Id查找</summary>
    /// <param name="countryId">国家Id</param>
    /// <returns>实体列表</returns>
    public static IList<SampleApply> FindAllByCountryId(Int32 countryId)
    {
        if (countryId < 0) return [];

        return FindAll(_.CountryId == countryId);
    }
    #endregion

    #region 数据清理
    /// <summary>清理指定时间段内的数据</summary>
    /// <param name="start">开始时间。未指定时清理小于指定时间的所有数据</param>
    /// <param name="end">结束时间</param>
    /// <returns>清理行数</returns>
    public static Int32 DeleteWith(DateTime start, DateTime end)
    {
        return Delete(_.Id.Between(start, end, Meta.Factory.Snow));
    }
    #endregion

    #region 字段名
    /// <summary>取得样品申请表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>项目名称</summary>
        public static readonly Field ProjectName = FindByName("ProjectName");

        /// <summary>预计月/年用量(个)</summary>
        public static readonly Field Dosage = FindByName("Dosage");

        /// <summary>邮箱</summary>
        public static readonly Field Mail = FindByName("Mail");

        /// <summary>联系电话</summary>
        public static readonly Field Phone = FindByName("Phone");

        /// <summary>项目联系人</summary>
        public static readonly Field ContactPerson = FindByName("ContactPerson");

        /// <summary>公司名称</summary>
        public static readonly Field CompanyName = FindByName("CompanyName");

        /// <summary>国家Id</summary>
        public static readonly Field CountryId = FindByName("CountryId");

        /// <summary>省份Id</summary>
        public static readonly Field ProvinceId = FindByName("ProvinceId");

        /// <summary>市级Id</summary>
        public static readonly Field CityId = FindByName("CityId");

        /// <summary>详细地址</summary>
        public static readonly Field Address = FindByName("Address");

        /// <summary>申请主题</summary>
        public static readonly Field Theme = FindByName("Theme");

        /// <summary>需求描述</summary>
        public static readonly Field Description = FindByName("Description");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得样品申请表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>项目名称</summary>
        public const String ProjectName = "ProjectName";

        /// <summary>预计月/年用量(个)</summary>
        public const String Dosage = "Dosage";

        /// <summary>邮箱</summary>
        public const String Mail = "Mail";

        /// <summary>联系电话</summary>
        public const String Phone = "Phone";

        /// <summary>项目联系人</summary>
        public const String ContactPerson = "ContactPerson";

        /// <summary>公司名称</summary>
        public const String CompanyName = "CompanyName";

        /// <summary>国家Id</summary>
        public const String CountryId = "CountryId";

        /// <summary>省份Id</summary>
        public const String ProvinceId = "ProvinceId";

        /// <summary>市级Id</summary>
        public const String CityId = "CityId";

        /// <summary>详细地址</summary>
        public const String Address = "Address";

        /// <summary>申请主题</summary>
        public const String Theme = "Theme";

        /// <summary>需求描述</summary>
        public const String Description = "Description";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
