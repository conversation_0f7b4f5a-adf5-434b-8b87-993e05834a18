﻿@using HlktechPower.Entity
@{
    var selectCityText = T("请选择市");
}
<head>
    <link rel="stylesheet" href="~/css/mySidebar.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="~/css/pageCss/suggest.css">
    <script src="~/components/mySidebar.js" defer></script>
</head>

<body style="background-color:white">
    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="/">@T("首页")</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="#">@T("应用支持")</div>
        </div>
        <div class="myContent">
            <!-- 侧边栏 -->
            <div class="mySidebar"></div>
            <div class="myContent-right">
                <div class="search">
                    <div class="h3">@T("建议反馈")</div>
                </div>
                <p class="tip">@T("为了更及时的处理您的问题并回复，请您选择提交问题涉及的领域")</p>
                <form class="layui-form">
                    <div class="layui-input-block problem">
                        <div class="layui-form-item">
                            <input type="checkbox" name="problemType" value="0" title="@T("网站问题")" lay-filter="problemType">
                            <input type="checkbox" name="problemType" value="1" title="@T("产品问题")" checked lay-verify="required"
                            lay-filter="problemType">
                            <input type="checkbox" name="problemType" value="2" title="@T("技术问题")" lay-filter="problemType">
                            <input type="checkbox" name="problemType" value="3" title="@T("其他问题")" lay-filter="problemType">
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("姓名")</label>
                            <div class="layui-input-block">
                                <input type="text" name="Name" lay-verify="required" placeholder="@T("请输入")" autocomplete="off"
                                class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("邮箱")</label>
                            <div class="layui-input-block">
                                <input type="text" name="Mail" lay-verify="required|email" placeholder="@T("请输入")" autocomplete="off"
                                class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("手机")</label>
                            <div class="layui-input-block">
                                <input type="tel" name="Phone" lay-verify="required|phone" placeholder="@T("请输入")" autocomplete="off" lay-reqtext="@T("请填写手机号")"
                                lay-affix="clear" class="layui-input demo-phone">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6"></div>
                    <div class="layui-form-item">
                        <div class="layui-col-md6">
                            <label class="layui-form-label">@T("省份")</label>
                            <div class="layui-input-block">
                                <select name="ProvinceCode" lay-verify="required" lay-filter="province">
                                    <option value="" selected>@T("请选择省")</option>
                                    @foreach (Regions item in Model.provincelist)
                                    {
                                        <option value="@item.AreaCode">@item.Name</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <label class="layui-form-label">@T("城市")</label>
                            <div class="layui-input-block">
                                <select name="CityCode" lay-verify="required" id="quiz2">
                                    <option value="">@T("请选择市")</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("产品类型")</label>
                        <div class="layui-input-block pic">
                            @{
                                var i = 2;
                                foreach (ProductClass item in Model.productlist)
                                {
                                    <div class=@(i % 2 == 0 ? "layui-col-md6":"layui-col-md5")>
                                        <input type="checkbox" name="productType" value="@item.Id" title="@item.Name"
                                        lay-filter="productType">
                                        <img src="@DHSetting.Current.CurDomainUrl/@item.Image" alt="">
                                    </div>
                                    i++;
                                }
                            }
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">@T("问题或建议")</label>
                        <div class="layui-input-block">
                            <textarea id="myTextarea" placeholder="@T("请输入")" lay-verify="required" class="layui-textarea" name="Content"></textarea>
                        </div>
                    </div>
@*                     <div class="layui-form-item">
                        <label class="layui-form-label">@T("验证码")</label>
                        <div class="layui-input-inline">
                            <div class="layui-row">
                                <div class="layui-col-xs7">
                                    <div class="layui-input-wrap">
                                        <input type="text" name="captcha" value="" lay-verify="required" placeholder="@T("请输入")" lay-reqtext="@T("请填写验证码")"
                                               autocomplete="off" class="layui-input" lay-affix="clear">
                                    </div>
                                </div>
                                <div class="layui-col-xs5">
                                    <div style="margin-left: 10px;">
                                        <img src="https://www.oschina.net/action/user/captcha"
                                             onclick="this.src='https://www.oschina.net/action/user/captcha?t='+ new Date().getTime();">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> *@
                    <div class="layui-col-xs4">&nbsp;</div>
                    <div class="layui-col-xs4">
                        <div class="layui-form-item">
                            <button class="layui-btn layui-bg-blue" lay-submit lay-filter="demo1">@T("确认")</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
<script>
    const data = [
           {
               text: '@T("联系我们")',
               link: '#',
           },
           {
               text: '@T("联系信息")',
               link: '@Url.Action("Index")',
           },
           {
               text: '@T("建议反馈")',
               link: '@Url.Action("SuggestionAndFeedback")',
           },
       ]

    document.addEventListener('DOMContentLoaded', function () {
      layui.use(['form', 'laydate', 'util'], function () {
        var form = layui.form;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var util = layui.util;


        // checkbox 改造成单选功能 - 问题类型 (点击自身不能取消)
        form.on('checkbox(problemType)', function (data) {
          // console.log('问题类型:', data);
          if (data.elem.checked) {
            // 取消同组其他checkbox的选中状态
            $('input[name="problemType"]').not(data.elem).prop('checked', false);
            // 重新渲染表单
            form.render('checkbox');

            // 根据选择的问题类型显示/隐藏产品类型部分
            if (data.value != '1') { // 网站问题
              $('.layui-form-item:has(input[name="productType"])').hide();
            } else {
              $('.layui-form-item:has(input[name="productType"])').show();
            }
          } else {
            // 点击元素本身防止取消选择
            $(data.elem).prop('checked', true);
            form.render('checkbox');
          }
        });


        // 产品类型 - 单选功能
        form.on('checkbox(productType)', function (data) {
          console.log('产品类型:', data);
          if (data.elem.checked) {
            // 取消同组其他checkbox的选中状态
            $('input[name="productType"]').not(data.elem).prop('checked', false);
            // 重新渲染表单
            form.render('checkbox');
          } else {
            // 点击元素本身防止取消选择
            $(data.elem).prop('checked', true);
            form.render('checkbox');
          }
        });

        // 监听省份选择变化
         form.on('select(province)', function (data) {
            var provinceValue = data.value;
            $.getJSON('@Url.Action("QueryAllCity", "AppSupport")',{code:provinceValue},function(res){
                if(res.success){
                    $("#quiz2").empty();
                    $("#quiz2").append('<option value="0">@selectCityText</option>');
                    for(var i = 0; i < res.data.length; i++) {
                        $("#quiz2").append('<option value="'+res.data[i].AreaCode+'">'+res.data[i].Name+'</option>');
                    }
                    layui.form.render("select");
                }else{
                    layui.layer.msg(res.msg);
                }
            })
         });

        // 提交事件
        form.on('submit(demo1)', function (data) {
          var field = data.field; // 获取表单字段值

          // 产品类型不提交
          if(field.problemType != '1'){
            delete field.productType
          }

          // 显示填写结果
          console.log('field :>> ', field);

          // 此处可执行 Ajax 等操作
          // …

          $.post('@Url.Action("AddSuggestionAndFeedback")',field,function(res){
              if(res.success){
                  window.location.href = '@Url.Action("Index")';
              }else{
                  layui.layer.msg(res.msg);
              }
          })

          return false; // 阻止默认 form 跳转
        });
      });
    });
</script>