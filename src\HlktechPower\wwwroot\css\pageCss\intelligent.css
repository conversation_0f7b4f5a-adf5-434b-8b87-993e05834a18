body {
  background-color: var(--bg);
}

.intelligent {
  width: 88vw;
  display: flex;
  margin: 0 auto;
  gap: 1vw;
  margin-top: 2vw;
  align-content: center;
  justify-content: center;

}

.intelligent-left {
  width: 24%;
  height: 100%;
  border: 1px solid var(--line);
  background-color: #fff;
  min-height: 80vh;
  padding-top: 1vw;
  box-shadow: 2px 2px 15px 0px #00000026;
}

.intelligent-right {
  background-color: #fff;
  padding: 0 .5vw;
  box-shadow: 2px 2px 15px 0px #00000026;
  flex: 1;
  border-radius: 5px;
}

.layui-tab .layui-tab-title {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.layui-tab .layui-tab-title li {
  min-width: 80px;
  padding: 0 1.8vw;
  border-radius: 5px;
  color: var(--white);
  background: linear-gradient(352.7deg, #BEBEBE 3.58%, #9E9E9E 92.29%);
}

.layui-tab-brief>.layui-tab-title .layui-this {
  color: var(--white);
  background: linear-gradient(352.7deg, #FF410B 3.58%, #FF8E27 92.29%);
}

.layui-tab:nth-of-type(2) .layui-tab-title li {
  background: var(--white);
  color: var(--text-color);
  border: 1px solid var(--line);
  border-radius: 0;
}


.layui-tab-brief:nth-of-type(2)>.layui-tab-title .layui-this {
  background: var(--myBlue);
  color: var(--white);

}

.layui-tab .layui-tab-title:after {
  border: none;
}

.layui-tab-brief>.layui-tab-more li.layui-this:after,
.layui-tab-brief>.layui-tab-title .layui-this:after {
  border: none;
}

.mind,
.manual p {
  text-align: center;
  color: var(--text-color3);
}

.manual p {
  margin-bottom: .5vw;
}

.type {
  width: 8vw;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: .7vw;
  margin: .5vw auto;
  position: relative;
  background-color: #fff;
  font-size: 20px;

}

.type::before,
.type::after {
  content: '';
  position: absolute;
  height: 1px;
  background-color: var(--line);
  /* 浅灰色 */
  top: 50%;
  transform: translateY(-50%);
  width: 5vw;
  /* 线的长度 */
}

.type::before {
  left: -5.5vw;
  /* 左侧线的位置（包括0.5vw的间隙） */
}

.type::after {
  right: -5.5vw;
  /* 右侧线的位置（包括0.5vw的间隙） */
}

#demoTabsHeader .layui-btn.layui-this {
  border-color: #eee;
  color: unset;
  background: none;
}

#demoTabsHeader {
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 10px;
  padding: 0 1vw;
  height: 100%;

}

#demoTabsHeader li {
  width: 6vw;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0px 1.4vw;
}



.layui-tab-bar,
#demoTabsBody .test-item {
  display: none;
}

.layui-input-block {
  margin-left: 85px;
}

.layui-form-label {
  padding: 9px 0px;
}

.acdc .layui-input,
.dcdc .layui-input,
.manual .my-input {
  border: none;
  border-bottom: 1px solid var(--line);
}

.layui-input:focus,
.layui-textarea:focus {
  border-color: none !important;
  box-shadow: 0 0 0 0px rgba(255, 255, 255, 0.08);
}

.layui-input-suffix {
  color: var(--text-color);
  background: var(--white);
  border: none;
  border-bottom: 1px solid var(--line);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px
}

.layui-form-item {
  margin-bottom: 0;
}

.demo-login-container .inp .layui-input-block {
  margin-left: 0px;
}

.inp .layui-input-block:first-child {
  margin-left: 22px;

}


.layui-form-mid {
  margin-left: 10px;
  margin-right: -10px;
}

.del,
.layui-input-prefix .layui-icon,
.layui-input-split .layui-icon,
.layui-input-suffix .layui-icon {
  color: red;
  cursor: pointer;
}

.Vout {
  width: 33% !important;
  margin-right: 4cap;
  margin-left: 2px;
}


.tip {
  text-align: center;
  margin-bottom: 0.2vw;
  background-color: var(--myBlueBg);
  color: var(--myBlue);
  font-size: 12px;
  margin-top: 2px;
  width: 54%;
  margin-left: 38%;
  padding: 2px 6px;

}

.layui-input:focus,
.layui-textarea:focus {
  border-color: var(--line) !important;

}

.layui-form-item:last-child button:nth-child(1) {
  background: linear-gradient(352.7deg, #FF410B 3.58%, #FF8E27 92.29%);
  border-radius: 5px;
  width: 32%;
  margin-left: 10%;
  margin-top: 1vw;
}

.layui-form-item:last-child button:nth-child(2) {
  background: linear-gradient(352.7deg, #0E6AF2 3.58%, #63A2FF 92.29%);
  border-radius: 5px;
  width: 32%;
  margin-left: 12%;
  margin-top: 1vw;
}

.pics {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-left: 6%;
  gap: .5vw;
}

.pics li {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2px .6vw;
  border: 1px solid var(--line);
  cursor: pointer;
  box-sizing: border-box;
  transition: all 0.3s ease;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);

}

.pics img {
  width: 3vw;
  height: 3vw;
}

.pics li.act {
  border: 1px solid var(--myBlue) !important;
  background-color: rgba(47, 124, 239, 0.05);
  box-shadow: 0 0 2px rgb(23, 80, 155) !important;

}

.pics:nth-of-type(2) li {
  min-width: 4.5vw;
}

.dcdc .pics li {
  min-width: 4.5vw;
  padding: 2px .3vw;
}


.myLabel {
  margin-left: 22px;
  margin-bottom: 6px;
}

.use {
  margin-top: 9px;
  margin-left: 22px;
  margin-bottom: 9px;
}

.manual .layui-input-block {
  margin-left: 22px !important;
}

.manual .first {
  margin-left: 85px !important;
}

.manual .myLabel .layui-input {
  border: 1px solid var(--line);
}


.layui-form-item .myInline {
  width: 220px;
}

.manual .layui-input {
  border-color: #eee;
}

.manual .last {
  display: flex;
  padding: 0 5%;
  align-items: center;
}

.manual .layui-form-mid {
  width: 2vw;

}

.manual .bottom {
  margin-bottom: 0;
  padding: 9px 0;
}

.manual .layui-form-item:last-child button:nth-child(1) {
  margin-left: 32%;
  margin-top: 1.2vw;
}

#demo-laypage-all {
  text-align: right;
  margin-right: 20px;
  color: var(--myBlue);
  cursor: pointer;
  font-size: 15px;
}

.tableBox {
  border: 1px solid var(--line);
  border-radius: 5px;
  overflow: hidden;
  margin: 10px 0 20px;
}

.layui-table {
  border-radius: 10px;
  overflow: hidden;
  margin: 0;
}

.layui-table thead {
  background-color: var(--bg6) !important;
  text-align: center;
}



.layui-table th {
  font-weight: 400;
  color: var(--text-color);
}

.layui-table th,
.layui-table td {
  padding: 5px !important;
  width: 50px;
  text-align: center;
  font-size: 14px;
  line-height: normal;
}



.renzhengBox {
  width: 76px !important;
  overflow: hidden;
}

.renzhengBox img {
  position: absolute;
  top: 50%;
  left: 106px;
  transform: scale(5);
  filter: grayscale(100%) brightness(0);
}

.paging {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 6vw 0 28vw;
  margin-top: 1vw;
  margin-bottom: 2vw;

}

.paging .warn {
  font-size: 12px;
  color: var(--text-color3);
}

.paging .warn::before {
  content: '*';
  position: relative;
  color: red;
  left: 0px;
}

.product-img {
  width: 38px;
  height: 38px;
}

.layui-laypage button,
.layui-laypage input {
  border: 1px solid #fff;
}

.layui-laypage .layui-laypage-skip {
  padding-left: 2px;
}