﻿using DH.Entity;
using HlktechPower.Entity;
using Microsoft.AspNetCore.Mvc;
using Pek;
using Pek.Models;
using Pek.NCube;
using System.Dynamic;
using System.Runtime.CompilerServices;

namespace HlktechPower.Controllers
{
    /// <summary>
    /// 应用支持
    /// </summary>
    public class AppSupportController : PekBaseControllerX
    {
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 资料下载
        /// </summary>
        /// <returns></returns>
        public IActionResult DataDownload(String key,Int32 dataFileClass = -1)
        {
            dynamic viewModel = new ExpandoObject();

            viewModel.list0 = DataFile.FindAllByDataFileClass(0).Where(e => e.Show == true).OrderBy(e => e.Sort).Select(e => new DataFile
            {
                Id = e.Id,
                Name = DataFileLan.FindByDataFile(e, WorkingLanguage.Id).Name,
                Url = DataFileLan.FindByDataFile(e, WorkingLanguage.Id).Url,
            }).WhereIf(dataFileClass == 0 && key.IsNotNullAndWhiteSpace(), e => e.Name != null && e.Name.Contains(key)).ToList();

            viewModel.list1 = DataFile.FindAllByDataFileClass(1).Where(e => e.Show == true).OrderBy(e => e.Sort).Select(e => new DataFile
            {
                Id = e.Id,
                Name = DataFileLan.FindByDataFile(e, WorkingLanguage.Id).Name,
                Url = DataFileLan.FindByDataFile(e, WorkingLanguage.Id).Url,
            }).WhereIf(dataFileClass == 1 && key.IsNotNullAndWhiteSpace(), e => e.Name != null && e.Name.Contains(key)).ToList();

            viewModel.list2 = DataFile.FindAllByDataFileClass(2).Where(e => e.Show == true).OrderBy(e => e.Sort).Select(e => new DataFile
            {
                Id = e.Id,
                Name = DataFileLan.FindByDataFile(e, WorkingLanguage.Id).Name,
                Url = DataFileLan.FindByDataFile(e, WorkingLanguage.Id).Url,
            }).WhereIf(dataFileClass == 2 && key.IsNotNullAndWhiteSpace(), e => e.Name != null && e.Name.Contains(key)).ToList();

            viewModel.list3 = DataFile.FindAllByDataFileClass(3).Where(e => e.Show == true).OrderBy(e => e.Sort).Select(e => new DataFile
            {
                Id = e.Id,
                Name = DataFileLan.FindByDataFile(e, WorkingLanguage.Id).Name,
                Url = DataFileLan.FindByDataFile(e, WorkingLanguage.Id).Url,
            }).WhereIf(dataFileClass == 3 && key.IsNotNullAndWhiteSpace(), e => e.Name != null && e.Name.Contains(key)).ToList();

            viewModel.key = key;
            return View(viewModel);
        }

        /// <summary>
        /// 修改资料下载量
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateDownloadVolume(Int32 Id)
        {
            DResult res = new();
            var model = DataFile.FindById(Id);
            if(model == null)
            {
                res.msg = GetResource("资料文件不存在");
                return Json(res);
            }
            model.DownloadVolume = model.DownloadVolume + 1;
            model.Update();

            res.success = true;
            return Json(res);
        }

        /// <summary>
        /// 应用视频
        /// </summary>
        /// <returns></returns>
        public IActionResult AppVideo(String key)
        {
            dynamic viewModel = new ExpandoObject();

            viewModel.list = Entity.AppVideo.FindAll(Entity.AppVideo._.Show == true).Select(e => new AppVideo
            {
                Id = e.Id,
                Name = AppVideoLan.FindByAppVideo(e,WorkingLanguage.Id).Name,
                Url = AppVideoLan.FindByAppVideo(e, WorkingLanguage.Id).Url,
                ViewCount = e.ViewCount,
            }).WhereIf(!key.IsNullOrWhiteSpace(), e => e.Name != null && e.Name.Contains(key)).OrderBy(e => e.Sort).ToList();

            viewModel.key = key;

            return View(viewModel);
        }

        [HttpPost]
        public IActionResult UpdateViewCount(Int32 Id)
        {
            DResult res = new();
            var model = Entity.AppVideo.FindById(Id);
            if (model == null)
            {
                res.msg = GetResource("应用视频不存在");
                return Json(res);
            }
            model.ViewCount = model.ViewCount + 1;
            model.Update();

            res.success = true;
            return Json(res);
        }

        /// <summary>
        /// 常见问题
        /// </summary>
        /// <returns></returns>
        public IActionResult CommonProblem(String key,Int64 pId)
        {
            dynamic viewModel = new ExpandoObject();

            var list = ProductClass.FindAllByParentIdLan(0, WorkingLanguage.Id).Where(e => e.Enable == true).OrderBy(e => e.Sort).ToList();

            if(pId > 0 && !key.IsNullOrWhiteSpace())
            {
                var model = list.FirstOrDefault(e => e.Id == pId);
                if(model != null)
                {
                    model.Problems = model.Problems.Where(e => e.Content != null && e.Content.Contains(key)).ToList();
                }
            }

            viewModel.list = list;
            viewModel.key = key;
            viewModel.pId = pId;

            return View(viewModel);
        }

        /// <summary>
        /// 样品申请
        /// </summary>
        /// <returns></returns>
        public IActionResult SampleApply()
        {
            dynamic viewModel = new ExpandoObject();
            var list = ProductClass.FindAllByLevel(3).Where(e => e.Enable).OrderBy(e => e.Id).Select(e => new ProductClass
            {
                Id = e.Id,
                Name = ProductClassLan.FindByProductClass(e, WorkingLanguage.Id).Name,
            });
            viewModel.classlist = list;
            viewModel.countrylist = Country.FindAll().Select(e => new Country
            {
                Id = e.Id,
                TwoLetterIsoCode = e.TwoLetterIsoCode,
                Name = CountryLan.FindByCIdAndLId(e.Id, WorkingLanguage.Id)?.Name ?? e.Name,
            });
            return View(viewModel);
        }

        /// <summary>
        /// 根据国家二字代码查询所有省份
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public IActionResult QueryAllProvince(String code)
        {
            DResult res = new();

            if (code.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("国家代码不能为空");
                return Json(res);
            }
            var list = Regions.FindAllByCIdAndLevel(code,0)!.Select(e => new Regions
            {
                Id = e.Id,
                AreaCode = e.AreaCode,
                Name = RegionsLan.FindByRIdAndLId(e.Id, WorkingLanguage.Id)?.Name ?? e.Name,
            });
            res.success = true;
            res.data = list;
            return Json(res);
        }

        /// <summary>
        /// 根据省份code查找城市
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public IActionResult QueryAllCity(String code)
        {
            DResult res = new();
            if (code.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("省份代码不能为空");
                return Json(res);
            }
            var list = Regions.FindAllByParentCode(code)!.Select(e => new Regions
            {
                Id = e.Id,
                AreaCode = e.AreaCode,
                Name = RegionsLan.FindByRIdAndLId(e.Id, WorkingLanguage.Id)?.Name ?? e.Name,
            });
            res.success = true;
            res.data = list;
            return Json(res);
        }

        /// <summary>
        /// 添加样品申请
        /// </summary>
        /// <param name="productModel">产品型号</param>
        /// <param name="price_min">产品数量</param>
        /// <param name="projectName">项目名称</param>
        /// <param name="dosageNum">预计月/年用量 (个)</param>
        /// <param name="username">项目联系人</param>
        /// <param name="company">公司名称</param>
        /// <param name="phone">联系电话</param>
        /// <param name="email">邮箱</param>
        /// <param name="quiz1">国家</param>
        /// <param name="quiz2">省份</param>
        /// <param name="quiz3">市</param>
        /// <param name="address">详细地址</param>
        /// <param name="theme">申请主题</param>
        /// <param name="remark">需求描述</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddSampleApply([FromForm]Int64[] productModel,[FromForm]Int32[] price_min,[FromForm] String projectName, [FromForm] Int32 dosageNum, [FromForm] String username, [FromForm] String company, [FromForm] String phone, [FromForm] String email, [FromForm] String quiz1, [FromForm] String quiz2, [FromForm] String quiz3, [FromForm] String address, [FromForm] String theme, [FromForm] String remark)
        {
            DResult res = new();

            if(productModel.Length == 0)
            {
                res.msg = GetResource("产品型号不能为空");
                return Json(res);
            }
            if (price_min.Length == 0)
            {
                res.msg = GetResource("产品型号数量不能为空");
                return Json(res);
            }
            if (projectName.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("项目名称不能为空");
                return Json(res);
            }
            if (dosageNum <= 0)
            {
                res.msg = $"{GetResource("预计")}{GetResource("月")}/{GetResource("年")}{GetResource("用量")}({GetResource("个")}){GetResource("不能为空")}";
                return Json(res);
            }
            if (username.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("项目联系人不能为空");
                return Json(res);
            }
            if (company.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("公司名称不能为空");
                return Json(res);
            }
            if (phone.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("联系电话不能为空");
                return Json(res);
            }
            if (email.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("邮箱不能为空");
                return Json(res);
            }
            if (quiz1.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("国家不能为空");
                return Json(res);
            }
            if(quiz1 == "CN")
            {
                if (quiz2.IsNullOrWhiteSpace())
                {
                    res.msg = GetResource("省份不能为空");
                    return Json(res);
                }
                if (quiz3.IsNullOrWhiteSpace())
                {
                    res.msg = GetResource("城市不能为空");
                    return Json(res);
                }
            }
            if (address.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("详细地址不能为空");
                return Json(res);
            }
            if (theme.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("申请主题不能为空");
                return Json(res);
            }
            if (remark.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("需求描述不能为空");
                return Json(res);
            }

            var modelCountry = Country.FindByTwoLetterIsoCode(quiz1);
            if(modelCountry == null)
            {
                res.msg = GetResource("国家不存在");
                return Json(res);
            }

            if(productModel.Length != price_min.Length)
            {
                res.msg = GetResource("产品型号错误");
                return Json(res);
            }

            using (var tran = Entity.SampleApply.Meta.CreateTrans())
            {

                var modelSampleApply = new SampleApply
                {
                    ProjectName = projectName,
                    Dosage = dosageNum,
                    ContactPerson = username,
                    CompanyName = company,
                    Phone = phone,
                    Mail = email,
                    CountryId = modelCountry.Id,
                    ProvinceId = Regions.FindByAreaCode(quiz2)?.Id ?? 0,
                    CityId = Regions.FindByAreaCode(quiz3)?.Id ?? 0,
                    Address = address,
                    Theme = theme,
                    Description = remark,
                };
                modelSampleApply.Insert();

                for (int i = 0; i < productModel.Length; i++)
                {
                    var pId = productModel[i];
                    var num = price_min[i];

                    var modelProductClass1 = ProductClass.FindById(pId);
                    if (modelProductClass1 == null)
                    {
                        res.msg = GetResource("产品型号不存在");
                        return Json(res);
                    }
                    var modelProductClass2 = ProductClass.FindById(modelProductClass1.ParentId);
                    if (modelProductClass2 == null)
                    {
                        res.msg = GetResource("产品型号不存在");
                        return Json(res);
                    }

                    var modelSampleApplyEx = new SampleApplyEx()
                    {
                        SId = modelSampleApply.Id,
                        PId1 = modelProductClass2.ParentId,
                        PId2 = modelProductClass1.ParentId,
                        PId3 = modelProductClass1.Id,
                        Number = num,
                    };
                    modelSampleApplyEx.Insert();
                }

                tran.Commit();
            }

            res.success = true;
            res.msg = GetResource("样品申请提交成功");
            return Json(res);
        }

        /// <summary>
        /// 成品检测报告
        /// </summary>
        /// <returns></returns>
        public IActionResult ProductInspectionReport()
        {
            return View();
        }
    }
}
