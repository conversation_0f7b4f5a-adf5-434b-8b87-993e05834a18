/**
 * 产品分类悬停事件处理模块
 * 负责处理产品分类的初始化、悬停事件和内容更新
 */

// 产品分类数据配置
const productCategory_onePageList = [
    {
        title: 'AC/DC(2-5W)隔离模块',
        id: 1,
        children: [
            {
                title: '2w',
                id: 11,
                children: [
                    {
                        title: '2W系列',
                        id: 111
                    }
                ]
            },
            {
                title: '3w',
                id: 12,
                children: [
                    {
                        title: 'LS03系列',
                        id: 121
                    },
                    {
                        title: 'LD03系列',
                        id: 122
                    },
                    {
                        title: '3W系列',
                        id: 123
                    },
                    {
                        title: '3W-B系列',
                        id: 124
                    },
                    {
                        title: '3W-C系列',
                        id: 125
                    }
                ]
            },
            {
                title: '5w',
                id: 13,
                children: [
                    {
                        title: '5W系列',
                        id: 131
                    }
                ]
            }
        ]
    },
    {
        title: 'AC/DC(10-15W)隔离模块',
        id: 2,
        children: [
            {
                title: '10w',
                id: 21,
                children: [
                    {
                        title: '10W系列',
                        id: 211
                    }
                ]
            },
            {
                title: '15w',
                id: 22,
                children: [
                    {
                        title: '15W系列',
                        id: 221
                    }
                ]
            }
        ]
    },
    {
        title: 'AC/DC(20-60W)隔离模块',
        id: 3,
        children: [
            {
                title: '20w',
                id: 31,
                children: [
                    {
                        title: '20W系列',
                        id: 311
                    }
                ]
            },
            {
                title: '60w',
                id: 32,
                children: [
                    {
                        title: '60W系列',
                        id: 321
                    }
                ]
            }
        ]
    },
    {
        title: '机壳开关电源模块',
        id: 4,
        children: [
            {
                title: '35w',
                id: 41,
                children: [
                    {
                        title: '35W系列',
                        id: 411
                    }
                ]
            },
            {
                title: '350w',
                id: 42,
                children: [
                    {
                        title: '350W系列',
                        id: 421
                    }
                ]
            }
        ]
    },
    {
        title: 'DC/DC定电压隔离模块',
        id: 5,
        children: [
            {
                title: '1w',
                id: 51,
                children: [
                    {
                        title: '1W系列',
                        id: 511
                    }
                ]
            },
            {
                title: '2w',
                id: 52,
                children: [
                    {
                        title: '2W系列',
                        id: 521
                    }
                ]
            }
        ]
    },
    {
        title: 'DC/DC(3-15W)隔离模块',
        id: 6,
        children: [
            {
                title: '3w',
                id: 61,
                children: [
                    {
                        title: '3W系列',
                        id: 611
                    }
                ]
            },
            {
                title: '15w',
                id: 62,
                children: [
                    {
                        title: '15W系列',
                        id: 621
                    }
                ]
            }
        ]
    },
    {
        title: 'DC/DC(20-50W)隔离模块',
        id: 7,
        children: [
            {
                title: '20w',
                id: 71,
                children: [
                    {
                        title: '20W系列',
                        id: 711
                    }
                ]
            },
            {
                title: '50w',
                id: 72,
                children: [
                    {
                        title: '50W系列',
                        id: 721
                    }
                ]
            }
        ]
    },
    {
        title: 'DC/DC非隔离模块',
        id: 8,
        children: [
            {
                title: '宽电压',
                id: 81,
                children: [
                    {
                        title: '宽电压系列',
                        id: 811
                    }
                ]
            }
        ]
    }
];

// 初始化产品分类的函数
function initProductCategory() {
    // console.log('开始初始化产品分类');

    // 动态生成一级分类
    const onePageContainer = $('.productCategory_onePage');
    // console.log('查找容器:', onePageContainer.length > 0 ? '找到' : '未找到');

    if (onePageContainer.length === 0) {
        console.warn('找不到.productCategory_onePage容器，可能页面还未完全加载或该页面不包含此组件');
        return false;
    }

    // 检查容器是否可见
    if (!onePageContainer.is(':visible')) {
        // console.log('容器存在但不可见，这是正常的（悬停时才显示）');
    }

    onePageContainer.empty(); // 清空现有内容

    productCategory_onePageList.forEach(category => {
        const categoryDiv = $(`<div><span style="width: 80%;" class="textOver">${category.title}</span></div>`);
        onePageContainer.append(categoryDiv);
    });

    // console.log('分类初始化完成，生成了', productCategory_onePageList.length, '个分类项');
    return true;
}

// 更新悬浮页面内容
function updateHoverPagesContent(category) {
    // 检查悬浮页面容器是否存在
    const hoverPagesContainer = $('.productCategory_hoverPages');
    if (hoverPagesContainer.length === 0) {
        console.warn('找不到.productCategory_hoverPages容器');
        return;
    }

    // 在productCategory_onePageList中查找匹配的类别
    const matchedCategory = productCategory_onePageList.find(item => item.title === category);

    if (!matchedCategory) {
        // console.log('未找到匹配的类别:', category);
        // 显示默认内容或清空
        hoverPagesContainer.html('<div><div><h5>暂无相关分类信息</h5></div></div>');
        return;
    }

    // 按照注释中的结构构建悬浮页面内容
    let content = '<div>';

    // 如果有二级分类，按功率分组显示
    if (matchedCategory.children && matchedCategory.children.length > 0) {
        matchedCategory.children.forEach(powerCategory => {
            content += '<div>';

            // 二级分类标题（如：AC/DC电源模组 # 功率2W >）
            const categoryTitle = matchedCategory.title.replace('隔离模块', '电源模组');
            content += `<h5>${categoryTitle} # 功率${powerCategory.title.toUpperCase()} ></h5>`;

            // 三级分类标签容器
            content += '<div class="tag">';

            // 三级分类（如：2W系列）
            if (powerCategory.children && powerCategory.children.length > 0) {
                powerCategory.children.forEach(series => {
                    content += `<span>${series.title}</span>`;
                });
            } else {
                // 如果没有三级分类，显示功率系列
                content += `<span>${powerCategory.title}系列</span>`;
            }

            content += '</div>'; // 结束 .tag
            content += '</div>'; // 结束当前功率分组
        });
    } else {
        // 如果没有子分类，显示默认信息
        content += '<div>';
        content += `<h5>${matchedCategory.title}</h5>`;
        content += '<div class="tag">';
        content += '<span>暂无子分类</span>';
        content += '</div>';
        content += '</div>';
    }

    content += '</div>'; // 结束最外层div

    // 更新悬浮页面内容
    hoverPagesContainer.html(content);
    // console.log('已更新悬浮页面内容:', category);
}

// 处理产品分类悬停事件
$(document).ready(function() {
    // console.log('DOM加载完成，开始初始化产品分类');

    // 尝试初始化产品分类
    let initSuccess = initProductCategory();

    // 如果初始化失败，延迟重试（多次重试，因为头部组件可能还在加载）
    if (!initSuccess) {
        // console.log('初始化失败，开始重试...');
        let retryCount = 0;
        const maxRetries = 5;
        const retryInterval = setInterval(function() {
            retryCount++;
            console.log(`第${retryCount}次重试初始化产品分类`);
            let retrySuccess = initProductCategory();
            if (retrySuccess || retryCount >= maxRetries) {
                clearInterval(retryInterval);
                if (!retrySuccess) {
                    // console.log('重试仍然失败，可能当前页面不需要产品分类功能');
                }
            }
        }, 500);
    }

    // 使用事件委托，确保即使是动态添加的元素也能响应悬停事件
    $(document).on('mouseenter', '.productCategory_onePage > div', function() {
        // 获取当前悬停的产品类别文本
        const categoryText = $(this).find('span').text().trim();

        // 根据悬停的类别更新hoverPages内容
        updateHoverPagesContent(categoryText);

        // 高亮当前悬停项
        $(this).addClass('active').siblings().removeClass('active');
    });

    // 当鼠标离开整个分类区域时重置内容
    $(document).on('mouseleave', '.productCategory_onePage', function() {
        // 移除所有高亮
        $('.productCategory_onePage > div').removeClass('active');
    });
});

// 全局函数：手动重新初始化产品分类（用于调试或动态加载场景）
window.reinitProductCategory = function() {
    // console.log('手动重新初始化产品分类');
    return initProductCategory();
};

// 全局函数：检查产品分类容器状态
window.checkProductCategoryStatus = function() {
    const onePageContainer = $('.productCategory_onePage');
    const hoverPagesContainer = $('.productCategory_hoverPages');

    // console.log('=== 产品分类状态检查 ===');
    // console.log('一级分类容器:', onePageContainer.length > 0 ? '存在' : '不存在');
    // console.log('悬浮页面容器:', hoverPagesContainer.length > 0 ? '存在' : '不存在');

    if (onePageContainer.length > 0) {
        // console.log('一级分类容器可见性:', onePageContainer.is(':visible') ? '可见' : '隐藏');
        // console.log('一级分类子元素数量:', onePageContainer.children().length);
    }

    if (hoverPagesContainer.length > 0) {
        // console.log('悬浮页面容器可见性:', hoverPagesContainer.is(':visible') ? '可见' : '隐藏');
    }

    // console.log('产品分类数据长度:', productCategory_onePageList.length);
    // console.log('========================');

    return {
        onePageExists: onePageContainer.length > 0,
        hoverPagesExists: hoverPagesContainer.length > 0,
        onePageVisible: onePageContainer.is(':visible'),
        hoverPagesVisible: hoverPagesContainer.is(':visible'),
        childrenCount: onePageContainer.children().length,
        dataLength: productCategory_onePageList.length
    };
};
