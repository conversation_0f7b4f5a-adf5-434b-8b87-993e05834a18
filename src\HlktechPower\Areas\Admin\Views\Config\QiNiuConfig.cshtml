﻿@using Pek.Configs
@{
    // script
    PekHtml.AppendScriptParts(ResourceLocation.Head, "/static/plugins/js/layui/layui.js");

    // css
    PekHtml.AppendCssFileParts("/static/plugins/js/layui/css/layui.css");
}
<style asp-location="true">
    .layui-tab-title {
        width: 95%;
        margin: 0 auto;
    }

    .layui-tab.layui-tab-brief.Lan {
        box-shadow: 0 0 5px #AAA inset;
        box-shadow: 0 0 5px #AAA;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("站点设置")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("站点设置")</span></a></li>
                <li><a href="@Url.Action("Verified")"><span>实名认证配置</span></a></li>
                @*<li><a href="/index.php/admin/config/dump.html"><span>防灌水设置</span></a></li>
                    <li><a href="/index.php/admin/config/im.html"><span>站内IM设置</span></a></li>*@
                <li><a href="@Url.Action("Auto")"><span>@T("自动执行时间设置")</span></a></li>
                <li><a href="@Url.Action("QiNiuConfig")" class="current"><span>@T("七牛云设置")</span></a></li>
            </ul>
        </div>
    </div>
    @using (Html.BeginForm("UpdateQiNiu", "Config", FormMethod.Post, new { enctype = "multipart/form-data", name = "form1" }))
    {
        <div class="ncap-form-default">
            <dl>
                <dt>@T("授权密钥(AccessKey)")</dt>
                <dd>
                    <input id="DisplayName" name="AccessKey" value="@OssSetting.Current.QiNiu.AccessKey" class="input-txt" type="text">
                    <span class="err"></span>
                </dd>
            </dl>
            <dl>
                <dt>@T("密钥(SecretKey)")</dt>
                <dd>
                    <input id="CurDomainUrl" name="SecretKey" value="@OssSetting.Current.QiNiu.SecretKey" class="input-txt" type="text">
                    <span class="err"></span>
                </dd>
            </dl>
            <dl>
                <dt>@T("存储空间块(Bucket)")</dt>
                <dd>
                    <input id="companyname" name="Bucket" value="@OssSetting.Current.QiNiu.Bucket" class="input-txt" type="text">
                    <span class="err"></span>
                </dd>
            </dl>
            <dl>
                <dt>@T("绑定域名(Domain)")</dt>
                <dd>
                    <input id="sessiontimeout" name="Domain" value="@OssSetting.Current.QiNiu.Domain" class="input-txt" type="text">
                    <span class="err"></span>
                </dd>
            </dl>
            <dl>
                <dt>@T("基本路径(BasePath)")</dt>
                <dd>
                    <input id="refreshuserperiod" name="BasePath" value="@OssSetting.Current.QiNiu.BasePath" class="input-txt" type="text">
                    <span class="err"></span>
                </dd>
            </dl>
            <dl>
                <dt></dt>
                <dd><a href="JavaScript:void(0);" class="btn" onclick="document.form1.submit()">@T("确认提交")</a></dd>
            </dl>
        </div>
    }
</div>
<script asp-location="Footer">
    layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer,
            upload = layui.upload,
            layer = layui.layer,

            element = layui.element;
    })

    $(function () {

    })
</script>