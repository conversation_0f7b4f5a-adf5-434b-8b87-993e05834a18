﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechPower.Entity;

/// <summary>产品特点翻译表</summary>
[Serializable]
[DataObject]
[Description("产品特点翻译表")]
[BindIndex("IU_DH_ProductFeaturesLan_FId_LId", true, "FId,LId")]
[BindTable("DH_ProductFeaturesLan", Description = "产品特点翻译表", ConnName = "Pek", DbType = DatabaseType.None)]
public partial class ProductFeaturesLan : IProductFeaturesLan, IEntity<IProductFeaturesLan>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _FId;
    /// <summary>产品特点Id</summary>
    [DisplayName("产品特点Id")]
    [Description("产品特点Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("FId", "产品特点Id", "")]
    public Int32 FId { get => _FId; set { if (OnPropertyChanging("FId", value)) { _FId = value; OnPropertyChanged("FId"); } } }

    private Int32 _LId;
    /// <summary>所属语言Id</summary>
    [DisplayName("所属语言Id")]
    [Description("所属语言Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LId", "所属语言Id", "")]
    public Int32 LId { get => _LId; set { if (OnPropertyChanging("LId", value)) { _LId = value; OnPropertyChanged("LId"); } } }

    private String? _Name;
    /// <summary>名称</summary>
    [DisplayName("名称")]
    [Description("名称")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("Name", "名称", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductFeaturesLan model)
    {
        Id = model.Id;
        FId = model.FId;
        LId = model.LId;
        Name = model.Name;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "FId" => _FId,
            "LId" => _LId,
            "Name" => _Name,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "FId": _FId = value.ToInt(); break;
                case "LId": _LId = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static ProductFeaturesLan? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据产品特点Id、所属语言Id查找</summary>
    /// <param name="fId">产品特点Id</param>
    /// <param name="lId">所属语言Id</param>
    /// <returns>实体对象</returns>
    public static ProductFeaturesLan? FindByFIdAndLId(Int32 fId, Int32 lId)
    {
        if (fId < 0) return null;
        if (lId < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.FId == fId && e.LId == lId);

        return Find(_.FId == fId & _.LId == lId);
    }

    /// <summary>根据产品特点Id查找</summary>
    /// <param name="fId">产品特点Id</param>
    /// <returns>实体列表</returns>
    public static IList<ProductFeaturesLan> FindAllByFId(Int32 fId)
    {
        if (fId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.FId == fId);

        return FindAll(_.FId == fId);
    }
    #endregion

    #region 字段名
    /// <summary>取得产品特点翻译表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>产品特点Id</summary>
        public static readonly Field FId = FindByName("FId");

        /// <summary>所属语言Id</summary>
        public static readonly Field LId = FindByName("LId");

        /// <summary>名称</summary>
        public static readonly Field Name = FindByName("Name");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得产品特点翻译表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>产品特点Id</summary>
        public const String FId = "FId";

        /// <summary>所属语言Id</summary>
        public const String LId = "LId";

        /// <summary>名称</summary>
        public const String Name = "Name";
    }
    #endregion
}
