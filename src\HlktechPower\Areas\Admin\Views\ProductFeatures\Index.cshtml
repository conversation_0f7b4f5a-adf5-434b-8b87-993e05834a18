﻿@using HlktechPower.Dto
@using HlktechPower.Entity
@{
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("产品特点")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("产品特点")</span></a></li>
                <li><a href="@Url.Action("Add")"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>

    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("关键字")</dt>
                <dd><input type="text" value="@Model.Name" name="name" class="txt"></dd>
                <dt>@T("产品分类")</dt>
                <dd>
                    <select name="productClassId">
                        <option value="0">@T("请选择")</option>
                        @foreach (ProductClass item in Model.ProductClassList)
                        {
                            <option value="@item.Id">@item.Name</option>
                        }
                    </select>
                </dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")">
            </div>
        </div>
    </form>

    <table class="ds-default-table">
        <thead>
            <tr class="space">
                <th colspan="15" class="nobg">@T("列表")</th>
            </tr>
            <tr class="thead">
                <th class="w24">&nbsp;</th>
                <th>@T("名称")</th>
                <th>@T("产品类型")</th>
                <th>@T("状态")</th>
                <th>@T("创建时间")</th>
                <th class="align-center">@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (ProductDto item in Model.list)
            {
                <tr class="hover">
                    <td class="w24"></td>
                    <td>@item.Name</td>
                    <td>@item.ProductClassName</td>
                    <td>@(item.Enable ? "启用" : "禁用")</td>
                    <td>@item.CreateTime</td>
                    <td class="align-center">
                        @if (item.Enable)
                        {
                            <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("UpdateEnable")?Id=' + @item.Id + '','@T("您确定要禁用吗?")')" class="dsui-btn-edit"><i class="iconfont"></i>@T("禁用")</a>
                        }
                        else
                        {
                            <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("UpdateEnable")?Id=' + @item.Id + '','@T("您确定要启用吗?")')" class="dsui-btn-edit"><i class="iconfont"></i>@T("启用")</a>
                        }

                        <a href="@Url.Action("Update",new { Id=item.Id})" class="dsui-btn-edit"><i class="iconfont"></i>@T("编辑")</a>
                        <a href="javascript:;" onclick="javascript:dsLayerConfirm('@Url.Action("Delete")?Id=' + @item.Id + '','@T("您确定要删除吗?")')" class="dsui-btn-del"><i class="iconfont"></i>@T("删除")</a>
                    </td>
                </tr>
            }
        </tbody>
    </table>
    <ul class="pagination">@Html.Raw(Model.Str)</ul>
</div>