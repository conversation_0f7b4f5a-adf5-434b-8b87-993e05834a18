.h3 {
  margin-bottom: 10px;
}

.tip {
  padding-bottom: 1vw;
  border-bottom: 2px solid var(--line);
  margin-bottom: 1vw;
  color: var(--text-color3);
}

.layui-form {
  width: 80%;
  margin: 0 auto;
 
}
.layui-input{
  border-color: var(--line3)  ;
}

.layui-form-item {
  margin-bottom: 10px;
}

.layui-form-select dl {
  height: 150px;
}

.iconBox {
  display: flex;
  align-items: baseline;
  gap: 10px;

}


.icon {
  display: flex;
  align-content: center;
  width: 20px;
  height: 20px;
  margin-top: 8px;
  margin-left: 1vw;
  background-color: var(--myBlue);
  color:var(--white) ;
  border-radius: 50%;
}
.layui-icon-subtraction,
.layui-icon-addition {
  position: relative;
  left: 2px;
  top: 1px;
}

.layui-row {
  width: 20vw;
}

.layui-btn {
  margin-top: 1.5vw;
}
.remove-row {
  cursor: pointer;
}
.layui-input-affix .layui-icon{
  color: var(--white);
}
.problem{
  margin-bottom: 1vw;
}
.pic img{
  width: 36px;
}


.pic .layui-col-md5{
  margin-left: 1vw;
}