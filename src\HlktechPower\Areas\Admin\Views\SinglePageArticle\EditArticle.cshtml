﻿@using HlktechPower.Entity
@{
    var content = Model.Model.Content as String;
    content = content.SafeString().ToUnicodeString();
    var mobileContent = Model.Model.MobileContent as String;
    mobileContent = mobileContent.SafeString().ToUnicodeString();

    var localizationSettings = LocalizationSettings.Current;
}
<style asp-location="true">
    .type-file-preview {
        z-index: 99999
    }
</style>
<script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.all.min.js"></script>
<script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/lang/zh-cn/zh-cn.js"></script>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("单页文章管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("EditArticle",new { Id=Model.Model.Id})" class="current"><span>@T("修改")</span></a></li>
            </ul>
        </div>
    </div>
    <form id="goods_class_form" enctype="multipart/form-data" method="post">


        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准"):</li>
                    @foreach (var item in (IEnumerable<Language>)ViewBag.LanguageList)
                    {
                        <li data="@item.Id">@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <table class="ds-default-table">
                        <tbody>
                            <tr class="noborder">
                                <td class="required w120">@T("标题")</td>
                                <td class="vatop rowform"><input type="text" name="article_title" id="article_title" value="@Model.Model.Name" autocomplete="off" class="w200" /></td>
                                <td class="vatop tips"></td>
                            </tr>
                            <tr class="noborder">
                                <td class="required">@T("文章封面:") </td>
                                <td class="vatop rowform">

                                    @if (Model.Model.Pic != null)
                                    {
                                        <img class="show_image" src="/static/admin/images/preview.png">
                                        <div class="type-file-preview"><img src="@ViewBag.Images"></div>
                                    }

                                    <span class="type-file-box">
                                        <input type='text' name='textfield' id='textfield4' class='type-file-text' value="" />
                                        <input type='button' id="fileupload1" name="fileupload1" value='@T("上传")' class='type-file-button' />
                                        <input name="default_user_portrait" type="file" class="type-file-file" id="default_user_portrait" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                                    </span>
                                </td>
                                <td class="vatop tips">@T("图片限于png,gif,jpeg,jpg格式")</td>
                            </tr>
                            <tr class="noborder">
                                <td class="required">@T("文章内容")</td>
                                <td class="vatop rowform" colspan="2">
                                    <script type="text/javascript">
                                    var ue = UE.getEditor('article_content');
                                    ue.ready(function () {
                                        this.setContent('@Html.Raw(content)');
                                    })
                                    </script>
                                    <textarea name="article_content" id="article_content" value="" style="width:100%;"></textarea>
                                </td>
                            </tr>
                            <tr class="noborder">
                                <td class="required">@T("移动端文章内容")</td>
                                <td class="vatop rowform" colspan="2">
                                    <script type="text/javascript">
                                    var ueLan = UE.getEditor('article_mobileContent');
                                    ueLan.ready(function () {
                                        this.setContent('@Html.Raw(mobileContent)');
                                    })
                                    </script>
                                    <textarea name="article_mobileContent" id="article_mobileContent" value="" style="width:100%;"></textarea>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        var ModelLan = SingleArticleLan.FindBySIdAndLId((int)Model.Model.Id, item.Id);

                        var contenlan = ModelLan.Content as String;
                        contenlan = contenlan.SafeString().ToUnicodeString();
                        var Mobilecontenlan = ModelLan.MobileContent as String;
                        Mobilecontenlan = Mobilecontenlan.SafeString().ToUnicodeString();
                        <div class="layui-tab-item">
                            <table class="ds-default-table">
                                <tbody>
                                    <tr class="noborder">
                                        <td class="required w120">@T("标题")</td>
                                        <td class="vatop rowform"><input type="text" name="[@item.Id].article_title" id="[@item.Id].article_title" value="@ModelLan.Name" class="w830" autocomplete="off" /></td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="required">@T("文章封面:") </td>
                                        <td class="vatop rowform">
                                            @if (!ModelLan.Pic.IsNullOrWhiteSpace())
                                            {
                                                <img class="show_image" src="/static/admin/images/preview.png">
                                                <div class="type-file-preview"><img src="@ModelLan.Pic"></div>
                                            }
                                            <span class="type-file-box">
                                                <input type='text' name='[@item.Id].textfield' id='textfield@(item.Id)' class='type-file-text' />
                                                <input type='button' id="[@item.Id].fileuploads" name="[@item.Id].fileuploads" value='@T("上传")' class='type-file-button' />
                                                <input name="[@item.Id].default_user_portrait" type="file" class="type-file-file" id="[@item.Id].default_user_portrait" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                                            </span>
                                        </td>
                                        <td class="vatop tips">@T("图片限于png,gif,jpeg,jpg格式")</td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="required">@T("文章内容")</td>
                                        <td class="vatop rowform" colspan="2">
                                            <script type="text/javascript">
                                                    var ue@(item.Id) = UE.getEditor('<EMAIL>');
                                                    ue@(item.Id).ready(function () {
                                                        this.setContent('@Html.Raw(contenlan)');
                                                    })
                                            </script>
                                            <textarea name="<EMAIL>" id="<EMAIL>" style="width:100%;"></textarea>
                                        </td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="required">@T("移动端文章内容")</td>
                                        <td class="vatop rowform" colspan="2">
                                            <script type="text/javascript">
                                                    var ueLan@(item.Id) = UE.getEditor('<EMAIL>');
                                                    ueLan@(item.Id).ready(function () {
                                                        this.setContent('@Html.Raw(Mobilecontenlan)');
                                                    })
                                            </script>
                                            <textarea name="<EMAIL>" id="<EMAIL>" style="width:100%;"></textarea>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    }
                }
            </div>
        </div>

        <table class="ds-default-table">
            <tbody>
                @*<tr class="noborder">
                        <td class="required w120">@T("标题")</td>
                        <td class="vatop rowform"><input type="text" name="article_title" id="article_title" value="@Model.Model.Name" class="w200" /></td>
                        <td class="vatop tips"></td>
                    </tr>*@
                <tr class="noborder">
                    <td class="required w120">@T("链接")</td>
                    <td class="vatop rowform"><input type="text" name="article_url" id="article_url" value="@Model.Model.Url" class="w200" /></td>
                    <td class="vatop tips">@T("填写后直接跳转到该网址")</td>
                </tr>

                @*<tr class="noborder">
                        <td class="required">@T("文章封面:") </td>
                        <td class="vatop rowform">
                            <span class="type-file-show">
                                @if (Model.Model.Pic != null)
                                {
                                    <img class="show_image" src="/static/admin/images/preview.png">
                                    <div class="type-file-preview"><img src="@ViewBag.Images"></div>
                                }
                            </span>
                            <span class="type-file-box">
                                <input type='text' name='textfield' id='textfield4' class='type-file-text' value="" />
                                <input type='button' id="fileupload1" name="fileupload1" value='@T("上传")' class='type-file-button' />
                                <input name="default_user_portrait" type="file" class="type-file-file" id="default_user_portrait" size="30" hidefocus="true" ds_type="change_default_user_portrait">
                            </span>
                        </td>
                        <td class="vatop tips">@T("图片限于png,gif,jpeg,jpg格式")</td>
                    </tr>*@
                <tr class="noborder">
                    <td class="required w120">@T("调用别名")</td>
                    <td class="vatop rowform"><input type="text" name="article_code" id="article_code" value="@Model.Model.Code" class="w-100" /></td>
                    <td class="vatop tips">@T("别名访问,不可重复")</td>
                </tr>
                <tr class="noborder">
                    <td class="required w120">@T("文章是否显示")</td>
                    <td class="vatop rowform onoff">
                        @if (Model.Model.Show)
                        {
                            <label for="article_show1" class="cb-enable selected"><span>@T("是")</span></label>
                            <label for="article_show2" class="cb-disable "><span>@T("否")</span></label>
                            <input id="article_show1" name="article_show" checked="checked" value="1" type="radio">
                            <input id="article_show2" name="article_show" value="0" type="radio">
                        }
                        else
                        {
                            <label for="article_show1" class="cb-enable "><span>@T("是")</span></label>
                            <label for="article_show2" class="cb-disable selected"><span>@T("否")</span></label>
                            <input id="article_show1" name="article_show" value="1" type="radio">
                            <input id="article_show2" name="article_show" checked="checked" value="0" type="radio">
                        }
                    </td>
                    <td class="vatop tips"></td>
                </tr>
                <tr class="noborder">
                    <td class="required">@T("排序")</td>
                    <td class="vatop rowform"><input type="text" name="article_sort" id="article_sort" value="@Model.Model.Sort" class="w200" /></td>
                    <td class="vatop tips"></td>
                </tr>
                @*<tr class="noborder">
                        <td class="required">@T("文章内容")</td>
                        <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.config.js"></script>
                        <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.all.min.js"></script>
                        <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/lang/zh-cn/zh-cn.js"></script>
                        <script type="text/javascript">
                            var ue = UE.getEditor('article_content', {
                                toolbars: [[
                                    'fullscreen', 'source', '|', 'undo', 'redo', '|',
                                    'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',
                                    'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',
                                    'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',
                                    'directionalityltr', 'directionalityrtl', 'indent', '|',
                                    'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 'touppercase', 'tolowercase', '|',
                                    'link', 'unlink', 'anchor', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|',
                                    'emotion', 'map', 'gmap', 'insertcode', 'template', '|',
                                    'horizontal', 'date', 'time', 'spechars', '|',
                                    'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', 'charts', '|',
                                    'searchreplace', 'help', 'drafts', 'charts'
                                ]],
                            });

                                ue.ready(function () {
                                    this.setContent('@Html.Raw(Model.Model.Content)');
                                })

                        </script>
                        <td class="vatop rowform" colspan="2"><textarea name="article_content" id="article_content" value="" style="width:100%;"></textarea></td>
                    </tr>*@
                <tr>
                    <td class="required">@T("图片上传:")</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr class="noborder">
                    <td id="divComUploadContainer"><input type="file" multiple="multiple" id="fileupload" name="fileupload" /></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td class="required">@T("已传图片:")</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2">
                        <div class="tdare">
                            <table cellspacing="0" class="dataTable">
                                <tbody id="thumbnails">
                                    @foreach (var item in ViewBag.FileList as IList<UploadInfo>)
                                    {
                                        <tr id="@item.Id" class="tatr2">
                                            <input type="hidden" name="file_id[]" value="@item.Id" />
                                            <td><img width="40px" height="40px" src="@(Pek.Helpers.DHWeb.GetSiteUrl())/@(item.FileUrl)" /></td>
                                            <td>@item.FileName</td>
                                            <td><a href="javascript:insert_editor('@(Pek.Helpers.DHWeb.GetSiteUrl())/@(item.FileUrl)');">插入编辑器</a> | <a href="javascript:del_file_upload('@item.Id');">删除</a></td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="tfoot">
                    <td colspan="15"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>
<script src="/static/plugins/js/jquery-file-upload/jquery.fileupload.js"></script>
<script type="text/javascript" asp-location="Footer">

    layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer,
            upload = layui.upload,
            layer = layui.layer,
            element = layui.element;
    })
    var lid = "";

    $(function () {
        //$("#default_user_portrait").change(function () {
        //    $("#textfield4").val($("#default_user_portrait").val());
        //});

        $(".layui-tab-title").on("click", "li", function () {
            //debugger;
            console.log($(this).attr("data"));
            lid = $(this).attr("data");
        })


        $(".type-file-file").change(function () {
            $(this).parents(".layui-tab-item").find(".type-file-text").val($(this).parents(".layui-tab-item").find(".type-file-file").val())
        });

        // 图片上传
        $('#fileupload').each(function () {
            $(this).fileupload({
                dataType: 'json',
                url: "@Url.Action("UploadImg", new { Id = Model.Model.Id })",
                done: function (e, data) {
                    if (data != 'error') {
                        add_uploadedfile(data.result);
                    }
                }
            });
        });

        $('#article_form').validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().find('td:last'));
            },
            rules: {
                article_title: {
                    required: true,

                },
                article_url: {
                    url: true
                },
                article_sort: {
                    number: true,
                    range: [0, 255]
                },
                article_code: {
                    required: true,
                    remote: {
                        url: "@Url.Action("GetByCode")",
                        type: 'get',
                        data: {
                            code: function () {
                                return $('#article_code').val();
                            },
                            id: '@Model.Model.Id'
                        }
                    }
                }
            },
            messages: {
                article_title: {
                    required: '@T("标题名称不能为空")',
                },
                article_url: {
                    url: '@T("必须输入正确格式的网址")'
                },
                article_sort: {
                    number: '@T("排序只能为数字")',
                    range: '@T("数字范围为0~255，数字越小越靠前")'
                },
                article_code: {
                    required: '@T("调用别名不能为空")',
                    remote: '@T("调用别名已存在")'
                }
            }
        });

    });

    function add_uploadedfile(file_data) {
        var newImg = '<tr id="' + file_data.file_id + '" class="tatr2"><input type="hidden" name="file_id[]" value="' + file_data.file_id + '" /><td><img width="40px" height="40px" src="' + file_data.file_path + '" /></td><td>' + file_data.file_name + '</td><td><a href="javascript:insert_editor(\'' + file_data.file_path + '\');">插入编辑器</a> | <a href="javascript:del_file_upload(' + file_data.file_id + ');">删除</a></td></tr>';
        $('#thumbnails').prepend(newImg);
    }
    //function insert_editor(file_path) {
    //    ue.execCommand('insertimage', { src: file_path });
    //}

    function insert_editor(file_path) {
        if (!lid) {
            var ue = UE.getEditor('article_content');
            ue.execCommand('insertimage', { src: file_path });
        } else {
            var ue = UE.getEditor('article_content_' + lid);
            ue.execCommand('insertimage', { src: file_path });
        }
    }


    function del_file_upload(file_id) {
        layer.confirm('您确定要删除吗?', {
            btn: ['确定', '取消'],
            title: false,
        }, function () {
            $.getJSON("@Url.Action("DeleteImg")", { id: + file_id }, function (result) {
                if (result) {
                    $('#' + file_id).remove();
                    layer.close(layer.index);
                } else {
                    layer.alert('删除失败');
                }
            });
        });
    }

</script>