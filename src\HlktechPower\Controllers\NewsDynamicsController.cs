﻿using HlktechPower.Entity;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Pek;
using Pek.NCube;
using System.Collections.Generic;
using System.Dynamic;

namespace HlktechPower.Controllers
{
    /// <summary>
    /// 新闻动态
    /// </summary>
    public class NewsDynamicsController : PekBaseControllerX
    {
        /// <summary>
        /// 企业动态
        /// </summary>
        /// <returns></returns>
        public IActionResult Index(String key,Int32 page = 1,Int32 limit = 10)
        {
            dynamic viewModel = new ExpandoObject();

            var list = Article.FindAllByArticleClass(0).OrderBy(e => e.Sort).Select(e => new Article
            {
                Id = e.Id,
                Name = ArticleLan.FindByArticle(e, WorkingLanguage.Id).Name,
                Pic = ArticleLan.FindByArticle(e, WorkingLanguage.Id).Pic,
                Summary = ArticleLan.FindByArticle(e, WorkingLanguage.Id).Summary,
                CreateTime = e.CreateTime,
            }).WhereIf(!key.IsNullOrWhiteSpace(), e => e.Name != null && e.Name.Contains(key)).ToList();

            viewModel.key = key;
            viewModel.limit = limit;
            viewModel.page = page;
            viewModel.total = list.Count;
            viewModel.list = list.Skip((page - 1) * limit).Take(limit);
            return View(viewModel);
        }

        /// <summary>
        /// 产品动态
        /// </summary>
        /// <returns></returns>
        public IActionResult ProductDynamics(String key, Int32 page = 1, Int32 limit = 10)
        {
            dynamic viewModel = new ExpandoObject();

            var list = Article.FindAllByArticleClass(1).OrderBy(e => e.Sort).Select(e => new Article
            {
                Id = e.Id,
                Name = ArticleLan.FindByArticle(e, WorkingLanguage.Id).Name,
                Pic = ArticleLan.FindByArticle(e, WorkingLanguage.Id).Pic,
                Summary = ArticleLan.FindByArticle(e, WorkingLanguage.Id).Summary,
                CreateTime = e.CreateTime,
            }).WhereIf(!key.IsNullOrWhiteSpace(), e => e.Name != null && e.Name.Contains(key)).ToList();

            viewModel.key = key;
            viewModel.limit = limit;
            viewModel.page = page;
            viewModel.total = list.Count;
            viewModel.list = list.Skip((page - 1) * limit).Take(limit);
            return View(viewModel);
        }

        /// <summary>
        /// 技术应用
        /// </summary>
        /// <returns></returns>
        public IActionResult TechnologyApp(String key, Int32 page = 1, Int32 limit = 10)
        {
            dynamic viewModel = new ExpandoObject();

            var list = Article.FindAllByArticleClass(2).OrderBy(e => e.Sort).Select(e => new Article
            {
                Id = e.Id,
                Name = ArticleLan.FindByArticle(e, WorkingLanguage.Id).Name,
                Pic = ArticleLan.FindByArticle(e, WorkingLanguage.Id).Pic,
                Summary = ArticleLan.FindByArticle(e, WorkingLanguage.Id).Summary,
                CreateTime = e.CreateTime,
            }).WhereIf(!key.IsNullOrWhiteSpace(), e => e.Name != null && e.Name.Contains(key)).ToList();

            viewModel.key = key;
            viewModel.limit = limit;
            viewModel.page = page;
            viewModel.total = list.Count;
            viewModel.list = list.Skip((page - 1) * limit).Take(limit);
            return View(viewModel);
        }
    }
}
