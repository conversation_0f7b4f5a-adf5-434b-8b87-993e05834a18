.myContent-right {
  width: 80%;
}

.layui-tab-brief>.layui-tab-title .layui-this {
  color: var(--white);
  background-color: var(--myBlue);

}

.layui-tab .layui-tab-title li {
  margin: 0 1vw;
  border: 1px solid var(--line);
}


.layui-tab-title .layui-this:after {
  width: 0;
}

.layui-tab .layui-tab-title:after {
  bottom: -1vw;
  border-bottom-color: var(--myBlue);
  border-bottom-width: 2px;

}

.layui-tab-content {
  margin-top: 2vw;
}

.companyInfoBox {
  display: flex;
  justify-content: space-between;
  padding-bottom: 1vw;
  margin-bottom: 2vw;
  border-bottom: 1px solid var(--line);
}

.companyInfo {
  display: flex;
  flex-direction: column;
  width: 40%;
}

.companyInfo li {
  margin-bottom: .5vw;
  color: var(--text-color3);
  font-size: .9rem;
  height: 2.625rem;


}

.companyInfo .bold {
  font-size: 1.25rem;
  font-weight: 400;
  color: var(--text-color);
}

.hq {
  color: var(--myBlue);
}


.map-nav {
  margin: 0 auto;
  text-align: right;
  margin-bottom: 10px;
  box-sizing: border-box;
  max-width: 100%;
  display: flex;
  justify-content: flex-end;
}

.btn {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
  border: 1px solid var(--line);
  padding: 2px 5px;
  border-radius: 5px;
  position: relative;
}

.map-nav b {
  font-weight: normal;
}

.map-nav .caret {
  margin: 0 10px;
  display: inline-block;
  width: 0;
  height: 0;
  vertical-align: middle;
  border-top: 4px dashed;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
}

.dropdown-menu {
  text-align: left;
  display: none;
  position: absolute;
  background-color: #fff;
  padding: 5px 10px 10px;
}

.dropdown-menu a:hover {
  color: var(--myBlue);
}

.btn-group:hover .dropdown-menu {
  display: block;
}

.map-nav img {
  display: block;
  height: 24px;
}

#allmap {
  width: 100%;
  margin: 0 auto;
  height: 690px;
  margin-bottom: 2vw;
  z-index: -1;
  position: relative;
}

/* 不设置无法点击  body, */
/* html {
  width: 100%;
  height: 100%;
  margin: 0;
  font-family: "微软雅黑";
} */