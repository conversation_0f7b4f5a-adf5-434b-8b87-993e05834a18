﻿@using HlktechPower.Entity
@{
    var localizationSettings = LocalizationSettings.Current;
}
<style asp-location="true">
    .layui-tab-title {
        width: 95%;
        margin: 0 auto;
    }

    .layui-tab.layui-tab-brief.Lan {
        box-shadow: 0 0 5px #AAA inset;
        box-shadow: 0 0 5px #AAA;
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("产品隔离电压")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("产品隔离电压")</span></a></li>
                <li><a href="@Url.Action("Add")" class="current"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>

    @using (Html.BeginForm("Add", "ProductIsolationVoltage", FormMethod.Post, new { enctype = "multipart/form-data", name = "form1", id = "form1" }))
    {
        <div class="layui-tab layui-tab-brief Lan" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准"):</li>
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <li>@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="ncap-form-default">
                        <dl>
                            <dt>@T("名称")</dt>
                            <dd>
                                <input id="Name" name="Name" class="input-txt" type="text">
                            </dd>
                        </dl>
                    </div>
                </div>

                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {

                        <div class="layui-tab-item">
                            <div class="ncap-form-default">
                                <dl>
                                    <dt>@T("名称")</dt>
                                    <dd>
                                        <input name="[@item.Id].Name" class="input-txt" type="text">
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    }
                }
            </div>
        </div>
        <div class="ncap-form-default">
            <dl>
                <dt>@T("产品分类")</dt>
                <dd>
                    <select name="ProductClassId">
                        @foreach (ProductClass item in ViewBag.ProductClassList)
                        {
                            <option value="@item.Id">@item.Name</option>
                        }
                    </select>
                </dd>
            </dl>
            <dl>
                <dt></dt>
                <dd>
                    <a href="JavaScript:void(0);" class="btn" onclick="document.form1.submit()">@T("提交")</a>
                </dd>
            </dl>
        </div>
    }
</div>
<script asp-location="Footer">
    layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer,
            upload = layui.upload,
            layer = layui.layer,

            element = layui.element;
    })

    $(function () {
        $("#default_user_portrait").change(function () {
            $("#textfield4").val($("#default_user_portrait").val());
        });
    })
</script>
