﻿@{
    var localizationSettings = LocalizationSettings.Current;
}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("帮助分类")</h3>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="javascript:;" class="current"><span>@T("添加")</span></a></li>
            </ul>
        </div>
    </div>

    <form id="goods_class_form" enctype="multipart/form-data" method="post">
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">标准</li>
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <li data="@item.Id">@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content" style="height: 100px;">
                <div class="layui-tab-item layui-show">
                    <div class="layui-tab-item layui-show">
                        <table class="ds-default-table">
                            <tbody>
                                <tr class="noborder">
                                    <td colspan="2" class="required"><label class="validation" for="gc_name">@T("分类名称"):</label></td>
                                </tr>
                                <tr class="noborder">
                                    <td class="vatop rowform"><input type="text" value="" name="gc_name" id="gc_name" maxlength="20" class="txt"></td>
                                    <td class="vatop tips"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <div class="layui-tab-item">
                            <table class="ds-default-table">
                                <tbody>
                                    <tr class="noborder">
                                        <td colspan="2" class="required"><label class="validation" for="[@item.Id].gc_name">@T("分类名称"):</label></td>
                                    </tr>
                                    <tr class="noborder">
                                        <td class="vatop rowform"><input type="text" value="" name="[@item.Id].gc_name" id="[@item.Id].gc_name" maxlength="20" class="txt"></td>
                                        <td class="vatop tips"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    }
                }
            </div>
        </div>
        <table class="ds-default-table">
            <tbody>
                <tr>
                    <td colspan="2" class="required"><label for="parent_id">@T("上级分类"):</label></td>
                </tr>
                <tr class="noborder">
                    <td class="vatop rowform">
                        <select name="gc_parent_id" id="ParentCode">
                            <option value="">@T("请选择")...</option>
                            @foreach (var item in Model.Plist)
                            {
                                if (Model.ParentId == item.Id && Model.ParentId != 0)
                                {
                                    if (item.Level == 0)
                                    {
                                        <option value="@item.Id" selected>&nbsp;&nbsp; @item.Name</option>
                                    }
                                    else if (item.Level == 1)
                                    {
                                        <option value="@item.Id" selected>&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }
                                    else
                                    {
                                        <option value="@item.Id" selected>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }

                                }
                                else
                                {
                                    if (item.Level == 0)
                                    {
                                        <option value="@item.Id">&nbsp;&nbsp; @item.Name</option>
                                    }
                                    else if (item.Level == 1)
                                    {
                                        <option value="@item.Id">&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }
                                    else
                                    {
                                        <option value="@item.Id">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@item.Name</option>
                                    }
                                }
                            }
                        </select>
                    </td>
                    <td class="vatop tips">@T("如果选择上级分类，那么新增的分类则为被选择上级分类的子分类")</td>
                </tr>
                <tr>
                    <td colspan="2" class="required"><label class="validation" for="DisplayOrder">@T("排序"):</label></td>
                </tr>
                <tr class="noborder">
                    <td><input type="text" name="DisplayOrder" id="DisplayOrder" value="@ViewBag.DisplayOrder" class="w200"></td>
                    <td></td>
                </tr>
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="2"><input class="btn" type="submit" value="@T("提交")" /></td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
<script type="text/javascript" src="/static/plugins/mlselection.js" charset="utf-8"></script>
<script type="text/javascript" src="/static/plugins/jquery.mousewheel.js"></script>
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script asp-location="Footer">
    //按钮先执行验证再提交表单
    $(function () {
        $("#pic").change(function () {
            $("#textfield1").val($(this).val());
        });
        $('input[type="radio"][name="t_id"]').click(function () {
            if ($(this).val() == '0') {
                $('#t_name').val('');
            } else {
                $('#t_name').val($(this).next('span').html());
            }
        });

        $('#goods_class_form').validate({
            errorPlacement: function (error, element) {
                error.appendTo(element.parent().parent().prev().find('td:first'));
            },
            rules: {
                gc_name: {
                    required: true,
                    remote: {
                        url: "@Url.Action("GetByNames")",
                        type: 'get',
                        data: {
                            gc_name: function () {
                                return $('#gc_name').val();
                            },
                        }
                    }
                },
                DisplayOrder: {
                    required: true,
                    digits: true,
                }
            },
            messages: {
                gc_name: {
                    required: '@T("分类名称不能为空")',
                    remote: '@T("该分类名称已经存在了，请您换一个")'
                },
                 DisplayOrder: {
                    required: '@T("请填写排序")',
                    digits: '@T("请填写数字")',
                }
            }
        });
    });
</script>
