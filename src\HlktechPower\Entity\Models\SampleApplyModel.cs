﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechPower.Entity;

/// <summary>样品申请表</summary>
public partial class SampleApplyModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int64 Id { get; set; }

    /// <summary>项目名称</summary>
    public String? ProjectName { get; set; }

    /// <summary>预计月/年用量(个)</summary>
    public Int32 Dosage { get; set; }

    /// <summary>邮箱</summary>
    public String? Mail { get; set; }

    /// <summary>联系电话</summary>
    public String? Phone { get; set; }

    /// <summary>项目联系人</summary>
    public String? ContactPerson { get; set; }

    /// <summary>公司名称</summary>
    public String? CompanyName { get; set; }

    /// <summary>国家Id</summary>
    public Int32 CountryId { get; set; }

    /// <summary>省份Id</summary>
    public Int32 ProvinceId { get; set; }

    /// <summary>市级Id</summary>
    public Int32 CityId { get; set; }

    /// <summary>详细地址</summary>
    public String? Address { get; set; }

    /// <summary>申请主题</summary>
    public String? Theme { get; set; }

    /// <summary>需求描述</summary>
    public String? Description { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISampleApply model)
    {
        Id = model.Id;
        ProjectName = model.ProjectName;
        Dosage = model.Dosage;
        Mail = model.Mail;
        Phone = model.Phone;
        ContactPerson = model.ContactPerson;
        CompanyName = model.CompanyName;
        CountryId = model.CountryId;
        ProvinceId = model.ProvinceId;
        CityId = model.CityId;
        Address = model.Address;
        Theme = model.Theme;
        Description = model.Description;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
