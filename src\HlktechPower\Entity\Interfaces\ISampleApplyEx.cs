﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechPower.Entity;

/// <summary>样品申请拓展表</summary>
public partial interface ISampleApplyEx
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>样品申请编号</summary>
    Int64 SId { get; set; }

    /// <summary>产品分类编号1</summary>
    Int64 PId1 { get; set; }

    /// <summary>产品分类编号2</summary>
    Int64 PId2 { get; set; }

    /// <summary>产品分类编号3</summary>
    Int64 PId3 { get; set; }

    /// <summary>数量</summary>
    Int32 Number { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
