﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechPower.Entity;

/// <summary>产品封装翻译表</summary>
[Serializable]
[DataObject]
[Description("产品封装翻译表")]
[BindIndex("IU_DH_ProductPackagingLan_PId_LId", true, "PId,LId")]
[BindTable("DH_ProductPackagingLan", Description = "产品封装翻译表", ConnName = "Pek", DbType = DatabaseType.None)]
public partial class ProductPackagingLan : IProductPackagingLan, IEntity<IProductPackagingLan>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _PId;
    /// <summary>产品封装Id</summary>
    [DisplayName("产品封装Id")]
    [Description("产品封装Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("PId", "产品封装Id", "")]
    public Int32 PId { get => _PId; set { if (OnPropertyChanging("PId", value)) { _PId = value; OnPropertyChanged("PId"); } } }

    private Int32 _LId;
    /// <summary>所属语言Id</summary>
    [DisplayName("所属语言Id")]
    [Description("所属语言Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LId", "所属语言Id", "")]
    public Int32 LId { get => _LId; set { if (OnPropertyChanging("LId", value)) { _LId = value; OnPropertyChanged("LId"); } } }

    private String? _Name;
    /// <summary>封装形式</summary>
    [DisplayName("封装形式")]
    [Description("封装形式")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("Name", "封装形式", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductPackagingLan model)
    {
        Id = model.Id;
        PId = model.PId;
        LId = model.LId;
        Name = model.Name;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "PId" => _PId,
            "LId" => _LId,
            "Name" => _Name,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "PId": _PId = value.ToInt(); break;
                case "LId": _LId = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static ProductPackagingLan? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据产品封装Id、所属语言Id查找</summary>
    /// <param name="pId">产品封装Id</param>
    /// <param name="lId">所属语言Id</param>
    /// <returns>实体对象</returns>
    public static ProductPackagingLan? FindByPIdAndLId(Int32 pId, Int32 lId)
    {
        if (pId < 0) return null;
        if (lId < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.PId == pId && e.LId == lId);

        return Find(_.PId == pId & _.LId == lId);
    }

    /// <summary>根据产品封装Id查找</summary>
    /// <param name="pId">产品封装Id</param>
    /// <returns>实体列表</returns>
    public static IList<ProductPackagingLan> FindAllByPId(Int32 pId)
    {
        if (pId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.PId == pId);

        return FindAll(_.PId == pId);
    }
    #endregion

    #region 字段名
    /// <summary>取得产品封装翻译表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>产品封装Id</summary>
        public static readonly Field PId = FindByName("PId");

        /// <summary>所属语言Id</summary>
        public static readonly Field LId = FindByName("LId");

        /// <summary>封装形式</summary>
        public static readonly Field Name = FindByName("Name");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得产品封装翻译表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>产品封装Id</summary>
        public const String PId = "PId";

        /// <summary>所属语言Id</summary>
        public const String LId = "LId";

        /// <summary>封装形式</summary>
        public const String Name = "Name";
    }
    #endregion
}
