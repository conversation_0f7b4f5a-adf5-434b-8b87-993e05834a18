<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电源网</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <!-- 第三方库js -->
    <script src="../../modules/jq.js"></script>
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/media.css">
    <link rel="stylesheet" href="../../css/mySidebar.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="../../css/pageCss/videos.css">
    <!-- 综合-script -->
    <script src="../../script/index.js" defer></script>
    <!-- 产品分类模块 -->
    <script src="../../script/productCategory.js" defer></script>
    <!-- 所有页面组成部分 -->
    <script src="../../components/common.js" defer></script>
    <script src="../../components/mySidebar.js" defer></script>
    <!-- 单页面组成部分 -->
    <!-- <script src="./components/page.js" defer></script> -->
</head>

<body>
    <!-- 引入头部组件 -->
    <div id="header"></div>

    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="./providerDetail.html">应用支持</div>
        </div>
        <div class="myContent">
            <!-- 侧边栏 -->
            <div class="mySidebar"></div>
            <div class="myContent-right">
                <div class="search">
                    <div class="h3">应用视频</div>
                    <div class="layui-input-group">
                        <input type="text" placeholder="" class="layui-input">
                        <div class="layui-input-split layui-input-suffix">
                            <i class="iconfont icon-search"></i>&nbsp;<span class="wen">搜索</span>
                        </div>
                    </div>
                </div>
                <div class="videoBox">
                    <!--   下载功能
                        nodownload 即可隐藏
                        播放速度功能
                        noplaybackrate 即可隐藏
                        画中画功能
                        disablePictureInPicture=“true” -->
                    <div class="videoItem">
                        <video src="../../images/video/01.mp4" width="100%" height="180" controls
                            controlsList="nodownload noplaybackrate " disablePictureInPicture=“false”>
                        </video>
                        <div class="play-button"></div>
                        <div class="pause-button"></div>
                        <div class="title">【产品推荐】3W系列产品</div>
                        <div class="num">2365人观看</div>
                    </div>
                    <div class="videoItem">
                        <video src="../../images/video/02.mp4" width="100%" height="180" controls
                            controlsList="nodownload noplaybackrate " disablePictureInPicture=“false”>
                        </video>
                        <div class="play-button"></div>
                        <div class="pause-button"></div>
                        <div class="title">【产品推荐】3W系列产品</div>
                        <div class="num">2365人观看</div>
                    </div>
                    <div class="videoItem">
                        <video src="../../images/video/01.mp4" width="100%" height="180" controls
                            controlsList="nodownload noplaybackrate " disablePictureInPicture=“false”>
                        </video>
                        <div class="play-button"></div>
                        <div class="pause-button"></div>
                        <div class="title">【产品推荐】3W系列产品</div>
                        <div class="num">2365人观看</div>
                    </div>
                    <div class="videoItem">
                        <video src="../../images/video/01.mp4" width="100%" height="180" controls
                            controlsList="nodownload noplaybackrate " disablePictureInPicture=“false”>
                        </video>
                        <div class="play-button"></div>
                        <div class="pause-button"></div>
                        <div class="title">【产品推荐】3W系列产品</div>
                        <div class="num">2365人观看</div>
                    </div>
                </div>



            </div>


        </div>
    </div>

    <div class="bug"></div>

    <!-- 引入底部组件 -->
    <div id="footer"></div>
</body>
<script>
    const data = [
        { text: '应用支持', link: '#' },  // 标题
        { text: '焦点专题', link: '#' },
        { text: '资料下载', link: './downloads.html' },
        { text: '应用视频', link: './videos.html' },
        { text: '常见问题', link: './faq.html' },
        { text: '样品申请', link: './sample.html' },
        { text: '成品检测报告', link: './reports.html' }
    ];

    // 视频播放控制
    document.addEventListener('DOMContentLoaded', function () {
        const videoItems = document.querySelectorAll('.videoItem');

        // 暂停所有其他视频的函数
        function pauseAllOtherVideos(currentVideo) {
            videoItems.forEach(item => {
                const video = item.querySelector('video');
                const playButton = item.querySelector('.play-button');
                const pauseButton = item.querySelector('.pause-button');

                if (video !== currentVideo && !video.paused) {
                    video.pause();
                    playButton.style.display = 'flex';
                    pauseButton.style.display = 'none';
                    pauseButton.style.opacity = '0';
                    item.classList.remove('playing'); // 移除播放状态类
                }
            });
        }

        videoItems.forEach(item => {
            const video = item.querySelector('video');
            const playButton = item.querySelector('.play-button');
            const pauseButton = item.querySelector('.pause-button');
            let hoverTimeout;

            // 点击播放按钮播放视频
            playButton.addEventListener('click', function () {
                pauseAllOtherVideos(video); // 暂停其他视频
                video.play();
                playButton.style.display = 'none';
                item.classList.add('playing'); // 添加播放状态类
            });

            // 点击暂停按钮暂停视频
            pauseButton.addEventListener('click', function () {
                video.pause();
                playButton.style.display = 'flex';
                pauseButton.style.display = 'none';
                pauseButton.style.opacity = '0';
                item.classList.remove('playing'); // 移除播放状态类
            });

            // 点击视频暂停/播放
            video.addEventListener('click', function (e) {
                // console.log('video click event - paused:', video.paused, video);

                // 阻止默认行为和事件冒泡
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();

                // 添加延迟以避免事件冲突
                setTimeout(() => {
                    if (video.paused) {
                        pauseAllOtherVideos(video); // 暂停其他视频
                        video.play();
                        playButton.style.display = 'none';
                        item.classList.add('playing'); // 添加播放状态类
                    } else {
                        // console.log('Attempting to pause video...');
                        video.pause();
                        // 暂停后的UI更新现在由pause事件处理器处理
                    }
                }, 10);
            });





            // 监听视频开始播放事件
            video.addEventListener('play', function () {
                pauseAllOtherVideos(video); // 暂停其他视频
                item.classList.add('playing'); // 添加播放状态类
            });

            // 监听视频暂停事件
            video.addEventListener('pause', function () {
                playButton.style.display = 'flex';
                pauseButton.style.display = 'none';
                pauseButton.style.opacity = '0';
                item.classList.remove('playing'); // 移除播放状态类
            });

            // 鼠标悬停在视频上时显示暂停按钮（仅在播放时）
            item.addEventListener('mouseenter', function () {
                if (!video.paused) {
                    clearTimeout(hoverTimeout);
                    pauseButton.style.display = 'flex';
                    setTimeout(() => {
                        pauseButton.style.opacity = '1';
                    }, 10);
                }
            });

            // 鼠标离开时隐藏暂停按钮
            item.addEventListener('mouseleave', function () {
                pauseButton.style.opacity = '0';
                hoverTimeout = setTimeout(() => {
                    pauseButton.style.display = 'none';
                }, 300);
            });

            // 视频结束时显示播放按钮
            video.addEventListener('ended', function () {
                playButton.style.display = 'flex';
                pauseButton.style.display = 'none';
                pauseButton.style.opacity = '0';
                item.classList.remove('playing'); // 移除播放状态类
            });

        });
    });
</script>

</html>