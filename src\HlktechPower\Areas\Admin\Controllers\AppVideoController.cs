﻿using DH.Core.Domain.Localization;
using DH.Entity;
using HlktechPower.Entity;
using Microsoft.AspNetCore.Identity.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NewLife.Data;
using NewLife.Log;
using Pek;
using Pek.Configs;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Helpers;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.QiNiu.Extensions;
using Pek.Webs;
using System.ComponentModel;
using System.Dynamic;
using System.IO;
using XCode.Membership;

namespace HlktechPower.Areas.Admin.Controllers
{
    /// <summary>
    /// 应用视频
    /// </summary>
    [DisplayName("应用视频")]
    [Description("用于应用视频的管理")]
    [AdminArea]
    [DHMenu(49, ParentMenuName = "Product", ParentMenuDisplayName = "产品", ParentMenuUrl = "~/{area}/Product", ParentMenuOrder = 90, ParentVisible = true, CurrentMenuUrl = "~/{area}/AppVideo", CurrentMenuName = "AppVideo", CurrentIcon = "&#xe652;", LastUpdate = "20250603")]
    public class AppVideoController : PekCubeAdminControllerX
    {
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("列表")]
        public IActionResult Index(String name,Int32 page)
        {
            dynamic viewModel = new ExpandoObject();

            var list = AppVideo.FindAll().Select(e => new AppVideo
            {
                Id = e.Id,
                Show = e.Show,
                Name = AppVideoLan.FindByAppVideo(e, WorkingLanguage.Id).Name,
                Sort = e.Sort,
            }).OrderBy(e => e.Sort).WhereIf(!name.IsNullOrWhiteSpace(), e => e.Name != null && e.Name.Contains(name)).ToList();

            viewModel.list = list.Skip((page - 1) * 10).Take(10).ToList();

            viewModel.name = name;

            var pageCount = list.Count / 10;

            if (pageCount == 0) pageCount = 1;
            else if(list.Count % 10 > 0) pageCount++;

            viewModel.PageHtml = PageHelper.CreatePage(page, list.Count, pageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name } });

            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Insert)]
        [DisplayName("添加")]
        public IActionResult Add()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.listLanguage = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            return View(viewModel);
        }

        [HttpPost]
        public IActionResult Add([FromForm] String name, [FromForm] IFormFile claPic, [FromForm] Int32 sort)
        {
            if (name.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("标题不能为空"), IsOk = false });
            }
            if (claPic == null)
            {
                return Prompt(new PromptModel { Message = GetResource("视频不能为空"), IsOk = false });
            }
            var model = AppVideo.FindByName(name);
            if (model != null)
            {
                return Prompt(new PromptModel { Message = GetResource("标题已经存在"), IsOk = false });
            }
            if (OssSetting.Current.QiNiu.Domain.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("七牛云绑定域名未配置"), IsOk = false });
            }
            model = new AppVideo()
            {
                Name = name,
                Sort = sort,
                Show = true,
            };
            if (claPic != null)
            {
                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(claPic.FileName)}";
                byte[] fileBytes;
                using (var ms = new MemoryStream())
                {
                    claPic.CopyTo(ms);
                    fileBytes = ms.ToArray();
                }
                QiniuCloud.UploadData(filename, fileBytes);
                model.Url = Path.Combine(OssSetting.Current.QiNiu.Domain, OssSetting.Current.QiNiu.BasePath ?? "", filename).Replace("\\", "/");
            }
            model.Insert();
            var modelLocalizationSettings = LocalizationSettings.Current;
            if (modelLocalizationSettings.IsEnable)
            {
                var listLanguage = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                var filea = Request.Form.Files;
                foreach (var modelLanguage in listLanguage)
                {
                    var lan = new AppVideoLan()
                    {
                        AId = model.Id,
                        Name = (GetRequest($"[{modelLanguage.Id}].name")).SafeString().Trim(),
                        LId = modelLanguage.Id,
                    };
                    var file = filea.Where(e => e.Name == $"[{modelLanguage.Id}].claPic").FirstOrDefault();
                    if (file != null)
                    {
                        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
                        byte[] fileBytes;
                        using (var ms = new MemoryStream())
                        {
                            file.CopyTo(ms);
                            fileBytes = ms.ToArray();
                        }
                        QiniuCloud.UploadData(filename, fileBytes);
                        lan.Url = Path.Combine(OssSetting.Current.QiNiu.Domain, OssSetting.Current.QiNiu.BasePath ?? "", filename).Replace("\\", "/");
                    }
                    lan.Insert();
                }
            }
            return Prompt(new PromptModel { Message = GetResource("操作成功"), IsOk = true, BackUrl = Url.Action("Index") });
        }

        [EntityAuthorize(PermissionFlags.Update)]
        [DisplayName("编辑")]
        public IActionResult Update(Int32 Id)
        {
            dynamic viewModel = new ExpandoObject();
            var model = AppVideo.FindById(Id);
            if (model == null) return Content(GetResource("应用视频不存在"));
            viewModel.listLanguage = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            viewModel.Data = model;
            return View(viewModel);
        }

        [HttpPost]
        public IActionResult Update([FromForm] Int32 Id, [FromForm] String name, [FromForm] IFormFile claPic, [FromForm] Int32 dataFileClass, [FromForm] Int32 sort)
        {
            if (name.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("标题不能为空"), IsOk = false });
            }
            if (OssSetting.Current.QiNiu.Domain.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("七牛云绑定域名未配置"), IsOk = false });
            }
            var model = AppVideo.FindByName(name);
            if (model != null && Id != model.Id)
            {
                return Prompt(new PromptModel { Message = GetResource("标题已经存在"), IsOk = false });
            }
            var item = AppVideo.FindById(Id);
            if (item == null)
            {
                return Prompt(new PromptModel { Message = GetResource("应用视频不存在"), IsOk = false });
            }
            item.Name = name;
            item.Sort = sort;
            if (claPic != null)
            {
                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(claPic.FileName)}";
                byte[] fileBytes;
                using (var ms = new MemoryStream())
                {
                    claPic.CopyTo(ms);
                    fileBytes = ms.ToArray();
                }
                QiniuCloud.UploadData(filename, fileBytes);
                item.Url = Path.Combine(OssSetting.Current.QiNiu.Domain, OssSetting.Current.QiNiu.BasePath ?? "", filename).Replace("\\", "/");
            }
            item.Update();
            var modelLocalizationSettings = LocalizationSettings.Current;
            if (modelLocalizationSettings.IsEnable)
            {
                var listLanguage = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                var filea = Request.Form.Files;
                foreach (var modelLanguage in listLanguage)
                {
                    var lan = AppVideoLan.FindByAIdAndLId(item.Id, modelLanguage.Id);
                    if (lan == null)
                    {
                        lan = new AppVideoLan()
                        {
                            AId = item.Id,
                            Name = (GetRequest($"[{modelLanguage.Id}].name")).SafeString().Trim(),
                            LId = modelLanguage.Id,
                        };
                        var file = filea.Where(e => e.Name == $"[{modelLanguage.Id}].claPic").FirstOrDefault();
                        if (file != null)
                        {
                            var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
                            byte[] fileBytes;
                            using (var ms = new MemoryStream())
                            {
                                file.CopyTo(ms);
                                fileBytes = ms.ToArray();
                            }
                            QiniuCloud.UploadData(filename, fileBytes);
                            lan.Url = Path.Combine(OssSetting.Current.QiNiu.Domain, OssSetting.Current.QiNiu.BasePath ?? "", filename).Replace("\\", "/");
                        }
                        lan.Insert();
                    }
                    else
                    {
                        lan.Name = (GetRequest($"[{modelLanguage.Id}].name")).SafeString().Trim();
                        var file = filea.Where(e => e.Name == $"[{modelLanguage.Id}].claPic").FirstOrDefault();
                        if (file != null)
                        {
                            var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
                            byte[] fileBytes;
                            using (var ms = new MemoryStream())
                            {
                                file.CopyTo(ms);
                                fileBytes = ms.ToArray();
                            }
                            QiniuCloud.UploadData(filename, fileBytes);
                            lan.Url = Path.Combine(OssSetting.Current.QiNiu.Domain, OssSetting.Current.QiNiu.BasePath ?? "", filename).Replace("\\", "/");
                        }
                        lan.Update();
                    }
                }
            }
            return Prompt(new PromptModel { Message = GetResource("操作成功"), IsOk = true, BackUrl = Url.Action("Index") });
        }

        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("删除")]
        public IActionResult Delete(String Ids)
        {
            var list = AppVideo.FindAll(AppVideo._.Id.In(Ids.Trim(',')));
            foreach (var item in list)
            {
                if (!item.Url.IsNullOrWhiteSpace())
                {
                    QiniuCloud.Delete(item.Url);
                }            
                item.Delete();
                var lanlist = AppVideoLan.FindAllByAId(item.Id);
                foreach (var lan in lanlist)
                {
                    if (!lan.Url.IsNullOrWhiteSpace())
                    {
                        QiniuCloud.Delete(lan.Url);
                    }
                    lan.Delete();
                }
            }
            return Json(new { success = true });
        }

        /// <summary>
        /// 修改显示状态
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult UpdateShow(Int32 Id)
        {
            var model = AppVideo.FindById(Id);
            if (model == null) return Json(new { success = false, msg = GetResource("应用视频不存在") });
            model.Show = !model.Show;
            model.Update();
            return Json(new { success = true });
        }
    }
}
