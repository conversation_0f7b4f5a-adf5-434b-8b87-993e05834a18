﻿@using HlktechPower.Entity

<head>
    <!-- 本页私有css -->
    <link rel="stylesheet" href="~/css/pageCss/faq.css">
    <link rel="stylesheet" href="~/css/mySidebar.css">
    <!-- 所有页面组成部分 -->
    <script src="~/components/mySidebar.js" defer></script>
</head>

<body style="background-color:white">
    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="/">@T("首页")</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="#">@T("应用支持")</div>
        </div>
        <div class="myContent">
            <!-- 侧边栏 -->
            <div class="mySidebar"></div>
            <div class="myContent-right">
                <div class="search">
                    <div class="h3">@T("常见问题")</div>
                    <div class="layui-input-group">
                        <input type="text" placeholder="" class="layui-input" value="@Model.key" id="searchKey">
                        <div class="layui-input-split layui-input-suffix">
                            <i class="iconfont icon-search"></i>&nbsp;<span class="wen" id="sousuo">@T("搜索")</span>
                        </div>
                    </div>
                </div>
                <div class="layui-tab layui-tab-brief">
                    <ul class="layui-tab-title">
                        @foreach (ProductClass item in Model.list)
                        {
                            if (Model.pId > 0)
                            {
                                if (item.Id == Model.pId)
                                {
                                    <li class="layui-this" id="@item.Id">@item.Name</li>
                                }
                                else
                                {
                                    <li id="@item.Id">@item.Name</li>
                                }
                            }
                            else
                            {
                                if (item == Model.list[0])
                                {
                                    <li class="layui-this" id="@item.Id">@item.Name</li>
                                }
                                else
                                {
                                    <li id="@item.Id">@item.Name</li>
                                }
                            }
                        }
                    </ul>
                    <div class="layui-tab-content layui-tab-content2">
                        @foreach (ProductClass item in Model.list)
                        {
                            if (Model.pId > 0)
                            {
                                if (item.Id == Model.pId)
                                {
                                    <div class="layui-tab-item layui-show">
                                        <ul class="item2">
                                            @foreach (Problem problem in item.Problems)
                                            {
                                                <li>
                                                    <div class="li-left">
                                                        <img src="~/images/power/chanjianwenti.png" alt="">
                                                        <span>@problem.Content</span>
                                                    </div>
                                                    @if (!problem.Answer.IsNullOrWhiteSpace())
                                                    {
                                                        <div class="li-right">
                                                            <span>@T("解答"):</span> @problem.Answer
                                                        </div>
                                                    }
                                                </li>
                                            }
                                        </ul>
                                    </div>
                                }
                                else
                                {
                                    <div class="layui-tab-item">
                                        <ul class="item2">
                                            @foreach (Problem problem in item.Problems)
                                            {
                                                <li>
                                                    <div class="li-left">
                                                        <img src="~/images/power/chanjianwenti.png" alt="">
                                                        <span>@problem.Content</span>
                                                    </div>
                                                    @if (!problem.Answer.IsNullOrWhiteSpace())
                                                    {
                                                        <div class="li-right">
                                                            <span>@T("解答"):</span> @problem.Answer
                                                        </div>
                                                    }
                                                </li>
                                            }
                                        </ul>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="layui-tab-item @(item == Model.list[0] ? "layui-show" : "")">
                                    <ul class="item2">
                                        @foreach (Problem problem in item.Problems)
                                        {
                                            <li>
                                                <div class="li-left">
                                                    <img src="~/images/power/chanjianwenti.png" alt="">
                                                    <span>@problem.Content</span>
                                                </div>
                                                @if (!problem.Answer.IsNullOrWhiteSpace())
                                                {
                                                    <div class="li-right">
                                                        <span>@T("解答"):</span> @problem.Answer
                                                    </div>
                                                }
                                            </li>
                                        }
                                    </ul>
                                </div>
                            }
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bug"></div>
</body>
<script>
    const data = [
        { text: '@T("应用支持")', link: '#' },  // 标题
        { text: '@T("焦点专题")', link: '@Url.Action("Index")' },
        { text: '@T("资料下载")', link: '@Url.Action("DataDownload")' },
        { text: '@T("应用视频")', link: '@Url.Action("AppVideo")' },
        { text: '@T("常见问题")', link: '@Url.Action("CommonProblem")' },
        { text: '@T("样品申请")', link: '@Url.Action("SampleApply")' },
        { text: '@T("成品检测报告")', link: '@Url.Action("ProductInspectionReport")' }
    ];

</script>
<script asp-location="Footer">
    $("#sousuo").click(function () {
        // 获取当前选中tab的id
        var selectedId = $('.layui-tab-title li.layui-this').attr('id');
        //console.log(selectedId)
        var key = $("#searchKey").val();
        window.location.href = '@Url.Action("CommonProblem")?key=' + key + '&pId=' + selectedId + '';
    });
</script>