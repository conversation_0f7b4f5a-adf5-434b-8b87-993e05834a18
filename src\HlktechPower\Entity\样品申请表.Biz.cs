﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using DH.Entity;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace HlktechPower.Entity;

public partial class SampleApply : DHEntityBase<SampleApply>
{
    #region 对象操作
    static SampleApply()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(Dosage));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化SampleApply[样品申请表]数据……");

    //    var entity = new SampleApply();
    //    entity.Id = 0;
    //    entity.ProjectName = "abc";
    //    entity.Dosage = 0;
    //    entity.Mail = "abc";
    //    entity.Phone = "abc";
    //    entity.ContactPerson = "abc";
    //    entity.CompanyName = "abc";
    //    entity.CountryId = 0;
    //    entity.ProvinceId = 0;
    //    entity.CityId = 0;
    //    entity.Address = "abc";
    //    entity.Theme = "abc";
    //    entity.Description = "abc";
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化SampleApply[样品申请表]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性

    public IList<SampleApplyEx>? listSampleApplyEx { get; set; }

    [XmlIgnore, ScriptIgnore, IgnoreDataMember]
    public Country? Country => Extends.Get(nameof(Country), k => Country.FindById(CountryId));

    [XmlIgnore, ScriptIgnore, IgnoreDataMember]
    public Regions? Province => Extends.Get(nameof(Province), k => Regions.FindById(ProvinceId));

    [XmlIgnore, ScriptIgnore, IgnoreDataMember]
    public Regions? City => Extends.Get(nameof(City), k => Regions.FindById(CityId));

    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="projectName">项目名称</param>
    /// <param name="contactPerson">项目联系人</param>
    /// <param name="companyName">公司名称</param>
    /// <param name="countryId">国家Id</param>
    /// <param name="start">编号开始</param>
    /// <param name="end">编号结束</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<SampleApply> Search(String? projectName, String? contactPerson, String? companyName, Int32 countryId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (!projectName.IsNullOrEmpty()) exp &= _.ProjectName == projectName;
        if (!contactPerson.IsNullOrEmpty()) exp &= _.ContactPerson == contactPerson;
        if (!companyName.IsNullOrEmpty()) exp &= _.CompanyName == companyName;
        if (countryId >= 0) exp &= _.CountryId == countryId;
        exp &= _.Id.Between(start, end, Meta.Factory.Snow);
        if (!key.IsNullOrEmpty()) exp &= SearchWhereByKeys(key);

        return FindAll(exp, page);
    }

    // Select Count(Id) as Id,ProjectName From DH_SampleApply Where CreateTime>'2020-01-24 00:00:00' Group By ProjectName Order By Id Desc limit 20
    static readonly FieldCache<SampleApply> _ProjectNameCache = new(nameof(ProjectName))
    {
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    };

    /// <summary>获取项目名称列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    /// <returns></returns>
    public static IDictionary<String, String> GetProjectNameList() => _ProjectNameCache.FindAllName();

    // Select Count(Id) as Id,CompanyName From DH_SampleApply Where CreateTime>'2020-01-24 00:00:00' Group By CompanyName Order By Id Desc limit 20
    static readonly FieldCache<SampleApply> _CompanyNameCache = new(nameof(CompanyName))
    {
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    };

    /// <summary>获取公司名称列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    /// <returns></returns>
    public static IDictionary<String, String> GetCompanyNameList() => _CompanyNameCache.FindAllName();

    // Select Count(Id) as Id,ContactPerson From DH_SampleApply Where CreateTime>'2020-01-24 00:00:00' Group By ContactPerson Order By Id Desc limit 20
    static readonly FieldCache<SampleApply> _ContactPersonCache = new(nameof(ContactPerson))
    {
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    };

    /// <summary>获取项目联系人列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    /// <returns></returns>
    public static IDictionary<String, String> GetContactPersonList() => _ContactPersonCache.FindAllName();
    #endregion

    #region 业务操作
    public ISampleApply ToModel()
    {
        var model = new SampleApply();
        model.Copy(this);

        return model;
    }

    #endregion
}
