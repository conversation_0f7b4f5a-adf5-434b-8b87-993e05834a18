﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechPower.Entity;

/// <summary>应用视频翻译表</summary>
[Serializable]
[DataObject]
[Description("应用视频翻译表")]
[BindIndex("IU_DH_AppVideoLan_AId_LId", true, "AId,LId")]
[BindIndex("IX_DH_AppVideoLan_Name", false, "Name")]
[BindTable("DH_AppVideoLan", Description = "应用视频翻译表", ConnName = "Pek", DbType = DatabaseType.None)]
public partial class AppVideoLan : IAppVideoLan, IEntity<IAppVideoLan>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _AId;
    /// <summary>应用视频Id</summary>
    [DisplayName("应用视频Id")]
    [Description("应用视频Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("AId", "应用视频Id", "")]
    public Int32 AId { get => _AId; set { if (OnPropertyChanging("AId", value)) { _AId = value; OnPropertyChanged("AId"); } } }

    private Int32 _LId;
    /// <summary>所属语言Id</summary>
    [DisplayName("所属语言Id")]
    [Description("所属语言Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LId", "所属语言Id", "")]
    public Int32 LId { get => _LId; set { if (OnPropertyChanging("LId", value)) { _LId = value; OnPropertyChanged("LId"); } } }

    private String? _Name;
    /// <summary>标题</summary>
    [DisplayName("标题")]
    [Description("标题")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("Name", "标题", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private String? _Url;
    /// <summary>链接</summary>
    [DisplayName("链接")]
    [Description("链接")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("Url", "链接", "")]
    public String? Url { get => _Url; set { if (OnPropertyChanging("Url", value)) { _Url = value; OnPropertyChanged("Url"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IAppVideoLan model)
    {
        Id = model.Id;
        AId = model.AId;
        LId = model.LId;
        Name = model.Name;
        Url = model.Url;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "AId" => _AId,
            "LId" => _LId,
            "Name" => _Name,
            "Url" => _Url,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "AId": _AId = value.ToInt(); break;
                case "LId": _LId = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "Url": _Url = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static AppVideoLan? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据应用视频Id、所属语言Id查找</summary>
    /// <param name="aId">应用视频Id</param>
    /// <param name="lId">所属语言Id</param>
    /// <returns>实体对象</returns>
    public static AppVideoLan? FindByAIdAndLId(Int32 aId, Int32 lId)
    {
        if (aId < 0) return null;
        if (lId < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.AId == aId && e.LId == lId);

        return Find(_.AId == aId & _.LId == lId);
    }

    /// <summary>根据应用视频Id查找</summary>
    /// <param name="aId">应用视频Id</param>
    /// <returns>实体列表</returns>
    public static IList<AppVideoLan> FindAllByAId(Int32 aId)
    {
        if (aId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.AId == aId);

        return FindAll(_.AId == aId);
    }

    /// <summary>根据标题查找</summary>
    /// <param name="name">标题</param>
    /// <returns>实体列表</returns>
    public static IList<AppVideoLan> FindAllByName(String? name)
    {
        if (name == null) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Name.EqualIgnoreCase(name));

        return FindAll(_.Name == name);
    }
    #endregion

    #region 字段名
    /// <summary>取得应用视频翻译表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>应用视频Id</summary>
        public static readonly Field AId = FindByName("AId");

        /// <summary>所属语言Id</summary>
        public static readonly Field LId = FindByName("LId");

        /// <summary>标题</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>链接</summary>
        public static readonly Field Url = FindByName("Url");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得应用视频翻译表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>应用视频Id</summary>
        public const String AId = "AId";

        /// <summary>所属语言Id</summary>
        public const String LId = "LId";

        /// <summary>标题</summary>
        public const String Name = "Name";

        /// <summary>链接</summary>
        public const String Url = "Url";
    }
    #endregion
}
